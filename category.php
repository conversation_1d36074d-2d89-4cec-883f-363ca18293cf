<?php
session_start();
require_once 'includes/functions.php';

// الحصول على slug التصنيف
$slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';

if (empty($slug)) {
    header("Location: index.php");
    exit();
}

// الحصول على التصنيف
$category = getCategory($slug);

if (!$category) {
    header("HTTP/1.0 404 Not Found");
    $page_title = 'التصنيف غير موجود';
    include 'includes/header.php';
    echo '<div class="container mx-auto px-4 py-16 text-center">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">404 - التصنيف غير موجود</h1>
            <p class="text-gray-600 mb-8">عذراً، التصنيف المطلوب غير موجود.</p>
            <a href="index.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                العودة إلى الرئيسية
            </a>
          </div>';
    include 'includes/footer.php';
    exit();
}

// إعداد الصفحة
$page_title = $category['name'];
$page_description = $category['description'];

// الحصول على رقم الصفحة
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = (int)getSetting('articles_per_page', 12);

// الحصول على المقالات
$articles = getArticles($page, $per_page, $category['id']);
$total_articles = getTotalArticles($category['id']);
$total_pages = ceil($total_articles / $per_page);

include 'includes/header.php';
?>

<main class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-8">
        <ol class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
            <li><a href="index.php" class="hover:text-blue-600">الرئيسية</a></li>
            <li><i class="fas fa-chevron-left mx-2"></i></li>
            <li class="text-gray-700"><?php echo $category['name']; ?></li>
        </ol>
    </nav>

    <!-- Category Header -->
    <header class="mb-8">
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8">
            <h1 class="text-3xl md:text-4xl font-bold mb-4">
                <i class="fas fa-folder-open ml-3"></i>
                <?php echo $category['name']; ?>
            </h1>
            <?php if ($category['description']): ?>
                <p class="text-blue-100 text-lg"><?php echo $category['description']; ?></p>
            <?php endif; ?>
            <div class="mt-4 text-blue-100">
                <i class="fas fa-newspaper ml-2"></i>
                <?php echo number_format($total_articles); ?> مقال
            </div>
        </div>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-3">
            <?php if (!empty($articles)): ?>
                <!-- Articles Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    <?php foreach ($articles as $article): ?>
                    <article class="bg-white rounded-lg shadow-md overflow-hidden news-card">
                        <div class="aspect-w-16 aspect-h-9">
                            <?php if ($article['image_url']): ?>
                                <img src="<?php echo $article['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($article['title']); ?>"
                                     class="w-full h-48 object-cover">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                    <i class="fas fa-newspaper text-4xl text-gray-400"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                                    <?php echo $category['name']; ?>
                                </span>
                                <span class="text-xs text-gray-500">
                                    <i class="fas fa-clock ml-1"></i>
                                    <?php echo formatDate($article['published_at'], 'H:i'); ?>
                                </span>
                            </div>
                            <h3 class="font-bold text-lg mb-2 leading-tight">
                                <a href="article.php?slug=<?php echo $article['slug']; ?>" 
                                   class="hover:text-blue-600 transition-colors">
                                    <?php echo $article['title']; ?>
                                </a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-3 line-clamp-3">
                                <?php echo $article['excerpt']; ?>
                            </p>
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <span>
                                    <i class="fas fa-eye ml-1"></i>
                                    <?php echo number_format($article['views']); ?>
                                </span>
                                <span>
                                    <?php echo formatArabicDate($article['published_at']); ?>
                                </span>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php echo generatePagination($page, $total_pages, "category.php?slug={$category['slug']}"); ?>
                
            <?php else: ?>
                <div class="text-center py-16">
                    <i class="fas fa-folder-open text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد مقالات في هذا التصنيف</h3>
                    <p class="text-gray-500 mb-8">سيتم إضافة المقالات قريباً</p>
                    <a href="index.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        تصفح جميع الأخبار
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Other Categories -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-list ml-2 text-blue-600"></i>
                    تصنيفات أخرى
                </h3>
                <div class="space-y-2">
                    <?php 
                    $all_categories = getCategories();
                    foreach ($all_categories as $cat): 
                        if ($cat['id'] != $category['id']):
                    ?>
                    <a href="category.php?slug=<?php echo $cat['slug']; ?>" 
                       class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-700"><?php echo $cat['name']; ?></span>
                        <i class="fas fa-chevron-left text-gray-400"></i>
                    </a>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </div>
            </div>

            <!-- Latest News -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-clock ml-2 text-blue-600"></i>
                    أحدث الأخبار
                </h3>
                <div class="space-y-4">
                    <?php 
                    $latest_articles = getLatestArticles(6);
                    foreach ($latest_articles as $latest): 
                    ?>
                    <article class="flex space-x-3 space-x-reverse">
                        <div class="w-16 h-12 flex-shrink-0">
                            <?php if ($latest['image_url']): ?>
                                <img src="<?php echo $latest['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($latest['title']); ?>"
                                     class="w-full h-full object-cover rounded">
                            <?php else: ?>
                                <div class="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sm leading-tight mb-1">
                                <a href="article.php?slug=<?php echo $latest['slug']; ?>" 
                                   class="hover:text-blue-600 transition-colors">
                                    <?php echo mb_substr($latest['title'], 0, 60) . (mb_strlen($latest['title']) > 60 ? '...' : ''); ?>
                                </a>
                            </h4>
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-clock ml-1"></i>
                                <?php echo formatDate($latest['published_at'], 'H:i'); ?>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Featured Articles -->
            <?php 
            $featured_articles = getFeaturedArticles(4);
            if (!empty($featured_articles)):
            ?>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-star ml-2 text-yellow-500"></i>
                    أخبار مميزة
                </h3>
                <div class="space-y-4">
                    <?php foreach ($featured_articles as $featured): ?>
                    <article class="flex space-x-3 space-x-reverse">
                        <div class="w-16 h-12 flex-shrink-0">
                            <?php if ($featured['image_url']): ?>
                                <img src="<?php echo $featured['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($featured['title']); ?>"
                                     class="w-full h-full object-cover rounded">
                            <?php else: ?>
                                <div class="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sm leading-tight mb-1">
                                <a href="article.php?slug=<?php echo $featured['slug']; ?>" 
                                   class="hover:text-blue-600 transition-colors">
                                    <?php echo mb_substr($featured['title'], 0, 60) . (mb_strlen($featured['title']) > 60 ? '...' : ''); ?>
                                </a>
                            </h4>
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-star ml-1 text-yellow-500"></i>
                                مميز
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</main>

<?php include 'includes/footer.php'; ?>
