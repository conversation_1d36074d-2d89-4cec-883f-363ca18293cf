<?php
/**
 * Minimal Test File - Tests basic functionality step by step
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Minimal Test</title></head><body>";
echo "<h1>Minimal PHP Test</h1>";

// Test 1: Basic PHP
echo "<h2>Test 1: Basic PHP</h2>";
echo "<p style='color: green;'>✅ PHP is working - Version: " . PHP_VERSION . "</p>";

// Test 2: File system
echo "<h2>Test 2: File System</h2>";
$test_files = ['config/database.php', 'includes/functions.php', 'config/config.php'];
foreach ($test_files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $file missing</p>";
    }
}

// Test 3: Include config (safe)
echo "<h2>Test 3: Include Config</h2>";
try {
    if (file_exists('config/config.php')) {
        // Define ROOT_PATH before including config
        if (!defined('ROOT_PATH')) {
            define('ROOT_PATH', __DIR__);
        }
        
        ob_start();
        include_once 'config/config.php';
        $output = ob_get_clean();
        
        echo "<p style='color: green;'>✅ Config included successfully</p>";
        if (!empty($output)) {
            echo "<p>Output: " . htmlspecialchars($output) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Config file not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Config error: " . $e->getMessage() . "</p>";
}

// Test 4: Include database (safe)
echo "<h2>Test 4: Include Database</h2>";
try {
    if (file_exists('config/database.php')) {
        ob_start();
        include_once 'config/database.php';
        $output = ob_get_clean();
        
        echo "<p style='color: green;'>✅ Database config included</p>";
        if (!empty($output)) {
            echo "<p>Output: " . htmlspecialchars($output) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Database config not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database config error: " . $e->getMessage() . "</p>";
}

// Test 5: Database connection
echo "<h2>Test 5: Database Connection</h2>";
try {
    if (class_exists('Database')) {
        $database = new Database();
        $db = $database->connect();
        
        if ($db) {
            echo "<p style='color: green;'>✅ Database connected successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Database connection failed</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Database class not available (config not loaded)</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection error: " . $e->getMessage() . "</p>";
}

// Test 6: Include functions (safe)
echo "<h2>Test 6: Include Functions</h2>";
try {
    if (file_exists('includes/functions.php')) {
        ob_start();
        include_once 'includes/functions.php';
        $output = ob_get_clean();
        
        echo "<p style='color: green;'>✅ Functions included</p>";
        if (!empty($output)) {
            echo "<p>Output: " . htmlspecialchars($output) . "</p>";
        }
        
        // Test a function if available
        if (function_exists('getSetting')) {
            echo "<p style='color: green;'>✅ getSetting function available</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Functions file not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Functions error: " . $e->getMessage() . "</p>";
}

// Test 7: Session
echo "<h2>Test 7: Session</h2>";
try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "<p style='color: green;'>✅ Session started successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Session error: " . $e->getMessage() . "</p>";
}

// Test 8: Required extensions
echo "<h2>Test 8: Required Extensions</h2>";
$extensions = ['pdo', 'pdo_mysql', 'simplexml', 'curl', 'mbstring'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✅ $ext loaded</p>";
    } else {
        echo "<p style='color: red;'>❌ $ext missing</p>";
    }
}

echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<p><a href='debug-500.php'>🔧 Run Full Diagnostic</a></p>";
echo "<p><a href='setup.php'>⚙️ Database Setup</a></p>";
echo "<p><a href='index.php'>🏠 Try Homepage</a></p>";

echo "</body></html>";
?>
