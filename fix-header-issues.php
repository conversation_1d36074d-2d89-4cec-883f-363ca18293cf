<?php
/**
 * إصلاح مشاكل الهيدر والتكرار
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشاكل الهيدر</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo ".code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; border-left: 4px solid #007bff; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح مشاكل الهيدر والتكرار</h1>";

$fixes_applied = [];
$errors = [];

// 1. إنشاء ملف header.php جديد ومحسن
echo "<div class='section info'>";
echo "<h2>1. إصلاح ملف header.php</h2>";

// إنشاء نسخة احتياطية
if (file_exists('includes/header.php')) {
    copy('includes/header.php', 'includes/header.php.backup.' . date('Y-m-d-H-i-s'));
    echo "<p>✅ تم إنشاء نسخة احتياطية من header.php</p>";
}

// إنشاء header.php جديد ومحسن
$new_header_content = '<?php
/**
 * Header Template - محسن ومنظم
 * يحتوي فقط على عناصر الهيدر بدون HTML الأساسي
 */

// التأكد من تحميل الدوال المطلوبة
if (!function_exists(\'getSetting\')) {
    require_once \'functions.php\';
}

// الحصول على التصنيفات للتنقل
try {
    $categories = getCategories();
} catch (Exception $e) {
    $categories = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . \' - \' : \'\'; ?><?php echo getSetting(\'site_name\', \'موقع الأخبار\'); ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : getSetting(\'site_description\', \'موقع إخباري شامل\'); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Navigation Enhancements CSS -->
    <link rel="stylesheet" href="assets/css/navigation-enhancements.css">
    
    <!-- Homepage Enhancements CSS -->
    <?php if (basename($_SERVER[\'PHP_SELF\']) == \'index.php\'): ?>
    <link rel="stylesheet" href="assets/css/homepage-enhancements.css">
    <?php endif; ?>
    
    <!-- Custom Styles -->
    <style>
        body {
            font-family: \'Tajawal\', Arial, sans-serif;
        }
        
        .sticky-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .sticky-header.scrolled {
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="sticky-header sticky top-0 z-50 shadow-lg">
        <div class="container mx-auto px-4">
            <!-- Top Bar -->
            <div class="border-b border-gray-200 py-2">
                <div class="flex justify-between items-center text-sm text-gray-600">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <span class="flex items-center">
                            <i class="fas fa-calendar-alt ml-1"></i>
                            <?php echo date(\'Y/m/d\'); ?>
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-clock ml-1"></i>
                            <span id="current-time"><?php echo date(\'H:i\'); ?></span>
                        </span>
                    </div>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <!-- Social Media Links -->
                        <a href="#" class="text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-blue-400 hover:text-blue-600 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-red-600 hover:text-red-800 transition-colors">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="text-pink-600 hover:text-pink-800 transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Header -->
            <div class="py-4">
                <div class="flex justify-between items-center">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="index.php" class="flex items-center">
                            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3 rounded-lg ml-3">
                                <i class="fas fa-newspaper text-2xl"></i>
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-800">
                                    <?php echo getSetting(\'site_name\', \'موقع الأخبار\'); ?>
                                </h1>
                                <p class="text-sm text-gray-600">
                                    <?php echo getSetting(\'site_description\', \'موقع إخباري شامل\'); ?>
                                </p>
                            </div>
                        </a>
                    </div>
                    
                    <!-- Search Box -->
                    <div class="hidden md:flex flex-1 max-w-md mx-8">
                        <form action="search.php" method="GET" class="w-full">
                            <div class="relative">
                                <input type="text" 
                                       name="q" 
                                       placeholder="البحث في الأخبار..." 
                                       class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button type="submit" 
                                        class="absolute left-0 top-0 h-full px-3 text-gray-400 hover:text-blue-600 transition-colors">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button class="mobile-nav-toggle md:hidden text-gray-600 hover:text-gray-800">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="main-navigation border-t border-gray-200">
                <div class="py-3">
                    <ul class="nav-menu hidden md:flex space-x-8 space-x-reverse">
                        <li class="nav-item">
                            <a href="index.php" class="nav-link">
                                <i class="fas fa-home ml-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="matches.php" class="nav-link">
                                <i class="fas fa-futbol ml-1"></i>مواعيد المباريات
                            </a>
                        </li>
                        <?php if (!empty($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 5) as $category): ?>
                            <li class="nav-item">
                                <a href="category.php?slug=<?php echo $category[\'slug\']; ?>" class="nav-link">
                                    <i class="fas fa-tag ml-1"></i><?php echo $category[\'name\']; ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a href="search.php" class="nav-link">
                                <i class="fas fa-search ml-1"></i>البحث
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content Container -->
    <main class="min-h-screen">
';

// حفظ الملف الجديد
if (file_put_contents('includes/header.php', $new_header_content)) {
    $fixes_applied[] = "إنشاء ملف header.php محسن";
    echo "<p style='color: green;'>✅ تم إنشاء ملف header.php جديد ومحسن</p>";
} else {
    $errors[] = "فشل في إنشاء ملف header.php الجديد";
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف header.php الجديد</p>";
}
echo "</div>";

// 2. إنشاء ملف footer.php منفصل
echo "<div class='section info'>";
echo "<h2>2. إنشاء ملف footer.php محسن</h2>";

$new_footer_content = '    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-16">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- About Section -->
                <div>
                    <h3 class="text-xl font-bold mb-4">عن الموقع</h3>
                    <p class="text-gray-300 leading-relaxed">
                        <?php echo getSetting(\'site_description\', \'موقع إخباري شامل يقدم أحدث الأخبار والمعلومات\'); ?>
                    </p>
                    <div class="flex space-x-4 space-x-reverse mt-4">
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            <i class="fab fa-facebook-f text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            <i class="fab fa-youtube text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-4">روابط سريعة</h3>
                    <ul class="space-y-2">
                        <li><a href="index.php" class="text-gray-300 hover:text-white transition-colors">الرئيسية</a></li>
                        <li><a href="matches.php" class="text-gray-300 hover:text-white transition-colors">المباريات</a></li>
                        <li><a href="search.php" class="text-gray-300 hover:text-white transition-colors">البحث</a></li>
                        <li><a href="admin/login.php" class="text-gray-300 hover:text-white transition-colors">لوحة التحكم</a></li>
                    </ul>
                </div>
                
                <!-- Categories -->
                <div>
                    <h3 class="text-xl font-bold mb-4">التصنيفات</h3>
                    <ul class="space-y-2">
                        <?php if (!empty($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 6) as $category): ?>
                            <li>
                                <a href="category.php?slug=<?php echo $category[\'slug\']; ?>" 
                                   class="text-gray-300 hover:text-white transition-colors">
                                    <?php echo $category[\'name\']; ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">معلومات الاتصال</h3>
                    <div class="space-y-3 text-gray-300">
                        <div class="flex items-center">
                            <i class="fas fa-envelope ml-2"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-phone ml-2"></i>
                            <span>+966 12 345 6789</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt ml-2"></i>
                            <span>الرياض، المملكة العربية السعودية</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Copyright -->
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; <?php echo date(\'Y\'); ?> <?php echo getSetting(\'site_name\', \'موقع الأخبار\'); ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" aria-label="العودة للأعلى">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- JavaScript Files -->
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString(\'ar-SA\', {
                hour: \'2-digit\',
                minute: \'2-digit\',
                hour12: false
            });
            const timeElement = document.getElementById(\'current-time\');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }
        
        // Update time every minute
        setInterval(updateTime, 60000);
        
        // Header scroll effect
        window.addEventListener(\'scroll\', function() {
            const header = document.querySelector(\'.sticky-header\');
            if (window.scrollY > 100) {
                header.classList.add(\'scrolled\');
            } else {
                header.classList.remove(\'scrolled\');
            }
        });
    </script>
    
    <!-- Navigation Enhancements JavaScript -->
    <script src="assets/js/navigation-enhancements.js"></script>
    
    <!-- Homepage Enhancements JavaScript -->
    <?php if (basename($_SERVER[\'PHP_SELF\']) == \'index.php\'): ?>
    <script src="assets/js/homepage-enhancements.js"></script>
    <?php endif; ?>

</body>
</html>';

// حفظ ملف footer.php
if (file_put_contents('includes/footer.php', $new_footer_content)) {
    $fixes_applied[] = "إنشاء ملف footer.php محسن";
    echo "<p style='color: green;'>✅ تم إنشاء ملف footer.php جديد ومحسن</p>";
} else {
    $errors[] = "فشل في إنشاء ملف footer.php";
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف footer.php</p>";
}
echo "</div>";

// 3. تحديث الصفحات لاستخدام الهيدر والفوتر الجديد
echo "<div class='section info'>";
echo "<h2>3. تحديث الصفحات الرئيسية</h2>";

$pages_to_update = [
    'index.php' => 'الصفحة الرئيسية',
    'matches.php' => 'صفحة المباريات'
];

foreach ($pages_to_update as $page => $description) {
    if (file_exists($page)) {
        $page_content = file_get_contents($page);
        
        // إنشاء نسخة احتياطية
        copy($page, $page . '.backup.' . date('Y-m-d-H-i-s'));
        
        // إزالة استدعاءات الهيدر المكررة
        $page_content = preg_replace('/include.*header\.php.*\n?/i', '', $page_content);
        
        // إزالة استدعاءات الفوتر المكررة
        $page_content = preg_replace('/include.*footer\.php.*\n?/i', '', $page_content);
        
        // إزالة عناصر HTML الأساسية إذا كانت موجودة
        $page_content = preg_replace('/<!DOCTYPE.*\n?/i', '', $page_content);
        $page_content = preg_replace('/<html.*\n?/i', '', $page_content);
        $page_content = preg_replace('/<head.*\n?/i', '', $page_content);
        $page_content = preg_replace('/<\/head.*\n?/i', '', $page_content);
        $page_content = preg_replace('/<body.*\n?/i', '', $page_content);
        $page_content = preg_replace('/<\/body.*\n?/i', '', $page_content);
        $page_content = preg_replace('/<\/html.*\n?/i', '', $page_content);
        
        // إضافة استدعاء الهيدر في البداية
        $page_content = "<?php\ninclude 'includes/header.php';\n?>\n\n" . $page_content;
        
        // إضافة استدعاء الفوتر في النهاية
        $page_content = $page_content . "\n\n<?php include 'includes/footer.php'; ?>";
        
        // حفظ الملف المحدث
        if (file_put_contents($page, $page_content)) {
            $fixes_applied[] = "تحديث $description";
            echo "<p style='color: green;'>✅ تم تحديث $description</p>";
        } else {
            $errors[] = "فشل في تحديث $description";
            echo "<p style='color: red;'>❌ فشل في تحديث $description</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ الملف $page غير موجود</p>";
    }
}
echo "</div>";

// 4. ملخص الإصلاحات
echo "<div class='section success'>";
echo "<h2>4. ملخص الإصلاحات المطبقة</h2>";

if (!empty($fixes_applied)) {
    echo "<h3>الإصلاحات المطبقة:</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li style='color: green;'>✅ $fix</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: blue;'>ℹ️ لم يتم تطبيق إصلاحات جديدة</p>";
}

if (!empty($errors)) {
    echo "<h3>الأخطاء:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>❌ $error</li>";
    }
    echo "</ul>";
}

echo "<h3>التحسينات المطبقة:</h3>";
echo "<ul>";
echo "<li>🔧 <strong>فصل الهيدر والفوتر:</strong> إنشاء ملفات منفصلة ومحسنة</li>";
echo "<li>🧹 <strong>إزالة التكرار:</strong> إزالة عناصر HTML المكررة</li>";
echo "<li>⚡ <strong>تحسين الأداء:</strong> تنظيم الكود وتحسين التحميل</li>";
echo "<li>📱 <strong>تحسين التصميم:</strong> تحسين التصميم المتجاوب</li>";
echo "<li>🔍 <strong>تحسين SEO:</strong> تحسين عناصر الـ meta</li>";
echo "</ul>";
echo "</div>";

// 5. اختبار النتائج
echo "<div class='section info'>";
echo "<h2>5. اختبار النتائج</h2>";
echo "<div style='text-align: center;'>";
echo "<a href='index.php' target='_blank' class='btn btn-primary'>اختبار الصفحة الرئيسية</a>";
echo "<a href='matches.php' target='_blank' class='btn btn-success'>اختبار صفحة المباريات</a>";
echo "<a href='header-diagnosis.php' target='_blank' class='btn btn-warning'>إعادة التشخيص</a>";
echo "<a href='test-scroll-effects.php' target='_blank' class='btn btn-danger'>اختبار تأثيرات التمرير</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
