<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

session_start();

$page_title = 'اختبار شريط الأخبار العاجلة';

// إضافة بعض المقالات التجريبية إذا لم توجد
$database = new Database();
$db = $database->connect();

// التحقق من وجود مقالات
$stmt = $db->query("SELECT COUNT(*) FROM articles");
$articles_count = $stmt->fetchColumn();

if ($articles_count < 5) {
    // إضافة مقالات تجريبية
    $sample_articles = [
        [
            'title' => 'عاجل: تطورات مهمة في الأحداث الجارية',
            'content' => 'هذا مقال تجريبي لاختبار شريط الأخبار العاجلة. يحتوي على محتوى تجريبي لعرض كيفية عمل النظام.',
            'excerpt' => 'تطورات مهمة في الأحداث الجارية',
            'is_featured' => 1
        ],
        [
            'title' => 'أخبار عاجلة: إعلان مهم من الحكومة',
            'content' => 'مقال تجريبي آخر لاختبار شريط الأخبار العاجلة مع محتوى مختلف.',
            'excerpt' => 'إعلان مهم من الحكومة',
            'is_featured' => 1
        ],
        [
            'title' => 'تحديث فوري: أحداث اقتصادية مهمة',
            'content' => 'مقال تجريبي ثالث لضمان وجود محتوى كافي في شريط الأخبار.',
            'excerpt' => 'أحداث اقتصادية مهمة',
            'is_featured' => 1
        ],
        [
            'title' => 'عاجل: تطورات تقنية جديدة',
            'content' => 'مقال تجريبي رابع حول التطورات التقنية الجديدة.',
            'excerpt' => 'تطورات تقنية جديدة',
            'is_featured' => 1
        ],
        [
            'title' => 'أخبار عاجلة: أحداث رياضية مهمة',
            'content' => 'مقال تجريبي خامس حول الأحداث الرياضية المهمة.',
            'excerpt' => 'أحداث رياضية مهمة',
            'is_featured' => 0
        ]
    ];
    
    foreach ($sample_articles as $article) {
        $slug = createSlug($article['title']);
        
        $stmt = $db->prepare("
            INSERT IGNORE INTO articles (title, slug, content, excerpt, is_featured, published_at, author) 
            VALUES (?, ?, ?, ?, ?, NOW(), 'نظام الاختبار')
        ");
        $stmt->execute([
            $article['title'], 
            $slug, 
            $article['content'], 
            $article['excerpt'], 
            $article['is_featured']
        ]);
    }
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
            <i class="fas fa-broadcast-tower ml-2 text-red-600"></i>
            اختبار شريط الأخبار العاجلة
        </h1>

        <div class="space-y-6">
            <!-- معلومات الشريط -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-blue-800">
                    <i class="fas fa-info-circle ml-2"></i>
                    معلومات شريط الأخبار العاجلة
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">الميزات المتاحة:</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-check text-green-600 ml-2"></i>حركة سلسة من اليمين إلى اليسار</li>
                            <li><i class="fas fa-check text-green-600 ml-2"></i>إيقاف/تشغيل الحركة</li>
                            <li><i class="fas fa-check text-green-600 ml-2"></i>إيقاف مؤقت عند hover</li>
                            <li><i class="fas fa-check text-green-600 ml-2"></i>إغلاق الشريط</li>
                            <li><i class="fas fa-check text-green-600 ml-2"></i>تصميم متجاوب</li>
                            <li><i class="fas fa-check text-green-600 ml-2"></i>تحديث تلقائي كل 5 دقائق</li>
                        </ul>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">التحكم:</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-pause text-blue-600 ml-2"></i>زر الإيقاف/التشغيل</li>
                            <li><i class="fas fa-times text-red-600 ml-2"></i>زر الإغلاق</li>
                            <li><i class="fas fa-mouse text-gray-600 ml-2"></i>إيقاف عند hover على النص</li>
                            <li><i class="fas fa-mobile-alt text-purple-600 ml-2"></i>متوافق مع الأجهزة المحمولة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- إحصائيات -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-800">
                    <i class="fas fa-chart-bar ml-2"></i>
                    إحصائيات المحتوى
                </h2>
                
                <?php
                // جلب إحصائيات
                $featured_count = count(getFeaturedArticles(10));
                $latest_count = count(getLatestArticles(10));
                $total_articles = $db->query("SELECT COUNT(*) FROM articles")->fetchColumn();
                ?>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600"><?php echo $featured_count; ?></div>
                        <div class="text-sm text-gray-600">أخبار مميزة</div>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600"><?php echo $latest_count; ?></div>
                        <div class="text-sm text-gray-600">أحدث الأخبار</div>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-purple-600"><?php echo $total_articles; ?></div>
                        <div class="text-sm text-gray-600">إجمالي المقالات</div>
                    </div>
                </div>
            </div>

            <!-- اختبار الوظائف -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-yellow-800">
                    <i class="fas fa-vial ml-2"></i>
                    اختبار الوظائف
                </h2>
                
                <div class="space-y-4">
                    <div class="flex flex-wrap gap-4">
                        <button onclick="testPausePlay()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-play-pause ml-2"></i>اختبار الإيقاف/التشغيل
                        </button>
                        
                        <button onclick="testHover()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-mouse ml-2"></i>اختبار Hover
                        </button>
                        
                        <button onclick="testResponsive()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-mobile-alt ml-2"></i>اختبار التجاوب
                        </button>
                        
                        <button onclick="refreshContent()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                            <i class="fas fa-sync-alt ml-2"></i>تحديث المحتوى
                        </button>
                    </div>
                    
                    <div id="test-results" class="bg-white p-4 rounded-lg border hidden">
                        <h3 class="font-semibold text-gray-800 mb-2">نتائج الاختبار:</h3>
                        <div id="test-output" class="text-sm text-gray-600"></div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">
                    <i class="fas fa-link ml-2"></i>
                    روابط سريعة
                </h2>
                
                <div class="flex flex-wrap gap-4">
                    <a href="index.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-home ml-2"></i>الصفحة الرئيسية
                    </a>
                    
                    <a href="admin/articles.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-edit ml-2"></i>إدارة المقالات
                    </a>
                    
                    <a href="admin/dashboard.php" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-tachometer-alt ml-2"></i>لوحة التحكم
                    </a>
                    
                    <a href="test-complete.php" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                        <i class="fas fa-vial ml-2"></i>اختبار شامل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testPausePlay() {
    const pauseBtn = document.getElementById('ticker-pause');
    const output = document.getElementById('test-output');
    const results = document.getElementById('test-results');
    
    if (pauseBtn) {
        pauseBtn.click();
        output.innerHTML = '<span class="text-green-600">✓ تم اختبار زر الإيقاف/التشغيل بنجاح</span>';
        results.classList.remove('hidden');
    } else {
        output.innerHTML = '<span class="text-red-600">✗ لم يتم العثور على زر الإيقاف/التشغيل</span>';
        results.classList.remove('hidden');
    }
}

function testHover() {
    const marquee = document.getElementById('news-marquee');
    const output = document.getElementById('test-output');
    const results = document.getElementById('test-results');
    
    if (marquee) {
        // محاكاة hover
        marquee.dispatchEvent(new Event('mouseenter'));
        setTimeout(() => {
            marquee.dispatchEvent(new Event('mouseleave'));
        }, 2000);
        
        output.innerHTML = '<span class="text-green-600">✓ تم اختبار تأثير Hover بنجاح</span>';
        results.classList.remove('hidden');
    } else {
        output.innerHTML = '<span class="text-red-600">✗ لم يتم العثور على عنصر الشريط</span>';
        results.classList.remove('hidden');
    }
}

function testResponsive() {
    const ticker = document.getElementById('breaking-news-ticker');
    const output = document.getElementById('test-output');
    const results = document.getElementById('test-results');
    
    if (ticker) {
        const width = ticker.offsetWidth;
        output.innerHTML = `<span class="text-green-600">✓ عرض الشريط الحالي: ${width}px - التصميم متجاوب</span>`;
        results.classList.remove('hidden');
    } else {
        output.innerHTML = '<span class="text-red-600">✗ لم يتم العثور على شريط الأخبار</span>';
        results.classList.remove('hidden');
    }
}

function refreshContent() {
    const output = document.getElementById('test-output');
    const results = document.getElementById('test-results');
    
    output.innerHTML = '<span class="text-blue-600">⏳ جاري تحديث المحتوى...</span>';
    results.classList.remove('hidden');
    
    // محاكاة تحديث المحتوى
    setTimeout(() => {
        output.innerHTML = '<span class="text-green-600">✓ تم تحديث المحتوى بنجاح (محاكاة)</span>';
    }, 2000);
}
</script>

<?php include 'includes/footer.php'; ?>
