<?php
/**
 * إضافة مباريات تجريبية لاختبار سلايدر المباريات
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// بدء الجلسة
session_start();

$success_messages = [];
$error_messages = [];

try {
    $database = new Database();
    $db = $database->connect();

    // التحقق من وجود جدول المباريات
    $stmt = $db->query("SHOW TABLES LIKE 'matches'");
    if (!$stmt->fetch()) {
        throw new Exception('جدول المباريات غير موجود. يرجى تشغيل setup.php أولاً.');
    }

    // إضافة مباريات تجريبية للسلايدر
    $sample_matches = [
        [
            'home_team' => 'النصر',
            'away_team' => 'الأهلي السعودي',
            'competition' => 'الدوري السعودي للمحترفين',
            'match_date' => date('Y-m-d H:i:s', strtotime('+1 day +2 hours')),
            'venue' => 'ملعب الملك فهد الدولي',
            'status' => 'scheduled',
            'is_featured' => 1
        ],
        [
            'home_team' => 'الهلال',
            'away_team' => 'الاتحاد',
            'competition' => 'كأس الملك',
            'match_date' => date('Y-m-d H:i:s', strtotime('+2 days +3 hours')),
            'venue' => 'ملعب الأمير فيصل بن فهد',
            'status' => 'scheduled',
            'is_featured' => 1
        ],
        [
            'home_team' => 'الزمالك',
            'away_team' => 'الأهلي المصري',
            'competition' => 'الدوري المصري',
            'match_date' => date('Y-m-d H:i:s', strtotime('+4 days +4 hours')),
            'venue' => 'ستاد القاهرة الدولي',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'مانشستر سيتي',
            'away_team' => 'ليفربول',
            'competition' => 'الدوري الإنجليزي الممتاز',
            'match_date' => date('Y-m-d H:i:s', strtotime('+6 days +5 hours')),
            'venue' => 'ملعب الاتحاد',
            'status' => 'scheduled',
            'is_featured' => 1
        ],
        [
            'home_team' => 'برشلونة',
            'away_team' => 'ريال مدريد',
            'competition' => 'دوري أبطال أوروبا',
            'match_date' => date('Y-m-d H:i:s', strtotime('+9 days +6 hours')),
            'venue' => 'كامب نو',
            'status' => 'scheduled',
            'is_featured' => 1
        ],
        [
            'home_team' => 'باريس سان جيرمان',
            'away_team' => 'مارسيليا',
            'competition' => 'الدوري الفرنسي',
            'match_date' => date('Y-m-d H:i:s', strtotime('+11 days +7 hours')),
            'venue' => 'حديقة الأمراء',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'يوفنتوس',
            'away_team' => 'إنتر ميلان',
            'competition' => 'الدوري الإيطالي',
            'match_date' => date('Y-m-d H:i:s', strtotime('+13 days +8 hours')),
            'venue' => 'ملعب أليانز',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'بايرن ميونخ',
            'away_team' => 'بوروسيا دورتموند',
            'competition' => 'الدوري الألماني',
            'match_date' => date('Y-m-d H:i:s', strtotime('+15 days +9 hours')),
            'venue' => 'أليانز أرينا',
            'status' => 'scheduled',
            'is_featured' => 1
        ],
        [
            'home_team' => 'أتلتيكو مدريد',
            'away_team' => 'إشبيلية',
            'competition' => 'الدوري الإسباني',
            'match_date' => date('Y-m-d H:i:s', strtotime('+17 days +10 hours')),
            'venue' => 'ملعب واندا ميتروبوليتانو',
            'status' => 'scheduled',
            'is_featured' => 0
        ]
    ];

    // حذف المباريات الموجودة (للاختبار فقط)
    $db->exec("DELETE FROM matches WHERE status = 'scheduled'");

    // إضافة المباريات الجديدة
    $stmt = $db->prepare("INSERT INTO matches (home_team, away_team, competition, match_date, venue, status, is_featured, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");

    $added_count = 0;
    foreach ($sample_matches as $match) {
        if ($stmt->execute([
            $match['home_team'],
            $match['away_team'],
            $match['competition'],
            $match['match_date'],
            $match['venue'],
            $match['status'],
            $match['is_featured']
        ])) {
            $added_count++;
        }
    }

    $success_messages[] = "تم إضافة {$added_count} مباراة تجريبية بنجاح";

} catch (Exception $e) {
    $error_messages[] = 'خطأ في إضافة المباريات: ' . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
            <i class="fas fa-futbol ml-2 text-green-600"></i>
            إضافة مباريات تجريبية
        </h1>

        <!-- الرسائل -->
        <?php if (!empty($success_messages)): ?>
            <?php foreach ($success_messages as $message): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-check-circle ml-2"></i><?php echo $message; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <?php if (!empty($error_messages)): ?>
            <?php foreach ($error_messages as $message): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-exclamation-circle ml-2"></i><?php echo $message; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- معلومات -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-blue-800">
                <i class="fas fa-info-circle ml-2"></i>معلومات
            </h2>
            <p class="text-blue-700 mb-4">
                تم إضافة 9 مباريات تجريبية لاختبار سلايدر المباريات في الصفحة الرئيسية.
            </p>
            <ul class="text-blue-700 space-y-2">
                <li><i class="fas fa-check ml-2"></i>مباريات من دوريات مختلفة</li>
                <li><i class="fas fa-check ml-2"></i>تواريخ مستقبلية متنوعة</li>
                <li><i class="fas fa-check ml-2"></i>بعض المباريات مميزة</li>
                <li><i class="fas fa-check ml-2"></i>أسماء فرق حقيقية</li>
            </ul>
        </div>

        <!-- روابط سريعة -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="index.php" class="bg-blue-600 text-white px-6 py-3 rounded text-center hover:bg-blue-700 transition-colors">
                <i class="fas fa-home block mb-1"></i>الصفحة الرئيسية
            </a>

            <a href="matches.php" class="bg-green-600 text-white px-6 py-3 rounded text-center hover:bg-green-700 transition-colors">
                <i class="fas fa-futbol block mb-1"></i>عرض المباريات
            </a>

            <a href="test-matches-slider.php" class="bg-purple-600 text-white px-6 py-3 rounded text-center hover:bg-purple-700 transition-colors">
                <i class="fas fa-vial block mb-1"></i>اختبار السلايدر
            </a>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
