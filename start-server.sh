#!/bin/bash

echo "========================================"
echo "       موقع الأخبار - تشغيل الخادم"
echo "========================================"
echo

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    echo "خطأ: PHP غير مثبت"
    echo "يرجى تثبيت PHP أولاً"
    exit 1
fi

echo "تم العثور على PHP..."
echo

# عرض معلومات الخادم
echo "سيتم تشغيل الخادم على:"
echo "http://localhost:8000"
echo
echo "للوصول إلى لوحة التحكم:"
echo "http://localhost:8000/admin/login.php"
echo
echo "بيانات الدخول الافتراضية:"
echo "اسم المستخدم: admin"
echo "كلمة المرور: admin123"
echo
echo "اضغط Ctrl+C لإيقاف الخادم"
echo "========================================"
echo

# تشغيل الخادم
php -S localhost:8000
