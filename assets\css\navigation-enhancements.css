/* ==========================================================================
   Navigation Enhancements
   ========================================================================== */

/* Main Navigation Styles */
.main-navigation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.main-navigation.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.15);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    margin: 0 10px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 25px;
    position: relative;
    overflow: hidden;
}

.main-navigation.scrolled .nav-link {
    color: #2c3e50;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.main-navigation.scrolled .nav-link:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.nav-link i {
    margin-left: 8px;
    font-size: 1.1rem;
}

/* Mobile Navigation */
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.main-navigation.scrolled .mobile-nav-toggle {
    color: #2c3e50;
}

.mobile-nav-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        border-radius: 0 0 15px 15px;
        padding: 20px 0;
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .nav-item {
        margin: 5px 0;
        width: 100%;
    }
    
    .nav-link {
        color: #2c3e50;
        padding: 12px 30px;
        border-radius: 0;
        justify-content: flex-start;
    }
    
    .nav-link:hover {
        background: #f8f9fa;
        transform: none;
        padding-right: 40px;
    }
    
    .mobile-nav-toggle {
        display: block;
    }
}

/* ==========================================================================
   Back to Top Button
   ========================================================================== */

.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: linear-gradient(135deg, #2980b9, #1f4e79);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.back-to-top:active {
    transform: translateY(-1px);
}

/* Pulse animation for attention */
@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(52, 152, 219, 0.6);
    }
    100% {
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }
}

.back-to-top.pulse {
    animation: pulse 2s infinite;
}

/* ==========================================================================
   Smooth Scrolling
   ========================================================================== */

html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2980b9, #1f4e79);
}

/* ==========================================================================
   Navigation Breadcrumbs
   ========================================================================== */

.breadcrumbs {
    background: #f8f9fa;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: #6c757d;
}

.breadcrumb-link {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

.breadcrumb-current {
    color: #6c757d;
    font-weight: 500;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 480px) {
    .back-to-top {
        bottom: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
    
    .nav-container {
        padding: 0 15px;
    }
}

/* ==========================================================================
   Accessibility Improvements
   ========================================================================== */

.nav-link:focus,
.back-to-top:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
    
    .back-to-top,
    .nav-link {
        transition: none;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .main-navigation,
    .back-to-top {
        display: none;
    }
}