<?php
/**
 * SQL Syntax Error Fix Tool
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define ROOT_PATH
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}

$action = $_GET['action'] ?? '';
$results = [];

function addResult($message, $success = true) {
    global $results;
    $results[] = [
        'message' => $message,
        'success' => $success
    ];
}

switch ($action) {
    case 'test_database':
        // Test database connection and basic queries
        try {
            include_once 'config/config.php';
            include_once 'config/database.php';
            
            $database = new Database();
            $db = $database->connect();
            
            if ($db) {
                addResult("✅ Database connection successful");
                
                // Test basic query
                $stmt = $db->query("SELECT 1 as test");
                if ($stmt) {
                    addResult("✅ Basic query test successful");
                }
                
                // Check if tables exist
                $tables = ['articles', 'categories', 'rss_sources', 'users', 'settings'];
                foreach ($tables as $table) {
                    $stmt = $db->query("SHOW TABLES LIKE '$table'");
                    if ($stmt && $stmt->fetch()) {
                        addResult("✅ Table '$table' exists");
                    } else {
                        addResult("❌ Table '$table' missing - run setup.php", false);
                    }
                }
            } else {
                addResult("❌ Database connection failed", false);
            }
        } catch (Exception $e) {
            addResult("❌ Database error: " . $e->getMessage(), false);
        }
        break;
        
    case 'test_functions':
        // Test the functions that were causing SQL errors
        try {
            include_once 'config/config.php';
            include_once 'includes/functions.php';
            
            addResult("✅ Functions loaded successfully");
            
            // Test getArticles function (this was causing the error)
            try {
                $articles = getArticles(1, 5);
                addResult("✅ getArticles function works (returned " . count($articles) . " articles)");
            } catch (Exception $e) {
                addResult("❌ getArticles error: " . $e->getMessage(), false);
            }
            
            // Test other functions
            try {
                $featured = getFeaturedArticles(3);
                addResult("✅ getFeaturedArticles function works (returned " . count($featured) . " articles)");
            } catch (Exception $e) {
                addResult("❌ getFeaturedArticles error: " . $e->getMessage(), false);
            }
            
            try {
                $latest = getLatestArticles(5);
                addResult("✅ getLatestArticles function works (returned " . count($latest) . " articles)");
            } catch (Exception $e) {
                addResult("❌ getLatestArticles error: " . $e->getMessage(), false);
            }
            
            try {
                $categories = getCategories();
                addResult("✅ getCategories function works (returned " . count($categories) . " categories)");
            } catch (Exception $e) {
                addResult("❌ getCategories error: " . $e->getMessage(), false);
            }
            
        } catch (Exception $e) {
            addResult("❌ Functions loading error: " . $e->getMessage(), false);
        }
        break;
        
    case 'create_sample_data':
        // Create some sample data for testing
        try {
            include_once 'config/config.php';
            include_once 'config/database.php';
            
            $database = new Database();
            $db = $database->connect();
            
            if (!$db) {
                addResult("❌ Database connection failed", false);
                break;
            }
            
            // Insert sample category
            $stmt = $db->prepare("INSERT IGNORE INTO categories (name, slug, description) VALUES (?, ?, ?)");
            $stmt->execute(['أخبار عامة', 'general-news', 'أخبار عامة ومتنوعة']);
            addResult("✅ Sample category created");
            
            // Get category ID
            $stmt = $db->prepare("SELECT id FROM categories WHERE slug = 'general-news'");
            $stmt->execute();
            $category = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($category) {
                // Insert sample article
                $stmt = $db->prepare("INSERT IGNORE INTO articles (title, slug, content, excerpt, category_id, published_at, created_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
                $stmt->execute([
                    'مقال تجريبي',
                    'sample-article',
                    'هذا مقال تجريبي لاختبار النظام. يحتوي على محتوى تجريبي لضمان عمل جميع الوظائف بشكل صحيح.',
                    'مقال تجريبي لاختبار النظام',
                    $category['id']
                ]);
                addResult("✅ Sample article created");
            }
            
            // Insert sample RSS source
            $stmt = $db->prepare("INSERT IGNORE INTO rss_sources (name, url, category_id, is_active) VALUES (?, ?, ?, 1)");
            $stmt->execute(['مصدر تجريبي', 'https://example.com/rss.xml', $category['id']]);
            addResult("✅ Sample RSS source created");
            
        } catch (Exception $e) {
            addResult("❌ Sample data creation error: " . $e->getMessage(), false);
        }
        break;
        
    default:
        addResult("No action specified", false);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Error Fix Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold mb-4">🔧 SQL Syntax Error Fix Tool</h1>
            
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h2 class="font-semibold text-blue-800 mb-2">Fixed Issues:</h2>
                <ul class="list-disc list-inside text-blue-700 space-y-1">
                    <li>LIMIT and OFFSET parameter binding in MySQL/MariaDB</li>
                    <li>Added input validation for SQL parameters</li>
                    <li>Fixed getArticles, getFeaturedArticles, and getLatestArticles functions</li>
                    <li>Added safe integer casting for all LIMIT values</li>
                </ul>
            </div>
            
            <?php if (!empty($results)): ?>
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-3">Test Results:</h2>
                    <div class="space-y-2">
                        <?php foreach ($results as $result): ?>
                            <div class="p-3 rounded <?php echo $result['success'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                <?php echo htmlspecialchars($result['message']); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <a href="?action=test_database" class="bg-blue-600 text-white p-4 rounded-lg text-center hover:bg-blue-700 transition-colors">
                    🗄️ Test Database
                </a>
                <a href="?action=test_functions" class="bg-green-600 text-white p-4 rounded-lg text-center hover:bg-green-700 transition-colors">
                    🔧 Test Functions
                </a>
                <a href="?action=create_sample_data" class="bg-purple-600 text-white p-4 rounded-lg text-center hover:bg-purple-700 transition-colors">
                    📝 Create Sample Data
                </a>
            </div>
            
            <div class="bg-yellow-50 p-4 rounded-lg mb-6">
                <h3 class="font-semibold text-yellow-800 mb-2">What was the problem?</h3>
                <p class="text-yellow-700 text-sm">
                    The error occurred because PDO was treating LIMIT and OFFSET values as quoted strings ('12', '0') 
                    instead of integers. MySQL/MariaDB requires these to be unquoted integers. The fix involved:
                </p>
                <ul class="list-disc list-inside text-yellow-700 text-sm mt-2 space-y-1">
                    <li>Removing parameter placeholders (?) for LIMIT and OFFSET</li>
                    <li>Using direct integer interpolation with proper validation</li>
                    <li>Adding input sanitization to prevent SQL injection</li>
                    <li>Limiting maximum values to prevent abuse</li>
                </ul>
            </div>
            
            <div class="flex space-x-4">
                <a href="index.php" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    🏠 Test Homepage
                </a>
                <a href="setup.php" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    ⚙️ Setup Database
                </a>
                <a href="final-test.php" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    ✅ Final Test
                </a>
            </div>
        </div>
    </div>
</body>
</html>
