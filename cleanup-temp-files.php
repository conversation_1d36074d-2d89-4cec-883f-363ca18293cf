<?php
/**
 * تنظيف الملفات المؤقتة
 * Cleanup temporary files
 */

echo "🧹 بدء عملية تنظيف الملفات المؤقتة...\n\n";

$filesToDelete = [
    'add_sample_matches.php',
    'test-matches-widget.php',
    'cleanup-temp-files.php'
];

$deletedCount = 0;
$totalFiles = count($filesToDelete);

foreach ($filesToDelete as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            echo "✅ تم حذف: $file\n";
            $deletedCount++;
        } else {
            echo "❌ فشل حذف: $file\n";
        }
    } else {
        echo "⚠️ الملف غير موجود: $file\n";
    }
}

echo "\n📊 ملخص العملية:\n";
echo "- تم حذف $deletedCount من أصل $totalFiles ملف\n";
echo "- الملفات المتبقية هي الملفات الأساسية للموقع\n\n";

echo "🎉 تم الانتهاء من عملية التنظيف!\n";
echo "📁 الملفات الأساسية المضافة:\n";
echo "   - assets/css/matches-widget.css\n";
echo "   - assets/js/matches-widget.js\n";
echo "   - MATCHES_WIDGET_FIX_REPORT.md\n\n";

echo "✨ جدول المباريات جاهز للاستخدام!\n";
?>
