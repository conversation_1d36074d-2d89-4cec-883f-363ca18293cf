<?php
/**
 * اختبار سلايدر المباريات في الصفحة الرئيسية
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من وجود ملف دوال المباريات
if (!file_exists('includes/matches_functions.php')) {
    die('ملف دوال المباريات غير موجود: includes/matches_functions.php');
}

require_once 'includes/matches_functions.php';

session_start();

$page_title = 'اختبار سلايدر المباريات';
$test_results = [];

// اختبار الدوال المختلفة
function testFunction($function_name, $function_call, $description = '') {
    global $test_results;
    
    try {
        $start_time = microtime(true);
        $result = $function_call();
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        $test_results[] = [
            'function' => $function_name,
            'description' => $description,
            'status' => 'success',
            'message' => 'تم تنفيذ الدالة بنجاح',
            'count' => is_array($result) ? count($result) : (is_numeric($result) ? $result : 1),
            'time' => $execution_time . ' ms',
            'data' => is_array($result) ? array_slice($result, 0, 2) : null
        ];
        
        return $result;
    } catch (Exception $e) {
        $test_results[] = [
            'function' => $function_name,
            'description' => $description,
            'status' => 'error',
            'message' => $e->getMessage(),
            'count' => 0,
            'time' => '0 ms',
            'data' => null
        ];
        
        return false;
    }
}

// جلب المباريات للاختبار
$upcoming_matches = testFunction(
    'getUpcomingMatches(9)', 
    function() { return getUpcomingMatches(9); },
    'جلب 9 مباريات قادمة للسلايدر'
);

// حساب عدد الشرائح
$total_slides = 0;
if ($upcoming_matches) {
    $all_matches = array_slice($upcoming_matches, 0, 9);
    $total_slides = ceil(count($all_matches) / 3);
    
    testFunction(
        'حساب عدد الشرائح',
        function() use ($total_slides) { return $total_slides; },
        'حساب عدد الشرائح المطلوبة للسلايدر'
    );
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
            <i class="fas fa-images ml-2 text-blue-600"></i>
            اختبار سلايدر المباريات
        </h1>

        <!-- نتائج الاختبارات -->
        <div class="grid grid-cols-1 gap-4 mb-8">
            <?php foreach ($test_results as $result): ?>
            <?php 
            $status_color = $result['status'] === 'success' ? 'green' : 'red';
            $status_icon = $result['status'] === 'success' ? 'check-circle' : 'times-circle';
            ?>
            <div class="bg-<?php echo $status_color; ?>-50 border border-<?php echo $status_color; ?>-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                        <i class="fas fa-<?php echo $status_icon; ?> text-<?php echo $status_color; ?>-600 ml-3"></i>
                        <div>
                            <h3 class="font-semibold text-<?php echo $status_color; ?>-800"><?php echo $result['function']; ?></h3>
                            <p class="text-sm text-<?php echo $status_color; ?>-700"><?php echo $result['description']; ?></p>
                        </div>
                    </div>
                    <div class="text-left">
                        <div class="text-sm text-<?php echo $status_color; ?>-600">النتائج: <?php echo $result['count']; ?></div>
                        <div class="text-xs text-<?php echo $status_color; ?>-500">الوقت: <?php echo $result['time']; ?></div>
                    </div>
                </div>
                
                <?php if ($result['status'] === 'success' && $result['data']): ?>
                <div class="mt-3 p-3 bg-white rounded border">
                    <h4 class="font-medium text-gray-800 mb-2">عينة من البيانات:</h4>
                    <pre class="text-xs text-gray-600 overflow-x-auto"><?php echo htmlspecialchars(json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                </div>
                <?php endif; ?>
                
                <?php if ($result['status'] === 'error'): ?>
                <div class="mt-3 p-3 bg-red-100 rounded border border-red-300">
                    <h4 class="font-medium text-red-800 mb-2">تفاصيل الخطأ:</h4>
                    <p class="text-sm text-red-700"><?php echo $result['message']; ?></p>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- معاينة السلايدر -->
        <?php if ($upcoming_matches && count($upcoming_matches) > 0): ?>
        <div class="bg-gradient-to-r from-green-600 to-blue-600 py-8 mb-8 matches-widget rounded-xl">
            <div class="container mx-auto px-4">
                <div class="bg-white rounded-xl shadow-2xl overflow-hidden relative z-10">
                    <!-- Header -->
                    <div class="bg-gradient-to-r from-green-500 to-blue-500 text-white p-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="bg-white bg-opacity-20 rounded-full p-3 ml-4">
                                    <i class="fas fa-futbol text-2xl"></i>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold">معاينة سلايدر المباريات</h2>
                                    <p class="text-green-100 text-sm">اختبار تفاعلي للسلايدر</p>
                                </div>
                            </div>
                            <div class="hidden md:flex items-center space-x-4 space-x-reverse">
                                <div class="text-center">
                                    <div class="text-2xl font-bold"><?php echo count($upcoming_matches); ?></div>
                                    <div class="text-xs text-green-100">مباراة</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold"><?php echo $total_slides; ?></div>
                                    <div class="text-xs text-green-100">شريحة</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Matches Slider -->
                    <div class="p-6">
                        <div class="matches-slider" id="testMatchesSlider">
                            <!-- Navigation Buttons -->
                            <button class="slider-nav-btn prev" id="testPrevBtn" aria-label="المباراة السابقة">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button class="slider-nav-btn next" id="testNextBtn" aria-label="المباراة التالية">
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <!-- Slider Container -->
                            <div class="matches-slider-container" id="testSliderContainer">
                                <?php 
                                $all_matches = array_slice($upcoming_matches, 0, 9);
                                for ($slide = 0; $slide < $total_slides; $slide++): 
                                    $slide_matches = array_slice($all_matches, $slide * 3, 3);
                                ?>
                                <div class="matches-slide">
                                    <?php foreach ($slide_matches as $match): ?>
                                    <div class="match-card <?php echo $match['is_featured'] ? 'featured-match' : 'bg-gray-50'; ?> rounded-lg p-4 border border-gray-200">
                                        <!-- Match Date & Competition -->
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="text-xs text-gray-600 bg-blue-100 px-2 py-1 rounded-full">
                                                <i class="fas fa-calendar ml-1"></i>
                                                <?php echo formatMatchDate($match['match_date'], 'date_only'); ?>
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                <?php echo mb_substr($match['competition'], 0, 15) . (mb_strlen($match['competition']) > 15 ? '...' : ''); ?>
                                            </div>
                                        </div>

                                        <!-- Teams -->
                                        <div class="flex items-center justify-between mb-3">
                                            <!-- Home Team -->
                                            <div class="flex items-center flex-1">
                                                <?php if ($match['home_team_logo']): ?>
                                                <img src="<?php echo $match['home_team_logo']; ?>" 
                                                     alt="<?php echo $match['home_team']; ?>" 
                                                     class="team-logo w-8 h-8 object-contain ml-2 rounded">
                                                <?php else: ?>
                                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center ml-2">
                                                    <i class="fas fa-shield-alt text-gray-500 text-xs"></i>
                                                </div>
                                                <?php endif; ?>
                                                <span class="text-sm font-semibold text-gray-800 truncate">
                                                    <?php echo mb_substr($match['home_team'], 0, 12) . (mb_strlen($match['home_team']) > 12 ? '...' : ''); ?>
                                                </span>
                                            </div>

                                            <!-- VS -->
                                            <div class="mx-3">
                                                <span class="text-xs text-gray-400 font-bold bg-gray-200 px-2 py-1 rounded">VS</span>
                                            </div>

                                            <!-- Away Team -->
                                            <div class="flex items-center flex-1 justify-end">
                                                <span class="text-sm font-semibold text-gray-800 truncate">
                                                    <?php echo mb_substr($match['away_team'], 0, 12) . (mb_strlen($match['away_team']) > 12 ? '...' : ''); ?>
                                                </span>
                                                <?php if ($match['away_team_logo']): ?>
                                                <img src="<?php echo $match['away_team_logo']; ?>" 
                                                     alt="<?php echo $match['away_team']; ?>" 
                                                     class="team-logo w-8 h-8 object-contain mr-2 rounded">
                                                <?php else: ?>
                                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-2">
                                                    <i class="fas fa-shield-alt text-gray-500 text-xs"></i>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Match Time & Status -->
                                        <div class="flex items-center justify-between">
                                            <div class="text-xs text-gray-600">
                                                <i class="fas fa-clock ml-1 text-blue-500"></i>
                                                <?php echo formatMatchDate($match['match_date'], 'time_only'); ?>
                                            </div>
                                            <?php if ($match['venue']): ?>
                                            <div class="text-xs text-gray-500 truncate max-w-20">
                                                <i class="fas fa-map-marker-alt ml-1"></i>
                                                <?php echo mb_substr($match['venue'], 0, 10) . (mb_strlen($match['venue']) > 10 ? '...' : ''); ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Featured Match Indicator -->
                                        <?php if ($match['is_featured']): ?>
                                        <div class="mt-2 text-center">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <i class="fas fa-star ml-1"></i>مباراة مميزة
                                            </span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endfor; ?>
                            </div>

                            <!-- Slider Dots -->
                            <div class="slider-dots" id="testSliderDots">
                                <?php for ($i = 0; $i < $total_slides; $i++): ?>
                                <button class="slider-dot <?php echo $i === 0 ? 'active' : ''; ?>" 
                                        data-slide="<?php echo $i; ?>" 
                                        aria-label="الشريحة <?php echo $i + 1; ?>"></button>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تعليمات الاختبار -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-blue-800">
                <i class="fas fa-info-circle ml-2"></i>تعليمات الاختبار
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-3">التحكم بالسلايدر</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>🖱️ <strong>الأزرار:</strong> استخدم أزرار السابق/التالي</li>
                        <li>⚫ <strong>النقاط:</strong> اضغط على النقاط للانتقال</li>
                        <li>👆 <strong>اللمس:</strong> اسحب يميناً أو يساراً</li>
                        <li>⌨️ <strong>لوحة المفاتيح:</strong> أسهم اليمين/اليسار</li>
                        <li>⏸️ <strong>الإيقاف:</strong> مرر الماوس فوق السلايدر</li>
                    </ul>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-3">الميزات المتقدمة</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>🔄 <strong>تشغيل تلقائي:</strong> كل 4 ثوان</li>
                        <li>📱 <strong>متجاوب:</strong> يتكيف مع حجم الشاشة</li>
                        <li>🎯 <strong>RTL:</strong> دعم كامل للغة العربية</li>
                        <li>♿ <strong>إمكانية الوصول:</strong> دعم قارئات الشاشة</li>
                        <li>⚡ <strong>أداء محسن:</strong> انتقالات سلسة</li>
                    </ul>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- روابط سريعة -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-link ml-2"></i>روابط سريعة
            </h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="index.php" class="bg-blue-600 text-white px-4 py-3 rounded text-center hover:bg-blue-700 transition-colors">
                    <i class="fas fa-home block mb-1"></i>الصفحة الرئيسية
                </a>
                
                <a href="matches.php" class="bg-green-600 text-white px-4 py-3 rounded text-center hover:bg-green-700 transition-colors">
                    <i class="fas fa-futbol block mb-1"></i>صفحة المباريات
                </a>
                
                <a href="admin/matches.php" class="bg-purple-600 text-white px-4 py-3 rounded text-center hover:bg-purple-700 transition-colors">
                    <i class="fas fa-cogs block mb-1"></i>إدارة المباريات
                </a>
                
                <a href="test-homepage-matches.php" class="bg-orange-600 text-white px-4 py-3 rounded text-center hover:bg-orange-700 transition-colors">
                    <i class="fas fa-vial block mb-1"></i>اختبار الجدول
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Test Slider JavaScript -->
<?php if ($upcoming_matches && count($upcoming_matches) > 0): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('testMatchesSlider');
    const sliderContainer = document.getElementById('testSliderContainer');
    const prevBtn = document.getElementById('testPrevBtn');
    const nextBtn = document.getElementById('testNextBtn');
    const dots = document.querySelectorAll('#testSliderDots .slider-dot');
    
    if (!slider || !sliderContainer || !prevBtn || !nextBtn) {
        console.warn('Test slider elements not found');
        return;
    }

    let currentSlide = 0;
    const totalSlides = <?php echo $total_slides; ?>;
    let autoPlayInterval;
    let isAutoPlaying = true;

    // Initialize slider
    function initSlider() {
        updateSlider();
        startAutoPlay();
        
        prevBtn.addEventListener('click', prevSlide);
        nextBtn.addEventListener('click', nextSlide);
        
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => goToSlide(index));
        });
        
        slider.addEventListener('mouseenter', pauseAutoPlay);
        slider.addEventListener('mouseleave', resumeAutoPlay);
    }

    function updateSlider() {
        const translateX = -currentSlide * 100;
        sliderContainer.style.transform = `translateX(${translateX}%)`;
        
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlide);
        });
        
        prevBtn.style.opacity = currentSlide === 0 ? '0.5' : '1';
        nextBtn.style.opacity = currentSlide === totalSlides - 1 ? '0.5' : '1';
        
        slider.classList.toggle('slider-paused', !isAutoPlaying);
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateSlider();
        resetAutoPlay();
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        updateSlider();
        resetAutoPlay();
    }

    function goToSlide(slideIndex) {
        currentSlide = slideIndex;
        updateSlider();
        resetAutoPlay();
    }

    function startAutoPlay() {
        if (totalSlides <= 1) return;
        autoPlayInterval = setInterval(() => {
            if (isAutoPlaying) nextSlide();
        }, 4000);
    }

    function pauseAutoPlay() {
        isAutoPlaying = false;
        updateSlider();
    }

    function resumeAutoPlay() {
        isAutoPlaying = true;
        updateSlider();
    }

    function resetAutoPlay() {
        clearInterval(autoPlayInterval);
        startAutoPlay();
    }

    initSlider();
    console.log('Test matches slider initialized successfully');
});
</script>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
