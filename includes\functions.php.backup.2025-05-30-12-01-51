<?php
// تحديد المسار الجذر للمشروع
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__DIR__));
}

require_once ROOT_PATH . '/config/database.php';

/**
 * دوال مساعدة للموقع
 */

/**
 * الحصول على إعداد من قاعدة البيانات
 */
function getSetting($key, $default = '') {
    static $settings = null;
    
    if ($settings === null) {
        $database = new Database();
        $db = $database->connect();
        $stmt = $db->prepare("SELECT setting_key, setting_value FROM settings");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    }
    
    return isset($settings[$key]) ? $settings[$key] : $default;
}

/**
 * تحديث إعداد في قاعدة البيانات
 */
function updateSetting($key, $value) {
    $database = new Database();
    $db = $database->connect();
    $stmt = $db->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
    return $stmt->execute([$value, $key]);
}

/**
 * الحصول على المقالات مع التصفح
 */
function getArticles($page = 1, $per_page = null, $category_id = null, $search = null) {
    $database = new Database();
    $db = $database->connect();

    if ($per_page === null) {
        $per_page = (int) getSetting('articles_per_page', 12);
    }

    // Ensure safe integer values
    $page = max(1, (int) $page);
    $per_page = max(1, min(100, (int) $per_page)); // Limit to max 100 per page
    $offset = ($page - 1) * $per_page;
    
    $where_conditions = [];
    $params = [];
    
    if ($category_id) {
        $where_conditions[] = "a.category_id = ?";
        $params[] = $category_id;
    }
    
    if ($search) {
        $where_conditions[] = "(a.title LIKE ? OR a.content LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
    
    $sql = "SELECT a.*, c.name as category_name, c.slug as category_slug
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            $where_clause
            ORDER BY a.published_at DESC
            LIMIT $per_page OFFSET $offset";

    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على عدد المقالات الإجمالي
 */
function getTotalArticles($category_id = null, $search = null) {
    $database = new Database();
    $db = $database->connect();
    
    $where_conditions = [];
    $params = [];
    
    if ($category_id) {
        $where_conditions[] = "category_id = ?";
        $params[] = $category_id;
    }
    
    if ($search) {
        $where_conditions[] = "(title LIKE ? OR content LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
    
    $sql = "SELECT COUNT(*) FROM articles $where_clause";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchColumn();
}

/**
 * الحصول على مقال واحد
 */
function getArticle($id_or_slug) {
    $database = new Database();
    $db = $database->connect();
    
    $sql = "SELECT a.*, c.name as category_name, c.slug as category_slug, r.name as source_name 
            FROM articles a 
            LEFT JOIN categories c ON a.category_id = c.id 
            LEFT JOIN rss_sources r ON a.rss_source_id = r.id 
            WHERE a.id = ? OR a.slug = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$id_or_slug, $id_or_slug]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * زيادة عدد المشاهدات
 */
function incrementViews($article_id) {
    $database = new Database();
    $db = $database->connect();
    $stmt = $db->prepare("UPDATE articles SET views = views + 1 WHERE id = ?");
    return $stmt->execute([$article_id]);
}

/**
 * الحصول على التصنيفات
 */
function getCategories() {
    $database = new Database();
    $db = $database->connect();
    $stmt = $db->prepare("SELECT * FROM categories ORDER BY name");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على تصنيف واحد
 */
function getCategory($id_or_slug) {
    $database = new Database();
    $db = $database->connect();
    $stmt = $db->prepare("SELECT * FROM categories WHERE id = ? OR slug = ?");
    $stmt->execute([$id_or_slug, $id_or_slug]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * الحصول على المقالات المميزة
 */
function getFeaturedArticles($limit = 5) {
    $database = new Database();
    $db = $database->connect();

    // Ensure safe integer value
    $limit = max(1, min(50, (int) $limit));
    
    $sql = "SELECT a.*, c.name as category_name, c.slug as category_slug
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE a.is_featured = 1
            ORDER BY a.published_at DESC
            LIMIT $limit";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على أحدث المقالات
 */
function getLatestArticles($limit = 10, $exclude_id = null) {
    $database = new Database();
    $db = $database->connect();

    // Ensure safe integer value
    $limit = max(1, min(50, (int) $limit));
    
    $where_clause = $exclude_id ? "WHERE a.id != ?" : "";
    $params = $exclude_id ? [$exclude_id] : [];

    $sql = "SELECT a.*, c.name as category_name, c.slug as category_slug
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            $where_clause
            ORDER BY a.published_at DESC
            LIMIT $limit";

    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d H:i') {
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ بالعربية
 */
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    $time = date('H:i', $timestamp);
    
    return "$day $month $year - $time";
}

/**
 * إنشاء روابط التصفح
 */
function generatePagination($current_page, $total_pages, $base_url) {
    $pagination = '';
    
    if ($total_pages <= 1) {
        return $pagination;
    }
    
    $pagination .= '<nav class="flex justify-center mt-8">';
    $pagination .= '<div class="flex space-x-2">';
    
    // الصفحة السابقة
    if ($current_page > 1) {
        $prev_page = $current_page - 1;
        $pagination .= "<a href='$base_url?page=$prev_page' class='px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'>السابق</a>";
    }
    
    // أرقام الصفحات
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $active_class = ($i == $current_page) ? 'bg-blue-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300';
        $pagination .= "<a href='$base_url?page=$i' class='px-3 py-2 $active_class rounded'>$i</a>";
    }
    
    // الصفحة التالية
    if ($current_page < $total_pages) {
        $next_page = $current_page + 1;
        $pagination .= "<a href='$base_url?page=$next_page' class='px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'>التالي</a>";
    }
    
    $pagination .= '</div>';
    $pagination .= '</nav>';
    
    return $pagination;
}

/**
 * تنظيف وتأمين المدخلات
 */
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من صلاحيات الإدارة
 */
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * إعادة توجيه
 */
function redirect($url) {
    header("Location: $url");
    exit();
}

/**
 * عرض رسالة
 */
function showMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

/**
 * الحصول على الرسالة وحذفها
 */
function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * إنشاء slug من النص
 */
function createSlug($text) {
    // تحويل إلى أحرف صغيرة
    $slug = mb_strtolower($text, 'UTF-8');

    // إزالة الأحرف الخاصة والاحتفاظ بالأحرف العربية والإنجليزية والأرقام
    $slug = preg_replace('/[^\p{Arabic}\p{L}\p{N}\s\-_]/u', '', $slug);

    // استبدال المسافات والشرطات المتعددة بشرطة واحدة
    $slug = preg_replace('/[\s\-_]+/', '-', $slug);

    // إزالة الشرطات من البداية والنهاية
    $slug = trim($slug, '-');

    // تحديد الطول الأقصى
    $slug = mb_substr($slug, 0, 100, 'UTF-8');

    // إذا كان فارغاً، استخدم timestamp
    if (empty($slug)) {
        $slug = 'article-' . time();
    }

    return $slug;
}

/**
 * التحقق من وجود slug في قاعدة البيانات وإنشاء slug فريد
 */
function createUniqueSlug($text, $table, $id = null) {
    $database = new Database();
    $db = $database->connect();

    $base_slug = createSlug($text);
    $slug = $base_slug;
    $counter = 1;

    while (true) {
        // التحقق من وجود slug
        if ($id) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM $table WHERE slug = ? AND id != ?");
            $stmt->execute([$slug, $id]);
        } else {
            $stmt = $db->prepare("SELECT COUNT(*) FROM $table WHERE slug = ?");
            $stmt->execute([$slug]);
        }

        if ($stmt->fetchColumn() == 0) {
            break;
        }

        $slug = $base_slug . '-' . $counter;
        $counter++;
    }

    return $slug;
}
?>
