<?php
/**
 * إضافة تحسينات التنقل وزر العودة للأعلى
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إضافة تحسينات التنقل</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo ".code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; border-left: 4px solid #007bff; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🧭 إضافة تحسينات التنقل وزر العودة للأعلى</h1>";

$enhancements_applied = [];
$errors = [];

// 1. فحص الوضع الحالي
echo "<div class='section info'>";
echo "<h2>1. فحص الوضع الحالي</h2>";

// فحص وجود التنقل الرئيسي
$header_content = file_get_contents('includes/header.php');
$has_main_nav = strpos($header_content, 'main-nav') !== false;
$has_mobile_nav = strpos($header_content, 'mobile-nav') !== false;

echo "<p><strong>شريط التنقل الرئيسي:</strong> " . ($has_main_nav ? '✅ موجود' : '❌ غير موجود') . "</p>";
echo "<p><strong>التنقل للموبايل:</strong> " . ($has_mobile_nav ? '✅ موجود' : '❌ غير موجود') . "</p>";

// فحص وجود زر العودة للأعلى
$index_content = file_get_contents('index.php');
$has_back_to_top = strpos($index_content, 'back-to-top') !== false || strpos($index_content, 'scroll-to-top') !== false;

echo "<p><strong>زر العودة للأعلى:</strong> " . ($has_back_to_top ? '✅ موجود' : '❌ غير موجود') . "</p>";
echo "</div>";

// 2. إنشاء ملف CSS للتحسينات
echo "<div class='section info'>";
echo "<h2>2. إنشاء ملف CSS للتحسينات</h2>";

$navigation_css = "/* ==========================================================================
   Navigation Enhancements
   ========================================================================== */

/* Main Navigation Styles */
.main-navigation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.main-navigation.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.15);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    margin: 0 10px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 25px;
    position: relative;
    overflow: hidden;
}

.main-navigation.scrolled .nav-link {
    color: #2c3e50;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.main-navigation.scrolled .nav-link:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.nav-link i {
    margin-left: 8px;
    font-size: 1.1rem;
}

/* Mobile Navigation */
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.main-navigation.scrolled .mobile-nav-toggle {
    color: #2c3e50;
}

.mobile-nav-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        border-radius: 0 0 15px 15px;
        padding: 20px 0;
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .nav-item {
        margin: 5px 0;
        width: 100%;
    }
    
    .nav-link {
        color: #2c3e50;
        padding: 12px 30px;
        border-radius: 0;
        justify-content: flex-start;
    }
    
    .nav-link:hover {
        background: #f8f9fa;
        transform: none;
        padding-right: 40px;
    }
    
    .mobile-nav-toggle {
        display: block;
    }
}

/* ==========================================================================
   Back to Top Button
   ========================================================================== */

.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: linear-gradient(135deg, #2980b9, #1f4e79);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.back-to-top:active {
    transform: translateY(-1px);
}

/* Pulse animation for attention */
@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(52, 152, 219, 0.6);
    }
    100% {
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }
}

.back-to-top.pulse {
    animation: pulse 2s infinite;
}

/* ==========================================================================
   Smooth Scrolling
   ========================================================================== */

html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2980b9, #1f4e79);
}

/* ==========================================================================
   Navigation Breadcrumbs
   ========================================================================== */

.breadcrumbs {
    background: #f8f9fa;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: #6c757d;
}

.breadcrumb-link {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

.breadcrumb-current {
    color: #6c757d;
    font-weight: 500;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 480px) {
    .back-to-top {
        bottom: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
    
    .nav-container {
        padding: 0 15px;
    }
}

/* ==========================================================================
   Accessibility Improvements
   ========================================================================== */

.nav-link:focus,
.back-to-top:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
    
    .back-to-top,
    .nav-link {
        transition: none;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .main-navigation,
    .back-to-top {
        display: none;
    }
}";

// حفظ ملف CSS
if (file_put_contents('assets/css/navigation-enhancements.css', $navigation_css)) {
    $enhancements_applied[] = "إنشاء ملف CSS للتحسينات";
    echo "<p style='color: green;'>✅ تم إنشاء ملف navigation-enhancements.css</p>";
} else {
    $errors[] = "فشل في إنشاء ملف CSS";
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف CSS</p>";
}

echo "<div class='code'>تم إنشاء الملف: assets/css/navigation-enhancements.css</div>";
echo "</div>";

// 3. إنشاء ملف JavaScript للتحسينات
echo "<div class='section info'>";
echo "<h2>3. إنشاء ملف JavaScript للتحسينات</h2>";

$navigation_js = "/**
 * Navigation Enhancements JavaScript
 * تحسينات التنقل وزر العودة للأعلى
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ==========================================================================
    // Navigation Scroll Effect
    // ==========================================================================
    
    const navigation = document.querySelector('.main-navigation');
    const backToTopBtn = document.querySelector('.back-to-top');
    
    if (navigation) {
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Add scrolled class to navigation
            if (scrollTop > 100) {
                navigation.classList.add('scrolled');
            } else {
                navigation.classList.remove('scrolled');
            }
            
            // Show/hide back to top button
            if (backToTopBtn) {
                if (scrollTop > 300) {
                    backToTopBtn.classList.add('visible');
                    
                    // Add pulse effect after 5 seconds
                    setTimeout(() => {
                        if (backToTopBtn.classList.contains('visible')) {
                            backToTopBtn.classList.add('pulse');
                        }
                    }, 5000);
                } else {
                    backToTopBtn.classList.remove('visible', 'pulse');
                }
            }
        });
    }
    
    // ==========================================================================
    // Mobile Navigation Toggle
    // ==========================================================================
    
    const mobileToggle = document.querySelector('.mobile-nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Change icon
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                if (navMenu.classList.contains('active')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navigation.contains(e.target)) {
                navMenu.classList.remove('active');
                const icon = mobileToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Close mobile menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navMenu.classList.remove('active');
                const icon = mobileToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            });
        });
    }
    
    // ==========================================================================
    // Back to Top Functionality
    // ==========================================================================
    
    if (backToTopBtn) {
        backToTopBtn.addEventListener('click', function() {
            // Smooth scroll to top
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            
            // Remove pulse effect
            backToTopBtn.classList.remove('pulse');
        });
    }
    
    // ==========================================================================
    // Active Navigation Link
    // ==========================================================================
    
    function setActiveNavLink() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.php';
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            
            if (href === currentPage || 
                (currentPage === '' && href === 'index.php') ||
                (currentPage === 'index.php' && href === 'index.php')) {
                link.classList.add('active');
            }
        });
    }
    
    setActiveNavLink();
    
    // ==========================================================================
    // Smooth Scrolling for Anchor Links
    // ==========================================================================
    
    const anchorLinks = document.querySelectorAll('a[href^=\"#\"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                e.preventDefault();
                
                const offsetTop = targetElement.offsetTop - (navigation ? navigation.offsetHeight : 0);
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // ==========================================================================
    // Keyboard Navigation
    // ==========================================================================
    
    document.addEventListener('keydown', function(e) {
        // ESC key closes mobile menu
        if (e.key === 'Escape' && navMenu && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-bars';
            }
        }
        
        // Home key scrolls to top
        if (e.key === 'Home' && e.ctrlKey && backToTopBtn) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    });
    
    // ==========================================================================
    // Performance Optimization
    // ==========================================================================
    
    // Throttle scroll events for better performance
    let ticking = false;
    
    function updateScrollEffects() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Update navigation
        if (navigation) {
            if (scrollTop > 100) {
                navigation.classList.add('scrolled');
            } else {
                navigation.classList.remove('scrolled');
            }
        }
        
        // Update back to top button
        if (backToTopBtn) {
            if (scrollTop > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible', 'pulse');
            }
        }
        
        ticking = false;
    }
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }
    
    // Replace the original scroll listener with throttled version
    window.removeEventListener('scroll', arguments.callee);
    window.addEventListener('scroll', requestTick);
    
    console.log('Navigation enhancements loaded successfully');
});";

// حفظ ملف JavaScript
if (file_put_contents('assets/js/navigation-enhancements.js', $navigation_js)) {
    $enhancements_applied[] = "إنشاء ملف JavaScript للتحسينات";
    echo "<p style='color: green;'>✅ تم إنشاء ملف navigation-enhancements.js</p>";
} else {
    $errors[] = "فشل في إنشاء ملف JavaScript";
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف JavaScript</p>";
}

echo "<div class='code'>تم إنشاء الملف: assets/js/navigation-enhancements.js</div>";
echo "</div>";

// 4. ملخص التحسينات
echo "<div class='section success'>";
echo "<h2>4. ملخص التحسينات المضافة</h2>";

if (!empty($enhancements_applied)) {
    echo "<h3>التحسينات المطبقة:</h3>";
    echo "<ul>";
    foreach ($enhancements_applied as $enhancement) {
        echo "<li style='color: green;'>✅ $enhancement</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3>الأخطاء:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>❌ $error</li>";
    }
    echo "</ul>";
}

echo "<h3>الميزات المضافة:</h3>";
echo "<ul>";
echo "<li>🧭 <strong>شريط تنقل محسن:</strong> تصميم متجاوب مع تأثيرات التمرير</li>";
echo "<li>📱 <strong>تنقل الموبايل:</strong> قائمة منسدلة سهلة الاستخدام</li>";
echo "<li>⬆️ <strong>زر العودة للأعلى:</strong> يظهر عند التمرير مع تأثيرات جميلة</li>";
echo "<li>🎯 <strong>التنقل النشط:</strong> تمييز الصفحة الحالية</li>";
echo "<li>⌨️ <strong>دعم الكيبورد:</strong> تنقل بالكيبورد للوصولية</li>";
echo "<li>🎨 <strong>تأثيرات بصرية:</strong> انتقالات سلسة وتأثيرات hover</li>";
echo "<li>📏 <strong>شريط التمرير المخصص:</strong> تصميم جميل لشريط التمرير</li>";
echo "<li>🔗 <strong>التمرير السلس:</strong> للروابط الداخلية</li>";
echo "</ul>";
echo "</div>";

// 5. خطوات التطبيق
echo "<div class='section warning'>";
echo "<h2>5. خطوات التطبيق</h2>";
echo "<p>لتطبيق هذه التحسينات على الموقع، تحتاج إلى:</p>";
echo "<ol>";
echo "<li><strong>إضافة ملفات CSS و JavaScript:</strong> تم إنشاؤها بالفعل</li>";
echo "<li><strong>تحديث header.php:</strong> لإضافة الروابط للملفات الجديدة</li>";
echo "<li><strong>إضافة زر العودة للأعلى:</strong> في footer.php أو index.php</li>";
echo "<li><strong>تحديث التنقل:</strong> لاستخدام الكلاسات الجديدة</li>";
echo "</ol>";
echo "</div>";

// 6. روابط الاختبار
echo "<div class='section info'>";
echo "<h2>6. الخطوات التالية</h2>";
echo "<a href='apply-navigation-updates.php' class='btn btn-primary'>تطبيق التحديثات</a>";
echo "<a href='test-navigation.php' class='btn btn-success'>اختبار التنقل</a>";
echo "<a href='index.php' target='_blank' class='btn btn-warning'>معاينة الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
