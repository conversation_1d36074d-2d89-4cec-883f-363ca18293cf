<?php
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/matches_functions.php';

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = 'إدارة المباريات';
$success_message = '';
$error_message = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $data = [
                    'home_team' => $_POST['home_team'],
                    'away_team' => $_POST['away_team'],
                    'match_date' => $_POST['match_date'],
                    'competition' => $_POST['competition'],
                    'venue' => $_POST['venue'],
                    'status' => $_POST['status'],
                    'home_score' => !empty($_POST['home_score']) ? $_POST['home_score'] : null,
                    'away_score' => !empty($_POST['away_score']) ? $_POST['away_score'] : null,
                    'match_time' => $_POST['match_time'],
                    'description' => $_POST['description'],
                    'is_featured' => isset($_POST['is_featured']) ? 1 : 0
                ];
                
                // رفع شعارات الفرق
                if (isset($_FILES['home_team_logo']) && $_FILES['home_team_logo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadTeamLogo($_FILES['home_team_logo'], $_POST['home_team']);
                    if ($upload_result['success']) {
                        $data['home_team_logo'] = $upload_result['filepath'];
                    }
                }
                
                if (isset($_FILES['away_team_logo']) && $_FILES['away_team_logo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadTeamLogo($_FILES['away_team_logo'], $_POST['away_team']);
                    if ($upload_result['success']) {
                        $data['away_team_logo'] = $upload_result['filepath'];
                    }
                }
                
                if (addMatch($data)) {
                    $success_message = 'تم إضافة المباراة بنجاح';
                } else {
                    $error_message = 'حدث خطأ أثناء إضافة المباراة';
                }
                break;
                
            case 'edit':
                $data = [
                    'home_team' => $_POST['home_team'],
                    'away_team' => $_POST['away_team'],
                    'match_date' => $_POST['match_date'],
                    'competition' => $_POST['competition'],
                    'venue' => $_POST['venue'],
                    'status' => $_POST['status'],
                    'home_score' => !empty($_POST['home_score']) ? $_POST['home_score'] : null,
                    'away_score' => !empty($_POST['away_score']) ? $_POST['away_score'] : null,
                    'match_time' => $_POST['match_time'],
                    'description' => $_POST['description'],
                    'is_featured' => isset($_POST['is_featured']) ? 1 : 0
                ];
                
                // الحصول على البيانات الحالية
                $current_match = getMatch($_POST['match_id']);
                $data['home_team_logo'] = $current_match['home_team_logo'];
                $data['away_team_logo'] = $current_match['away_team_logo'];
                
                // رفع شعارات جديدة إذا تم اختيارها
                if (isset($_FILES['home_team_logo']) && $_FILES['home_team_logo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadTeamLogo($_FILES['home_team_logo'], $_POST['home_team']);
                    if ($upload_result['success']) {
                        $data['home_team_logo'] = $upload_result['filepath'];
                    }
                }
                
                if (isset($_FILES['away_team_logo']) && $_FILES['away_team_logo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadTeamLogo($_FILES['away_team_logo'], $_POST['away_team']);
                    if ($upload_result['success']) {
                        $data['away_team_logo'] = $upload_result['filepath'];
                    }
                }
                
                if (updateMatch($_POST['match_id'], $data)) {
                    $success_message = 'تم تحديث المباراة بنجاح';
                } else {
                    $error_message = 'حدث خطأ أثناء تحديث المباراة';
                }
                break;
                
            case 'delete':
                if (deleteMatch($_POST['match_id'])) {
                    $success_message = 'تم حذف المباراة بنجاح';
                } else {
                    $error_message = 'حدث خطأ أثناء حذف المباراة';
                }
                break;
        }
    }
}

// الحصول على المباريات
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

$filters = [];
if (isset($_GET['status']) && !empty($_GET['status'])) {
    $filters['status'] = $_GET['status'];
}
if (isset($_GET['competition']) && !empty($_GET['competition'])) {
    $filters['competition'] = $_GET['competition'];
}

$matches = getMatches($per_page, $offset, $filters);
$total_matches = getTotalMatches($filters);
$total_pages = ceil($total_matches / $per_page);

// الحصول على البطولات
$competitions = getCompetitions();

// الحصول على الإحصائيات
$stats = getMatchesStats();

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <!-- العنوان والإحصائيات -->
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">
            <i class="fas fa-futbol ml-2 text-green-600"></i>
            إدارة المباريات
        </h1>
        <button onclick="openAddModal()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
            <i class="fas fa-plus ml-2"></i>إضافة مباراة جديدة
        </button>
    </div>

    <!-- الرسائل -->
    <?php if ($success_message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600"><?php echo $stats['total']; ?></div>
            <div class="text-sm text-blue-800">إجمالي المباريات</div>
        </div>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-yellow-600"><?php echo $stats['upcoming']; ?></div>
            <div class="text-sm text-yellow-800">مباريات قادمة</div>
        </div>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-red-600"><?php echo $stats['live']; ?></div>
            <div class="text-sm text-red-800">مباريات جارية</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600"><?php echo $stats['finished']; ?></div>
            <div class="text-sm text-green-800">مباريات منتهية</div>
        </div>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-600"><?php echo $stats['featured']; ?></div>
            <div class="text-sm text-purple-800">مباريات مميزة</div>
        </div>
    </div>

    <!-- الفلاتر -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">حالة المباراة</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="scheduled" <?php echo (isset($_GET['status']) && $_GET['status'] === 'scheduled') ? 'selected' : ''; ?>>مجدولة</option>
                    <option value="live" <?php echo (isset($_GET['status']) && $_GET['status'] === 'live') ? 'selected' : ''; ?>>جارية</option>
                    <option value="finished" <?php echo (isset($_GET['status']) && $_GET['status'] === 'finished') ? 'selected' : ''; ?>>انتهت</option>
                    <option value="postponed" <?php echo (isset($_GET['status']) && $_GET['status'] === 'postponed') ? 'selected' : ''; ?>>مؤجلة</option>
                    <option value="cancelled" <?php echo (isset($_GET['status']) && $_GET['status'] === 'cancelled') ? 'selected' : ''; ?>>ملغية</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">البطولة</label>
                <select name="competition" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع البطولات</option>
                    <?php foreach ($competitions as $comp): ?>
                    <option value="<?php echo $comp['name']; ?>" <?php echo (isset($_GET['competition']) && $_GET['competition'] === $comp['name']) ? 'selected' : ''; ?>>
                        <?php echo $comp['name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors ml-2">
                    <i class="fas fa-search ml-1"></i>فلترة
                </button>
                <a href="matches.php" class="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-times ml-1"></i>مسح
                </a>
            </div>
        </form>
    </div>

    <!-- جدول المباريات -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <?php if (!empty($matches)): ?>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المباراة</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">النتيجة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البطولة</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($matches as $match): ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <?php if ($match['home_team_logo']): ?>
                                <img src="../<?php echo $match['home_team_logo']; ?>" alt="<?php echo $match['home_team']; ?>" class="w-8 h-8 ml-2">
                                <?php endif; ?>
                                <span class="font-medium"><?php echo $match['home_team']; ?></span>
                                <span class="mx-2 text-gray-400">vs</span>
                                <span class="font-medium"><?php echo $match['away_team']; ?></span>
                                <?php if ($match['away_team_logo']): ?>
                                <img src="../<?php echo $match['away_team_logo']; ?>" alt="<?php echo $match['away_team']; ?>" class="w-8 h-8 mr-2">
                                <?php endif; ?>
                                <?php if ($match['is_featured']): ?>
                                <i class="fas fa-star text-yellow-500 mr-2" title="مباراة مميزة"></i>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-center text-sm">
                            <?php echo formatMatchDate($match['match_date']); ?>
                        </td>
                        <td class="px-6 py-4 text-center">
                            <?php if ($match['status'] === 'finished' || $match['status'] === 'live'): ?>
                            <span class="font-bold"><?php echo $match['home_score'] ?? 0; ?> - <?php echo $match['away_score'] ?? 0; ?></span>
                            <?php else: ?>
                            <span class="text-gray-500">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 text-sm">
                            <?php echo $match['competition']; ?>
                        </td>
                        <td class="px-6 py-4 text-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo getMatchStatusColor($match['status']); ?>">
                                <?php echo getMatchStatusArabic($match['status']); ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-center">
                            <button onclick="editMatch(<?php echo htmlspecialchars(json_encode($match)); ?>)" 
                                    class="text-blue-600 hover:text-blue-900 ml-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteMatch(<?php echo $match['id']; ?>)" 
                                    class="text-red-600 hover:text-red-900">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">السابق</a>
                <?php endif; ?>
                <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">التالي</a>
                <?php endif; ?>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        عرض <span class="font-medium"><?php echo ($page - 1) * $per_page + 1; ?></span> إلى 
                        <span class="font-medium"><?php echo min($page * $per_page, $total_matches); ?></span> من 
                        <span class="font-medium"><?php echo $total_matches; ?></span> نتيجة
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <a href="?page=<?php echo $i; ?>" 
                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?php echo $i === $page ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'; ?>">
                            <?php echo $i; ?>
                        </a>
                        <?php endfor; ?>
                    </nav>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <div class="text-center py-12">
            <i class="fas fa-futbol text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد مباريات</h3>
            <p class="text-gray-500">لم يتم إضافة أي مباريات بعد</p>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal إضافة مباراة -->
<div id="addModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">إضافة مباراة جديدة</h3>
                <button onclick="closeAddModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" name="action" value="add">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الفريق المضيف</label>
                        <input type="text" name="home_team" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الفريق الضيف</label>
                        <input type="text" name="away_team" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">شعار الفريق المضيف</label>
                        <input type="file" name="home_team_logo" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">شعار الفريق الضيف</label>
                        <input type="file" name="away_team_logo" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ ووقت المباراة</label>
                        <input type="datetime-local" name="match_date" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">البطولة</label>
                        <select name="competition" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">اختر البطولة</option>
                            <?php foreach ($competitions as $comp): ?>
                            <option value="<?php echo $comp['name']; ?>"><?php echo $comp['name']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الملعب</label>
                        <input type="text" name="venue"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">حالة المباراة</label>
                        <select name="status"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="scheduled">مجدولة</option>
                            <option value="live">جارية</option>
                            <option value="finished">انتهت</option>
                            <option value="postponed">مؤجلة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نتيجة الفريق المضيف</label>
                        <input type="number" name="home_score" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نتيجة الفريق الضيف</label>
                        <input type="number" name="away_score" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">وقت المباراة</label>
                        <input type="text" name="match_time" placeholder="90+2"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">وصف أو ملاحظات</label>
                    <textarea name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="is_featured" id="is_featured"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_featured" class="mr-2 block text-sm text-gray-900">مباراة مميزة</label>
                </div>

                <div class="flex justify-end space-x-2 space-x-reverse pt-4">
                    <button type="button" onclick="closeAddModal()"
                            class="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors">
                        إلغاء
                    </button>
                    <button type="submit"
                            class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors">
                        إضافة المباراة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل مباراة -->
<div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">تعديل المباراة</h3>
                <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="match_id" id="edit_match_id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الفريق المضيف</label>
                        <input type="text" name="home_team" id="edit_home_team" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الفريق الضيف</label>
                        <input type="text" name="away_team" id="edit_away_team" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">شعار الفريق المضيف</label>
                        <input type="file" name="home_team_logo" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <div id="current_home_logo" class="mt-2"></div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">شعار الفريق الضيف</label>
                        <input type="file" name="away_team_logo" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <div id="current_away_logo" class="mt-2"></div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ ووقت المباراة</label>
                        <input type="datetime-local" name="match_date" id="edit_match_date" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">البطولة</label>
                        <select name="competition" id="edit_competition" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">اختر البطولة</option>
                            <?php foreach ($competitions as $comp): ?>
                            <option value="<?php echo $comp['name']; ?>"><?php echo $comp['name']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الملعب</label>
                        <input type="text" name="venue" id="edit_venue"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">حالة المباراة</label>
                        <select name="status" id="edit_status"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="scheduled">مجدولة</option>
                            <option value="live">جارية</option>
                            <option value="finished">انتهت</option>
                            <option value="postponed">مؤجلة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نتيجة الفريق المضيف</label>
                        <input type="number" name="home_score" id="edit_home_score" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نتيجة الفريق الضيف</label>
                        <input type="number" name="away_score" id="edit_away_score" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">وقت المباراة</label>
                        <input type="text" name="match_time" id="edit_match_time" placeholder="90+2"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">وصف أو ملاحظات</label>
                    <textarea name="description" id="edit_description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="is_featured" id="edit_is_featured"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="edit_is_featured" class="mr-2 block text-sm text-gray-900">مباراة مميزة</label>
                </div>

                <div class="flex justify-end space-x-2 space-x-reverse pt-4">
                    <button type="button" onclick="closeEditModal()"
                            class="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors">
                        إلغاء
                    </button>
                    <button type="submit"
                            class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        تحديث المباراة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// فتح modal الإضافة
function openAddModal() {
    document.getElementById('addModal').classList.remove('hidden');
}

// إغلاق modal الإضافة
function closeAddModal() {
    document.getElementById('addModal').classList.add('hidden');
}

// فتح modal التعديل
function editMatch(match) {
    document.getElementById('edit_match_id').value = match.id;
    document.getElementById('edit_home_team').value = match.home_team;
    document.getElementById('edit_away_team').value = match.away_team;
    document.getElementById('edit_match_date').value = match.match_date.replace(' ', 'T');
    document.getElementById('edit_competition').value = match.competition;
    document.getElementById('edit_venue').value = match.venue || '';
    document.getElementById('edit_status').value = match.status;
    document.getElementById('edit_home_score').value = match.home_score || '';
    document.getElementById('edit_away_score').value = match.away_score || '';
    document.getElementById('edit_match_time').value = match.match_time || '';
    document.getElementById('edit_description').value = match.description || '';
    document.getElementById('edit_is_featured').checked = match.is_featured == 1;

    // عرض الشعارات الحالية
    const homeLogoDiv = document.getElementById('current_home_logo');
    const awayLogoDiv = document.getElementById('current_away_logo');

    if (match.home_team_logo) {
        homeLogoDiv.innerHTML = `<img src="../${match.home_team_logo}" alt="${match.home_team}" class="w-16 h-16 object-contain">`;
    } else {
        homeLogoDiv.innerHTML = '<span class="text-gray-500 text-sm">لا يوجد شعار</span>';
    }

    if (match.away_team_logo) {
        awayLogoDiv.innerHTML = `<img src="../${match.away_team_logo}" alt="${match.away_team}" class="w-16 h-16 object-contain">`;
    } else {
        awayLogoDiv.innerHTML = '<span class="text-gray-500 text-sm">لا يوجد شعار</span>';
    }

    document.getElementById('editModal').classList.remove('hidden');
}

// إغلاق modal التعديل
function closeEditModal() {
    document.getElementById('editModal').classList.add('hidden');
}

// حذف مباراة
function deleteMatch(matchId) {
    if (confirm('هل أنت متأكد من حذف هذه المباراة؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="match_id" value="${matchId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// إغلاق النماذج عند النقر خارجها
window.onclick = function(event) {
    const addModal = document.getElementById('addModal');
    const editModal = document.getElementById('editModal');

    if (event.target === addModal) {
        closeAddModal();
    }
    if (event.target === editModal) {
        closeEditModal();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
