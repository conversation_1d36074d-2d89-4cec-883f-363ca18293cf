<?php
// تحديد المسار الجذر
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'classes/RSSParser.php';

session_start();

$page_title = 'اختبار شامل للنظام';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-success { @apply bg-green-100 border border-green-400 text-green-700 p-3 rounded mb-2; }
        .test-error { @apply bg-red-100 border border-red-400 text-red-700 p-3 rounded mb-2; }
        .test-warning { @apply bg-yellow-100 border border-yellow-400 text-yellow-700 p-3 rounded mb-2; }
        .test-info { @apply bg-blue-100 border border-blue-400 text-blue-700 p-3 rounded mb-2; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
                <i class="fas fa-vial ml-2 text-blue-600"></i>
                اختبار شامل لنظام موقع الأخبار
            </h1>

            <?php
            $tests_passed = 0;
            $tests_failed = 0;
            $tests_total = 0;

            function runTest($test_name, $test_function) {
                global $tests_passed, $tests_failed, $tests_total;
                $tests_total++;
                
                echo "<div class='mb-4'>";
                echo "<h3 class='font-semibold text-lg mb-2'>$test_name</h3>";
                
                try {
                    $result = $test_function();
                    if ($result === true) {
                        echo "<div class='test-success'><i class='fas fa-check ml-2'></i>نجح الاختبار</div>";
                        $tests_passed++;
                    } elseif (is_string($result)) {
                        echo "<div class='test-success'><i class='fas fa-check ml-2'></i>$result</div>";
                        $tests_passed++;
                    } else {
                        echo "<div class='test-error'><i class='fas fa-times ml-2'></i>فشل الاختبار</div>";
                        $tests_failed++;
                    }
                } catch (Exception $e) {
                    echo "<div class='test-error'><i class='fas fa-times ml-2'></i>خطأ: " . $e->getMessage() . "</div>";
                    $tests_failed++;
                }
                
                echo "</div>";
            }

            // اختبار الاتصال بقاعدة البيانات
            runTest("اختبار الاتصال بقاعدة البيانات", function() {
                $database = new Database();
                $db = $database->connect();
                return $db ? "تم الاتصال بقاعدة البيانات بنجاح" : false;
            });

            // اختبار وجود الجداول
            runTest("اختبار وجود الجداول المطلوبة", function() {
                $database = new Database();
                $db = $database->connect();
                
                $required_tables = ['articles', 'categories', 'rss_sources', 'users', 'settings'];
                $existing_tables = [];
                
                foreach ($required_tables as $table) {
                    $stmt = $db->prepare("SHOW TABLES LIKE ?");
                    $stmt->execute([$table]);
                    if ($stmt->fetch()) {
                        $existing_tables[] = $table;
                    }
                }
                
                if (count($existing_tables) === count($required_tables)) {
                    return "جميع الجداول موجودة: " . implode(', ', $existing_tables);
                } else {
                    $missing = array_diff($required_tables, $existing_tables);
                    throw new Exception("جداول مفقودة: " . implode(', ', $missing));
                }
            });

            // اختبار وجود المستخدم الإداري
            runTest("اختبار وجود المستخدم الإداري", function() {
                $database = new Database();
                $db = $database->connect();
                
                $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
                $stmt->execute();
                $admin_count = $stmt->fetchColumn();
                
                return $admin_count > 0 ? "يوجد $admin_count مستخدم إداري" : false;
            });

            // اختبار الدوال المساعدة
            runTest("اختبار الدوال المساعدة", function() {
                if (!function_exists('getSetting')) {
                    throw new Exception("دالة getSetting غير موجودة");
                }
                
                if (!function_exists('createSlug')) {
                    throw new Exception("دالة createSlug غير موجودة");
                }
                
                if (!function_exists('formatArabicDate')) {
                    throw new Exception("دالة formatArabicDate غير موجودة");
                }
                
                // اختبار دالة createSlug
                $slug = createSlug("هذا اختبار للعنوان العربي");
                if (empty($slug)) {
                    throw new Exception("دالة createSlug لا تعمل بشكل صحيح");
                }
                
                return "جميع الدوال المساعدة تعمل بشكل صحيح";
            });

            // اختبار فئة RSSParser
            runTest("اختبار فئة RSSParser", function() {
                if (!class_exists('RSSParser')) {
                    throw new Exception("فئة RSSParser غير موجودة");
                }
                
                $parser = new RSSParser();
                
                if (!method_exists($parser, 'fetchRSSFeed')) {
                    throw new Exception("دالة fetchRSSFeed غير موجودة");
                }
                
                if (!method_exists($parser, 'testRSSFeed')) {
                    throw new Exception("دالة testRSSFeed غير موجودة");
                }
                
                if (!method_exists($parser, 'fetchAllActiveFeeds')) {
                    throw new Exception("دالة fetchAllActiveFeeds غير موجودة");
                }
                
                return "فئة RSSParser وجميع دوالها موجودة";
            });

            // اختبار ملفات لوحة الإدارة
            runTest("اختبار ملفات لوحة الإدارة", function() {
                $admin_files = [
                    'admin/dashboard.php',
                    'admin/articles.php',
                    'admin/categories.php',
                    'admin/rss-sources.php',
                    'admin/users.php',
                    'admin/settings.php',
                    'admin/login.php',
                    'admin/logout.php',
                    'admin/fetch-rss.php',
                    'admin/init-data.php'
                ];
                
                $missing_files = [];
                foreach ($admin_files as $file) {
                    if (!file_exists($file)) {
                        $missing_files[] = $file;
                    }
                }
                
                if (empty($missing_files)) {
                    return "جميع ملفات لوحة الإدارة موجودة (" . count($admin_files) . " ملف)";
                } else {
                    throw new Exception("ملفات مفقودة: " . implode(', ', $missing_files));
                }
            });

            // اختبار ملفات التضمين
            runTest("اختبار ملفات التضمين", function() {
                $include_files = [
                    'admin/includes/auth.php',
                    'admin/includes/header.php',
                    'admin/includes/footer.php',
                    'includes/header.php',
                    'includes/footer.php',
                    'includes/functions.php'
                ];
                
                $missing_files = [];
                foreach ($include_files as $file) {
                    if (!file_exists($file)) {
                        $missing_files[] = $file;
                    }
                }
                
                if (empty($missing_files)) {
                    return "جميع ملفات التضمين موجودة (" . count($include_files) . " ملف)";
                } else {
                    throw new Exception("ملفات مفقودة: " . implode(', ', $missing_files));
                }
            });

            // اختبار الصفحات الرئيسية
            runTest("اختبار الصفحات الرئيسية", function() {
                $main_files = [
                    'index.php',
                    'article.php',
                    'category.php',
                    'search.php',
                    'setup.php'
                ];
                
                $missing_files = [];
                foreach ($main_files as $file) {
                    if (!file_exists($file)) {
                        $missing_files[] = $file;
                    }
                }
                
                if (empty($missing_files)) {
                    return "جميع الصفحات الرئيسية موجودة (" . count($main_files) . " ملف)";
                } else {
                    throw new Exception("ملفات مفقودة: " . implode(', ', $missing_files));
                }
            });

            // اختبار الأذونات
            runTest("اختبار أذونات المجلدات", function() {
                $directories = ['logs', 'config', 'admin', 'includes', 'classes'];
                $issues = [];
                
                foreach ($directories as $dir) {
                    if (!is_dir($dir)) {
                        $issues[] = "المجلد $dir غير موجود";
                    } elseif (!is_readable($dir)) {
                        $issues[] = "المجلد $dir غير قابل للقراءة";
                    }
                }
                
                if (empty($issues)) {
                    return "جميع المجلدات لها الأذونات الصحيحة";
                } else {
                    throw new Exception(implode(', ', $issues));
                }
            });

            // اختبار إعدادات PHP
            runTest("اختبار إعدادات PHP المطلوبة", function() {
                $required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'curl'];
                $missing_extensions = [];
                
                foreach ($required_extensions as $ext) {
                    if (!extension_loaded($ext)) {
                        $missing_extensions[] = $ext;
                    }
                }
                
                if (empty($missing_extensions)) {
                    return "جميع امتدادات PHP المطلوبة متوفرة";
                } else {
                    throw new Exception("امتدادات مفقودة: " . implode(', ', $missing_extensions));
                }
            });
            ?>

            <!-- نتائج الاختبار -->
            <div class="mt-8 p-6 bg-gray-50 rounded-lg">
                <h2 class="text-2xl font-bold mb-4">نتائج الاختبار</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-green-100 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold text-green-800"><?php echo $tests_passed; ?></div>
                        <div class="text-green-600">اختبارات نجحت</div>
                    </div>
                    
                    <div class="bg-red-100 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold text-red-800"><?php echo $tests_failed; ?></div>
                        <div class="text-red-600">اختبارات فشلت</div>
                    </div>
                    
                    <div class="bg-blue-100 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold text-blue-800"><?php echo $tests_total; ?></div>
                        <div class="text-blue-600">إجمالي الاختبارات</div>
                    </div>
                </div>

                <?php
                $success_rate = $tests_total > 0 ? round(($tests_passed / $tests_total) * 100, 1) : 0;
                
                if ($success_rate >= 90) {
                    $status_class = "bg-green-100 border-green-500 text-green-700";
                    $status_icon = "fas fa-check-circle text-green-600";
                    $status_message = "ممتاز! النظام جاهز للاستخدام";
                } elseif ($success_rate >= 70) {
                    $status_class = "bg-yellow-100 border-yellow-500 text-yellow-700";
                    $status_icon = "fas fa-exclamation-triangle text-yellow-600";
                    $status_message = "جيد، لكن هناك بعض المشاكل التي تحتاج إلى إصلاح";
                } else {
                    $status_class = "bg-red-100 border-red-500 text-red-700";
                    $status_icon = "fas fa-times-circle text-red-600";
                    $status_message = "يحتاج النظام إلى إصلاحات قبل الاستخدام";
                }
                ?>

                <div class="<?php echo $status_class; ?> border-l-4 p-4 rounded">
                    <div class="flex items-center">
                        <i class="<?php echo $status_icon; ?> text-2xl ml-3"></i>
                        <div>
                            <h3 class="font-bold text-lg">معدل النجاح: <?php echo $success_rate; ?>%</h3>
                            <p><?php echo $status_message; ?></p>
                        </div>
                    </div>
                </div>

                <div class="mt-6 text-center">
                    <a href="index.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors ml-4">
                        <i class="fas fa-home ml-2"></i>الصفحة الرئيسية
                    </a>
                    <a href="admin/login.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-cog ml-2"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
