/*
 * Components - المكونات الأساسية
 * ملف CSS للمكونات المشتركة في الموقع
 */

/* ==========================================================================
   Header Components - مكونات الهيدر
   ========================================================================== */

.site-header {
    background: white;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: all var(--transition-base);
}

.site-header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
}

.site-logo {
    display: flex;
    align-items: center;
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    text-decoration: none;
}

.site-logo:hover {
    color: var(--primary-700);
}

.site-logo i {
    margin-left: var(--space-2);
    font-size: var(--text-2xl);
}

/* Navigation - التنقل */
.main-nav {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.nav-link {
    color: var(--neutral-700);
    font-weight: var(--font-medium);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-600);
    background-color: var(--primary-50);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--primary-600);
    transition: all var(--transition-fast);
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Mobile Navigation - التنقل المحمول */
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--text-xl);
    color: var(--neutral-700);
    cursor: pointer;
    padding: var(--space-2);
}

@media (max-width: 767px) {
    .mobile-nav-toggle {
        display: block;
    }
    
    .main-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: var(--shadow-lg);
        flex-direction: column;
        padding: var(--space-4);
        gap: var(--space-2);
    }
    
    .main-nav.active {
        display: flex;
    }
    
    .nav-link {
        width: 100%;
        text-align: center;
        padding: var(--space-3);
    }
}

/* Breaking News Ticker - شريط الأخبار العاجلة */
.breaking-news {
    background: linear-gradient(135deg, var(--error-600) 0%, var(--error-700) 100%);
    color: white;
    padding: var(--space-2) 0;
    overflow: hidden;
    position: relative;
}

.breaking-news-content {
    display: flex;
    align-items: center;
    animation: scroll-rtl 30s linear infinite;
}

.breaking-news-label {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-weight: var(--font-bold);
    font-size: var(--text-sm);
    margin-left: var(--space-4);
    white-space: nowrap;
}

@keyframes scroll-rtl {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* ==========================================================================
   News Cards - بطاقات الأخبار
   ========================================================================== */

.news-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-base);
    overflow: hidden;
    transition: all var(--transition-base);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.news-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
}

.news-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition-base);
}

.news-card:hover .news-card-image {
    transform: scale(1.05);
}

.news-card-content {
    padding: var(--space-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-3);
}

.news-card-category {
    background: var(--primary-100);
    color: var(--primary-800);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
}

.news-card-date {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.news-card-title {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-3);
    color: var(--neutral-900);
}

.news-card-title a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.news-card-title a:hover {
    color: var(--primary-600);
}

.news-card-excerpt {
    color: var(--neutral-600);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--space-3);
    border-top: 1px solid var(--neutral-200);
    margin-top: auto;
}

.news-card-views {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.news-card-read-more {
    color: var(--primary-600);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-1);
    transition: color var(--transition-fast);
}

.news-card-read-more:hover {
    color: var(--primary-700);
}

/* Featured News Card - بطاقة الخبر المميز */
.news-card-featured {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    border: 2px solid var(--primary-200);
    position: relative;
}

.news-card-featured::before {
    content: '⭐';
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    font-size: var(--text-lg);
    z-index: 1;
}

/* ==========================================================================
   Sidebar Components - مكونات الشريط الجانبي
   ========================================================================== */

.sidebar-widget {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-base);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
}

.sidebar-widget-title {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid var(--primary-100);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.sidebar-widget-title i {
    color: var(--primary-600);
}

.sidebar-article {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.sidebar-article:hover {
    background-color: var(--neutral-50);
}

.sidebar-article-image {
    width: 60px;
    height: 45px;
    border-radius: var(--radius-md);
    object-fit: cover;
    flex-shrink: 0;
}

.sidebar-article-content {
    flex: 1;
    min-width: 0;
}

.sidebar-article-title {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-1);
    color: var(--neutral-900);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.sidebar-article-title a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.sidebar-article-title a:hover {
    color: var(--primary-600);
}

.sidebar-article-meta {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* ==========================================================================
   Footer Components - مكونات الفوتر
   ========================================================================== */

.site-footer {
    background: var(--neutral-800);
    color: var(--neutral-200);
    padding: var(--space-16) 0 var(--space-8);
    margin-top: var(--space-16);
}

.footer-section-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: white;
    margin-bottom: var(--space-4);
}

.footer-link {
    color: var(--neutral-300);
    text-decoration: none;
    transition: color var(--transition-fast);
    display: block;
    padding: var(--space-1) 0;
}

.footer-link:hover {
    color: white;
}

.footer-social-links {
    display: flex;
    gap: var(--space-4);
    margin-top: var(--space-4);
}

.footer-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--neutral-700);
    color: var(--neutral-300);
    border-radius: var(--radius-full);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.footer-social-link:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.footer-copyright {
    border-top: 1px solid var(--neutral-700);
    margin-top: var(--space-8);
    padding-top: var(--space-8);
    text-align: center;
    color: var(--neutral-400);
}

/* ==========================================================================
   Form Components - مكونات النماذج
   ========================================================================== */

.form-group {
    margin-bottom: var(--space-4);
}

.form-label {
    display: block;
    font-weight: var(--font-medium);
    color: var(--neutral-700);
    margin-bottom: var(--space-2);
}

.form-input {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: all var(--transition-fast);
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:invalid {
    border-color: var(--error-500);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

/* ==========================================================================
   Utility Components - المكونات المساعدة
   ========================================================================== */

.back-to-top {
    position: fixed;
    bottom: var(--space-6);
    right: var(--space-6);
    width: 50px;
    height: 50px;
    background: var(--primary-600);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-base);
    opacity: 0;
    visibility: hidden;
    z-index: var(--z-fixed);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-700);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Loading Spinner - مؤشر التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--neutral-200);
    border-radius: 50%;
    border-top-color: var(--primary-600);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Breadcrumb - مسار التنقل */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--neutral-500);
    margin-bottom: var(--space-6);
}

.breadcrumb-link {
    color: var(--neutral-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb-link:hover {
    color: var(--primary-600);
}

.breadcrumb-separator {
    color: var(--neutral-400);
}

/* Pagination - ترقيم الصفحات */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    margin: var(--space-8) 0;
}

.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    color: var(--neutral-700);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.pagination-link:hover,
.pagination-link.active {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}

.pagination-link:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ==========================================================================
   Responsive Adjustments - التعديلات المتجاوبة
   ========================================================================== */

/* ==========================================================================
   Matches Widget Integration - تكامل جدول المباريات
   ========================================================================== */

.matches-widget {
    background: linear-gradient(135deg, var(--success-600) 0%, var(--primary-600) 100%);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    margin: var(--space-8) 0;
}

.matches-widget .widget-header {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--primary-500) 100%);
    padding: var(--space-6);
    color: white;
}

.match-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    border: 1px solid var(--neutral-200);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.match-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-300);
}

.match-date-badge {
    background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
    color: var(--primary-800);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}

.vs-indicator {
    background: linear-gradient(135deg, var(--neutral-200) 0%, var(--neutral-300) 100%);
    color: var(--neutral-700);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-weight: var(--font-bold);
    font-size: var(--text-xs);
    box-shadow: var(--shadow-sm);
}

.team-name {
    font-weight: var(--font-semibold);
    color: var(--neutral-800);
    font-size: var(--text-sm);
    transition: color var(--transition-fast);
}

.match-card:hover .team-name {
    color: var(--primary-600);
}

.featured-match {
    background: linear-gradient(135deg, var(--warning-50) 0%, var(--warning-100) 100%);
    border-color: var(--warning-300);
    position: relative;
}

.featured-indicator {
    background: linear-gradient(135deg, var(--warning-200) 0%, var(--warning-300) 100%);
    color: var(--warning-800);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}

/* ==========================================================================
   Responsive Adjustments - التعديلات المتجاوبة
   ========================================================================== */

@media (max-width: 767px) {
    .news-card-content {
        padding: var(--space-4);
    }

    .sidebar-widget {
        padding: var(--space-4);
        margin-bottom: var(--space-4);
    }

    .back-to-top {
        bottom: var(--space-4);
        right: var(--space-4);
        width: 45px;
        height: 45px;
    }

    .matches-widget .widget-header {
        padding: var(--space-4);
    }

    .match-card {
        padding: var(--space-4);
    }
}
