<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

checkAuth();

// التحقق من صلاحيات المدير
if ($_SESSION['user_role'] !== 'admin') {
    header("Location: dashboard.php");
    exit();
}

$page_title = 'إضافة البيانات الأولية';
$database = new Database();
$db = $database->connect();

$success_message = '';
$error_message = '';

// إضافة البيانات الأولية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['init_data'])) {
    try {
        // إضافة التصنيفات الافتراضية
        $categories = [
            ['name' => 'أخبار عامة', 'description' => 'الأخبار العامة والمتنوعة'],
            ['name' => 'تقنية', 'description' => 'أخبار التقنية والتكنولوجيا'],
            ['name' => 'رياضة', 'description' => 'الأخبار الرياضية'],
            ['name' => 'اقتصاد', 'description' => 'الأخبار الاقتصادية والمالية'],
            ['name' => 'سياسة', 'description' => 'الأخبار السياسية'],
            ['name' => 'صحة', 'description' => 'أخبار الصحة والطب'],
            ['name' => 'ثقافة', 'description' => 'الأخبار الثقافية والفنية'],
            ['name' => 'علوم', 'description' => 'أخبار العلوم والاكتشافات']
        ];
        
        foreach ($categories as $category) {
            $slug = createSlug($category['name']);
            $stmt = $db->prepare("INSERT IGNORE INTO categories (name, slug, description) VALUES (?, ?, ?)");
            $stmt->execute([$category['name'], $slug, $category['description']]);
        }
        
        // إضافة مصادر RSS افتراضية
        $rss_sources = [
            [
                'name' => 'الجزيرة نت',
                'url' => 'https://www.aljazeera.net/rss/all.xml',
                'category' => 'أخبار عامة'
            ],
            [
                'name' => 'العربية نت',
                'url' => 'https://www.alarabiya.net/rss.xml',
                'category' => 'أخبار عامة'
            ],
            [
                'name' => 'BBC العربية',
                'url' => 'https://feeds.bbci.co.uk/arabic/rss.xml',
                'category' => 'أخبار عامة'
            ],
            [
                'name' => 'سكاي نيوز عربية',
                'url' => 'https://www.skynewsarabia.com/rss',
                'category' => 'أخبار عامة'
            ]
        ];
        
        foreach ($rss_sources as $source) {
            // الحصول على معرف التصنيف
            $stmt = $db->prepare("SELECT id FROM categories WHERE name = ?");
            $stmt->execute([$source['category']]);
            $category_id = $stmt->fetchColumn();
            
            $stmt = $db->prepare("INSERT IGNORE INTO rss_sources (name, url, category_id, is_active) VALUES (?, ?, ?, 1)");
            $stmt->execute([$source['name'], $source['url'], $category_id]);
        }
        
        // إضافة مقالات تجريبية
        $sample_articles = [
            [
                'title' => 'مرحباً بكم في موقع الأخبار الجديد',
                'content' => 'هذا مقال تجريبي لاختبار النظام. يمكنكم حذف هذا المقال وإضافة مقالاتكم الخاصة. النظام يدعم إدارة المقالات والتصنيفات ومصادر RSS بشكل كامل.',
                'excerpt' => 'مقال ترحيبي في موقع الأخبار الجديد',
                'category' => 'أخبار عامة',
                'author' => 'إدارة الموقع',
                'is_featured' => 1
            ],
            [
                'title' => 'كيفية استخدام لوحة التحكم',
                'content' => 'لوحة التحكم تتيح لك إدارة جميع جوانب الموقع بسهولة. يمكنك إضافة وتعديل المقالات، إدارة التصنيفات، إضافة مصادر RSS، وإدارة المستخدمين. كما يمكنك تخصيص إعدادات الموقع من خلال صفحة الإعدادات.',
                'excerpt' => 'دليل استخدام لوحة التحكم',
                'category' => 'تقنية',
                'author' => 'إدارة الموقع',
                'is_featured' => 0
            ]
        ];
        
        foreach ($sample_articles as $article) {
            // الحصول على معرف التصنيف
            $stmt = $db->prepare("SELECT id FROM categories WHERE name = ?");
            $stmt->execute([$article['category']]);
            $category_id = $stmt->fetchColumn();
            
            $slug = createSlug($article['title']);
            
            $stmt = $db->prepare("
                INSERT IGNORE INTO articles (title, slug, content, excerpt, category_id, author, is_featured, published_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $article['title'], 
                $slug, 
                $article['content'], 
                $article['excerpt'], 
                $category_id, 
                $article['author'], 
                $article['is_featured']
            ]);
        }
        
        $success_message = 'تم إضافة البيانات الأولية بنجاح! تم إضافة التصنيفات ومصادر RSS والمقالات التجريبية.';
        
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء إضافة البيانات: ' . $e->getMessage();
    }
}

// التحقق من وجود البيانات
$stmt = $db->query("SELECT COUNT(*) FROM categories");
$categories_count = $stmt->fetchColumn();

$stmt = $db->query("SELECT COUNT(*) FROM rss_sources");
$rss_count = $stmt->fetchColumn();

$stmt = $db->query("SELECT COUNT(*) FROM articles");
$articles_count = $stmt->fetchColumn();

include 'includes/header.php';
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">إضافة البيانات الأولية</h1>
        <a href="dashboard.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            <i class="fas fa-arrow-right ml-2"></i>العودة للوحة التحكم
        </a>
    </div>

    <!-- Messages -->
    <?php if ($success_message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <!-- Current Status -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold mb-4">الحالة الحالية للبيانات</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-600 text-sm font-medium">التصنيفات</p>
                        <p class="text-2xl font-bold text-blue-800"><?php echo $categories_count; ?></p>
                    </div>
                    <i class="fas fa-folder text-blue-600 text-2xl"></i>
                </div>
            </div>
            
            <div class="bg-purple-50 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-600 text-sm font-medium">مصادر RSS</p>
                        <p class="text-2xl font-bold text-purple-800"><?php echo $rss_count; ?></p>
                    </div>
                    <i class="fas fa-rss text-purple-600 text-2xl"></i>
                </div>
            </div>
            
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-600 text-sm font-medium">المقالات</p>
                        <p class="text-2xl font-bold text-green-800"><?php echo $articles_count; ?></p>
                    </div>
                    <i class="fas fa-newspaper text-green-600 text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Initialize Data -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold mb-4">إضافة البيانات الأولية</h2>
        
        <div class="mb-6">
            <p class="text-gray-600 mb-4">
                سيتم إضافة البيانات التالية إلى موقعك:
            </p>
            
            <div class="space-y-4">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h3 class="font-medium text-gray-800">التصنيفات (8 تصنيفات)</h3>
                    <p class="text-sm text-gray-600">أخبار عامة، تقنية، رياضة، اقتصاد، سياسة، صحة، ثقافة، علوم</p>
                </div>
                
                <div class="border-l-4 border-purple-500 pl-4">
                    <h3 class="font-medium text-gray-800">مصادر RSS (4 مصادر)</h3>
                    <p class="text-sm text-gray-600">الجزيرة نت، العربية نت، BBC العربية، سكاي نيوز عربية</p>
                </div>
                
                <div class="border-l-4 border-green-500 pl-4">
                    <h3 class="font-medium text-gray-800">مقالات تجريبية (2 مقالات)</h3>
                    <p class="text-sm text-gray-600">مقالات ترحيبية ودليل الاستخدام</p>
                </div>
            </div>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-600 mt-1 ml-2"></i>
                <div>
                    <h4 class="font-medium text-yellow-800">ملاحظة مهمة</h4>
                    <p class="text-sm text-yellow-700 mt-1">
                        هذه العملية آمنة ولن تحذف أي بيانات موجودة. سيتم إضافة البيانات الجديدة فقط إذا لم تكن موجودة مسبقاً.
                    </p>
                </div>
            </div>
        </div>
        
        <form method="POST">
            <button type="submit" name="init_data" value="1" 
                    class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus ml-2"></i>إضافة البيانات الأولية
            </button>
        </form>
    </div>

    <!-- Next Steps -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold mb-4">الخطوات التالية</h2>
        
        <div class="space-y-3">
            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <i class="fas fa-check-circle text-green-600 ml-3"></i>
                <span>قم بزيارة <a href="rss-sources.php" class="text-blue-600 hover:underline">إدارة مصادر RSS</a> لجلب أول مجموعة من الأخبار</span>
            </div>
            
            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <i class="fas fa-check-circle text-green-600 ml-3"></i>
                <span>اذهب إلى <a href="settings.php" class="text-blue-600 hover:underline">الإعدادات</a> لتخصيص معلومات موقعك</span>
            </div>
            
            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <i class="fas fa-check-circle text-green-600 ml-3"></i>
                <span>ابدأ بإضافة <a href="articles.php?action=add" class="text-blue-600 hover:underline">مقالاتك الخاصة</a></span>
            </div>
            
            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <i class="fas fa-check-circle text-green-600 ml-3"></i>
                <span>تصفح <a href="../index.php" target="_blank" class="text-blue-600 hover:underline">الموقع الرئيسي</a> لرؤية النتيجة</span>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
