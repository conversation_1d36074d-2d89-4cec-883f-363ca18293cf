<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

checkAuth();

$page_title = 'إدارة المقالات';
$database = new Database();
$db = $database->connect();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$article_id = $_GET['id'] ?? null;
$edit_id = $_GET['edit'] ?? null;

$success_message = '';
$error_message = '';

// معالج AJAX لتحميل المقالات
if (isset($_GET['ajax']) && $_GET['ajax'] === 'load_articles') {
    $page = max(1, (int)($_GET['page'] ?? 1));
    $search = trim($_GET['search'] ?? '');
    $category_filter = $_GET['category'] ?? '';
    $per_page = 10;

    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = "(a.title LIKE ? OR a.content LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if (!empty($category_filter)) {
        $where_conditions[] = "a.category_id = ?";
        $params[] = $category_filter;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // عدد المقالات الإجمالي
    $count_sql = "SELECT COUNT(*) FROM articles a $where_clause";
    $stmt = $db->prepare($count_sql);
    $stmt->execute($params);
    $total_articles = $stmt->fetchColumn();
    $total_pages = ceil($total_articles / $per_page);

    // جلب المقالات
    $offset = ($page - 1) * $per_page;
    $sql = "SELECT a.*, c.name as category_name, r.name as source_name
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            LEFT JOIN rss_sources r ON a.rss_source_id = r.id
            $where_clause
            ORDER BY a.created_at DESC
            LIMIT $per_page OFFSET $offset";

    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إنشاء HTML للجدول
    $html = '';
    foreach ($articles as $article) {
        $featured_badge = $article['is_featured'] ? '<span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">مميز</span>' : '';
        $category_name = $article['category_name'] ?? 'بدون تصنيف';
        $source_badge = $article['source_name'] ? '<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">RSS</span>' : '';

        $html .= '<tr>';
        $html .= '<td class="px-6 py-4 whitespace-nowrap">';
        $html .= '<div class="flex items-center">';
        $html .= '<div>';
        $html .= '<div class="text-sm font-medium text-gray-900">' . htmlspecialchars($article['title']) . '</div>';
        $html .= '<div class="text-sm text-gray-500">' . $featured_badge . ' ' . $source_badge . '</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</td>';
        $html .= '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' . $category_name . '</td>';
        $html .= '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' . ($article['author'] ?: 'غير محدد') . '</td>';
        $html .= '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' . number_format($article['views']) . '</td>';
        $html .= '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' . formatArabicDate($article['published_at']) . '</td>';
        $html .= '<td class="px-6 py-4 whitespace-nowrap">';
        if ($article['is_featured']) {
            $html .= '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">مميز</span>';
        } else {
            $html .= '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">عادي</span>';
        }
        $html .= '</td>';
        $html .= '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">';
        $html .= '<div class="flex space-x-2 space-x-reverse">';
        $html .= '<a href="?edit=' . $article['id'] . '" class="text-blue-600 hover:text-blue-900">تعديل</a>';
        $html .= '<a href="?action=toggle_featured&id=' . $article['id'] . '" class="text-yellow-600 hover:text-yellow-900">' . ($article['is_featured'] ? 'إلغاء التمييز' : 'تمييز') . '</a>';
        $html .= '<a href="?action=delete&id=' . $article['id'] . '" onclick="return confirm(\'هل أنت متأكد من حذف هذا المقال؟\')" class="text-red-600 hover:text-red-900">حذف</a>';
        $html .= '</div>';
        $html .= '</td>';
        $html .= '</tr>';
    }

    // إنشاء HTML للتصفح
    $pagination = '';
    if ($total_pages > 1) {
        $pagination .= '<div class="flex items-center justify-between">';
        $pagination .= '<div class="text-sm text-gray-700">عرض ' . (($page - 1) * $per_page + 1) . ' إلى ' . min($page * $per_page, $total_articles) . ' من ' . $total_articles . ' مقال</div>';
        $pagination .= '<div class="flex space-x-1 space-x-reverse">';

        if ($page > 1) {
            $pagination .= '<button onclick="loadArticles(' . ($page - 1) . ', document.getElementById(\'searchInput\').value, document.getElementById(\'categoryFilter\').value)" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">السابق</button>';
        }

        for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++) {
            $active_class = $i == $page ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50';
            $pagination .= '<button onclick="loadArticles(' . $i . ', document.getElementById(\'searchInput\').value, document.getElementById(\'categoryFilter\').value)" class="px-3 py-2 text-sm border border-gray-300 rounded-md ' . $active_class . '">' . $i . '</button>';
        }

        if ($page < $total_pages) {
            $pagination .= '<button onclick="loadArticles(' . ($page + 1) . ', document.getElementById(\'searchInput\').value, document.getElementById(\'categoryFilter\').value)" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">التالي</button>';
        }

        $pagination .= '</div>';
        $pagination .= '</div>';
    }

    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'html' => $html,
        'pagination' => $pagination
    ]);
    exit;
}

// حذف مقال
if ($action === 'delete' && $article_id) {
    try {
        $stmt = $db->prepare("DELETE FROM articles WHERE id = ?");
        if ($stmt->execute([$article_id])) {
            $success_message = 'تم حذف المقال بنجاح';
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حذف المقال';
    }
    $action = 'list';
}

// تبديل حالة المقال المميز
if ($action === 'toggle_featured' && $article_id) {
    try {
        $stmt = $db->prepare("UPDATE articles SET is_featured = NOT is_featured WHERE id = ?");
        if ($stmt->execute([$article_id])) {
            $success_message = 'تم تحديث حالة المقال المميز';
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء تحديث المقال';
    }
    $action = 'list';
}

// إضافة أو تعديل مقال
if ($_SERVER['REQUEST_METHOD'] === 'POST' && in_array($action, ['add', 'edit'])) {
    $title = trim($_POST['title']);
    $content = trim($_POST['content']);
    $excerpt = trim($_POST['excerpt']);
    $category_id = $_POST['category_id'] ?: null;
    $author = trim($_POST['author']);
    $image_url = trim($_POST['image_url']);
    $source_url = trim($_POST['source_url']);
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $published_at = $_POST['published_at'] ?: date('Y-m-d H:i:s');
    
    if (!empty($title) && !empty($content)) {
        $slug = createSlug($title);
        
        try {
            if ($action === 'add') {
                $stmt = $db->prepare("
                    INSERT INTO articles (title, slug, content, excerpt, category_id, author, image_url, source_url, is_featured, published_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $result = $stmt->execute([$title, $slug, $content, $excerpt, $category_id, $author, $image_url, $source_url, $is_featured, $published_at]);
                
                if ($result) {
                    $success_message = 'تم إضافة المقال بنجاح';
                    $action = 'list';
                }
            } elseif ($action === 'edit' && $edit_id) {
                $stmt = $db->prepare("
                    UPDATE articles 
                    SET title = ?, slug = ?, content = ?, excerpt = ?, category_id = ?, author = ?, image_url = ?, source_url = ?, is_featured = ?, published_at = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $result = $stmt->execute([$title, $slug, $content, $excerpt, $category_id, $author, $image_url, $source_url, $is_featured, $published_at, $edit_id]);
                
                if ($result) {
                    $success_message = 'تم تحديث المقال بنجاح';
                    $action = 'list';
                }
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء حفظ المقال: ' . $e->getMessage();
        }
    } else {
        $error_message = 'يرجى إدخال العنوان والمحتوى على الأقل';
    }
}

// الحصول على بيانات المقال للتعديل
$article_data = null;
if (($action === 'edit' && $edit_id) || ($action === 'add' && $edit_id)) {
    $stmt = $db->prepare("SELECT * FROM articles WHERE id = ?");
    $stmt->execute([$edit_id]);
    $article_data = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($article_data) {
        $action = 'edit';
    }
}

// الحصول على التصنيفات
$stmt = $db->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">إدارة المقالات</h1>
        <?php if ($action === 'list'): ?>
        <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <i class="fas fa-plus ml-2"></i>إضافة مقال جديد
        </a>
        <?php else: ?>
        <a href="?" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            <i class="fas fa-arrow-right ml-2"></i>العودة للقائمة
        </a>
        <?php endif; ?>
    </div>

    <!-- Messages -->
    <?php if ($success_message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Articles List -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold">قائمة المقالات</h2>
                <div class="flex space-x-2 space-x-reverse">
                    <input type="text" id="searchInput" placeholder="البحث في المقالات..." 
                           class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <select id="categoryFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">جميع التصنيفات</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنوان</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التصنيف</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكاتب</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المشاهدات</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ النشر</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="articlesTableBody" class="bg-white divide-y divide-gray-200">
                    <!-- سيتم تحميل المقالات هنا عبر JavaScript -->
                </tbody>
            </table>
        </div>

        <div id="pagination" class="px-6 py-3 border-t border-gray-200">
            <!-- سيتم إضافة التصفح هنا -->
        </div>
    </div>

    <?php else: ?>
    <!-- Add/Edit Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold mb-6">
            <?php echo $action === 'add' ? 'إضافة مقال جديد' : 'تعديل المقال'; ?>
        </h2>

        <form method="POST" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">عنوان المقال *</label>
                    <input type="text" name="title" required 
                           value="<?php echo $article_data['title'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">التصنيف</label>
                    <select name="category_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">اختر التصنيف</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" 
                                <?php echo ($article_data['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo $category['name']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الكاتب</label>
                    <input type="text" name="author" 
                           value="<?php echo $article_data['author'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط الصورة</label>
                    <input type="url" name="image_url" 
                           value="<?php echo $article_data['image_url'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط المصدر</label>
                    <input type="url" name="source_url" 
                           value="<?php echo $article_data['source_url'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ النشر</label>
                    <input type="datetime-local" name="published_at" 
                           value="<?php echo isset($article_data['published_at']) ? date('Y-m-d\TH:i', strtotime($article_data['published_at'])) : date('Y-m-d\TH:i'); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الملخص</label>
                    <textarea name="excerpt" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo $article_data['excerpt'] ?? ''; ?></textarea>
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">محتوى المقال *</label>
                    <textarea name="content" rows="15" required 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo $article_data['content'] ?? ''; ?></textarea>
                </div>

                <div class="lg:col-span-2">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_featured" value="1" 
                               <?php echo ($article_data['is_featured'] ?? 0) ? 'checked' : ''; ?>
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">مقال مميز</span>
                    </label>
                </div>
            </div>

            <div class="flex justify-end space-x-3 space-x-reverse">
                <a href="?" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    إلغاء
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <?php echo $action === 'add' ? 'إضافة المقال' : 'تحديث المقال'; ?>
                </button>
            </div>
        </form>
    </div>
    <?php endif; ?>
</div>

<script>
// تحميل المقالات
function loadArticles(page = 1, search = '', category = '') {
    const params = new URLSearchParams({
        ajax: 'load_articles',
        page: page,
        search: search,
        category: category
    });

    fetch('articles.php?' + params)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('articlesTableBody').innerHTML = data.html;
                document.getElementById('pagination').innerHTML = data.pagination;
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// البحث والتصفية
document.getElementById('searchInput').addEventListener('input', function() {
    loadArticles(1, this.value, document.getElementById('categoryFilter').value);
});

document.getElementById('categoryFilter').addEventListener('change', function() {
    loadArticles(1, document.getElementById('searchInput').value, this.value);
});

// تحميل المقالات عند تحميل الصفحة
if (document.getElementById('articlesTableBody')) {
    loadArticles();
}
</script>

<?php include 'includes/footer.php'; ?>
