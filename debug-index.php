<?php
/**
 * تشخيص مشكلة عدم ظهور السلايدر في الصفحة الرئيسية
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>تشخيص مشكلة السلايدر في الصفحة الرئيسية</h1>";

// التحقق من وجود ملف matches_functions.php
echo "<h2>1. التحقق من ملف matches_functions.php:</h2>";
if (file_exists('includes/matches_functions.php')) {
    echo "<p>✅ ملف matches_functions.php موجود</p>";
    require_once 'includes/matches_functions.php';
    echo "<p>✅ تم تحميل ملف matches_functions.php بنجاح</p>";
} else {
    echo "<p>❌ ملف matches_functions.php غير موجود</p>";
    exit;
}

// اختبار دالة getUpcomingMatches
echo "<h2>2. اختبار دالة getUpcomingMatches:</h2>";
try {
    $upcoming_matches = getUpcomingMatches(5);
    echo "<p>✅ دالة getUpcomingMatches تعمل بنجاح</p>";
    echo "<p>عدد المباريات القادمة: " . count($upcoming_matches) . "</p>";
    
    if (!empty($upcoming_matches)) {
        echo "<p>✅ توجد مباريات قادمة</p>";
        echo "<h3>أول 3 مباريات:</h3>";
        echo "<ul>";
        for ($i = 0; $i < min(3, count($upcoming_matches)); $i++) {
            $match = $upcoming_matches[$i];
            echo "<li>" . $match['home_team'] . " vs " . $match['away_team'] . " - " . $match['match_date'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ لا توجد مباريات قادمة</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في دالة getUpcomingMatches: " . $e->getMessage() . "</p>";
    $upcoming_matches = [];
}

// محاكاة الكود في index.php
echo "<h2>3. محاكاة الكود في index.php:</h2>";

// الحصول على المباريات القادمة (إذا كان نظام المباريات مفعل)
$upcoming_matches_index = [];
if (file_exists('includes/matches_functions.php')) {
    require_once 'includes/matches_functions.php';
    try {
        $upcoming_matches_index = getUpcomingMatches(5);
        echo "<p>✅ تم جلب المباريات في محاكاة index.php</p>";
        echo "<p>عدد المباريات: " . count($upcoming_matches_index) . "</p>";
    } catch (Exception $e) {
        // نظام المباريات غير مفعل أو غير مثبت
        $upcoming_matches_index = [];
        echo "<p>❌ خطأ في محاكاة index.php: " . $e->getMessage() . "</p>";
    }
}

// اختبار الشرط
echo "<h2>4. اختبار شرط عرض السلايدر:</h2>";
if (!empty($upcoming_matches_index)) {
    echo "<p>✅ الشرط !empty(\$upcoming_matches) صحيح - يجب أن يظهر السلايدر</p>";
    
    // حساب عدد الشرائح
    $all_matches = array_slice($upcoming_matches_index, 0, 9); // أقصى 9 مباريات
    $total_slides = ceil(count($all_matches) / 3); // حساب عدد الشرائح للديسكتوب
    
    echo "<p>عدد المباريات للسلايدر: " . count($all_matches) . "</p>";
    echo "<p>عدد الشرائح: " . $total_slides . "</p>";
    
    // عرض تفاصيل الشرائح
    echo "<h3>تفاصيل الشرائح:</h3>";
    for ($slide = 0; $slide < $total_slides; $slide++) {
        $slide_matches = array_slice($all_matches, $slide * 3, 3);
        echo "<h4>الشريحة " . ($slide + 1) . " (" . count($slide_matches) . " مباريات):</h4>";
        echo "<ul>";
        foreach ($slide_matches as $match) {
            echo "<li>" . $match['home_team'] . " vs " . $match['away_team'] . " (" . ($match['is_featured'] ? 'مميزة' : 'عادية') . ")</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p>❌ الشرط !empty(\$upcoming_matches) خاطئ - لن يظهر السلايدر</p>";
}

// اختبار دوال التنسيق
echo "<h2>5. اختبار دوال التنسيق:</h2>";
if (!empty($upcoming_matches_index)) {
    $test_match = $upcoming_matches_index[0];
    
    echo "<p>اختبار formatMatchDate:</p>";
    try {
        $date_only = formatMatchDate($test_match['match_date'], 'date_only');
        $time_only = formatMatchDate($test_match['match_date'], 'time_only');
        echo "<p>✅ formatMatchDate تعمل بنجاح</p>";
        echo "<p>التاريخ فقط: " . $date_only . "</p>";
        echo "<p>الوقت فقط: " . $time_only . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في formatMatchDate: " . $e->getMessage() . "</p>";
    }
}

// فحص الصفحة الرئيسية الفعلية
echo "<h2>6. فحص محتوى الصفحة الرئيسية:</h2>";
echo "<p>سنقوم بفحص محتوى index.php للتأكد من وجود السلايدر...</p>";

// قراءة محتوى index.php
$index_content = file_get_contents('index.php');

// البحث عن كلمات مفتاحية
$keywords = [
    'Football Matches Widget',
    'matches-widget',
    'matchesSlider',
    'getUpcomingMatches',
    '!empty($upcoming_matches)'
];

echo "<h3>البحث عن الكلمات المفتاحية في index.php:</h3>";
foreach ($keywords as $keyword) {
    if (strpos($index_content, $keyword) !== false) {
        echo "<p>✅ تم العثور على: " . $keyword . "</p>";
    } else {
        echo "<p>❌ لم يتم العثور على: " . $keyword . "</p>";
    }
}

echo "<h2>7. الخلاصة:</h2>";
if (!empty($upcoming_matches_index)) {
    echo "<p style='color: green; font-weight: bold;'>✅ جميع الشروط متوفرة لعرض السلايدر. المشكلة قد تكون في:</p>";
    echo "<ul>";
    echo "<li>مشكلة في CSS أو JavaScript</li>";
    echo "<li>تضارب في الأكواد</li>";
    echo "<li>خطأ في HTML</li>";
    echo "<li>مشكلة في التخزين المؤقت للمتصفح</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ السلايدر لن يظهر لأن لا توجد مباريات قادمة</p>";
}
?>
