/*
 * Football Matches Widget CSS
 * تنسيقات جدول المباريات
 */

/* Main Widget Container */
.matches-widget {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #16a085 0%, #2980b9 100%);
}

.matches-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: matchesShimmer 4s infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes matchesShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Widget Header */
.matches-widget .widget-header {
    background: linear-gradient(135deg, #27ae60 0%, #3498db 100%);
    position: relative;
    z-index: 2;
}

.matches-widget .widget-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
    animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Matches Slider Container */
.matches-slider {
    position: relative;
    overflow: hidden;
    background: white;
    border-radius: 0 0 12px 12px;
}

.matches-slider-container {
    display: flex;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
}

.matches-slide {
    min-width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    padding: 1.5rem;
}

/* Match Cards */
.match-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.25rem;
    border: 2px solid #e5e7eb;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.match-card:hover::before {
    left: 100%;
}

.match-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

/* Featured Match */
.featured-match {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f59e0b;
    position: relative;
}

.featured-match::after {
    content: '⭐';
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 1.2rem;
    animation: starPulse 2s ease-in-out infinite;
}

@keyframes starPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Team Logos */
.team-logo {
    width: 32px;
    height: 32px;
    object-fit: contain;
    border-radius: 6px;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.match-card:hover .team-logo {
    transform: scale(1.1);
}

/* Match Date and Competition */
.match-date-badge {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.2);
}

.competition-badge {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* VS Indicator */
.vs-indicator {
    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
    color: #374151;
    padding: 0.375rem 0.75rem;
    border-radius: 8px;
    font-weight: 700;
    font-size: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.vs-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    transition: left 0.3s ease;
}

.match-card:hover .vs-indicator::before {
    left: 100%;
}

/* Team Names */
.team-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.match-card:hover .team-name {
    color: #3b82f6;
}

/* Match Time and Venue */
.match-time {
    color: #3b82f6;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
}

.match-venue {
    color: #6b7280;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Featured Match Indicator */
.featured-indicator {
    background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
    color: #92400e;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);
    animation: featuredGlow 2s ease-in-out infinite alternate;
}

@keyframes featuredGlow {
    0% { box-shadow: 0 2px 4px rgba(251, 191, 36, 0.3); }
    100% { box-shadow: 0 4px 12px rgba(251, 191, 36, 0.5); }
}

/* Navigation Buttons */
.slider-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e5e7eb;
    border-radius: 50%;
    color: #6b7280;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.slider-nav-btn:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #3b82f6;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.slider-nav-btn:active {
    transform: translateY(-50%) scale(0.95);
}

.slider-nav-btn.prev {
    right: 1rem;
}

.slider-nav-btn.next {
    left: 1rem;
}

.slider-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%);
}

.slider-nav-btn:disabled:hover {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #6b7280;
    border-color: #e5e7eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Slider Dots */
.slider-dots {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding: 0 1.5rem;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #d1d5db;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.slider-dot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: transparent;
    transition: background 0.3s ease;
}

.slider-dot:hover::before {
    background: rgba(59, 130, 246, 0.2);
}

.slider-dot.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    transform: scale(1.3);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.slider-dot.active::before {
    background: rgba(59, 130, 246, 0.3);
}

/* View All Button */
.view-all-btn {
    background: linear-gradient(135deg, #16a085 0%, #2980b9 100%);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 15px rgba(22, 160, 133, 0.3);
    position: relative;
    overflow: hidden;
}

.view-all-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.view-all-btn:hover::before {
    left: 100%;
}

.view-all-btn:hover {
    background: linear-gradient(135deg, #138d75 0%, #2471a3 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(22, 160, 133, 0.4);
}

.view-all-btn:active {
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .matches-slide {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 1rem;
    }

    .slider-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .slider-nav-btn.prev {
        right: 0.5rem;
    }

    .slider-nav-btn.next {
        left: 0.5rem;
    }
}

@media (max-width: 768px) {
    .matches-slide {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .match-card {
        padding: 1rem;
    }

    .team-logo {
        width: 28px;
        height: 28px;
    }

    .slider-nav-btn {
        width: 36px;
        height: 36px;
        font-size: 0.875rem;
    }

    .slider-dots {
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .slider-dot {
        width: 10px;
        height: 10px;
    }

    .view-all-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .matches-widget {
        margin: 0 -1rem;
    }

    .matches-slide {
        padding: 0.75rem;
    }

    .match-card {
        padding: 0.875rem;
    }

    .team-name {
        font-size: 0.8125rem;
    }

    .match-date-badge,
    .competition-badge,
    .match-time,
    .match-venue {
        font-size: 0.6875rem;
    }

    .vs-indicator {
        padding: 0.25rem 0.5rem;
        font-size: 0.6875rem;
    }

    .slider-nav-btn {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }

    .slider-dot {
        width: 8px;
        height: 8px;
    }
}

/* Loading States */
.matches-widget.loading {
    opacity: 0.7;
    pointer-events: none;
}

.matches-widget.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 100;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Error States */
.matches-widget.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.matches-error {
    text-align: center;
    padding: 2rem;
    color: white;
}

.matches-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

/* Accessibility */
.matches-widget:focus-within .slider-nav-btn {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.slider-dot:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .matches-widget::before,
    .matches-widget .widget-header::before,
    .match-card::before,
    .vs-indicator::before,
    .view-all-btn::before {
        animation: none;
    }

    .matches-slider-container,
    .match-card,
    .slider-nav-btn,
    .slider-dot,
    .view-all-btn {
        transition: none;
    }

    .featured-match::after,
    .featured-indicator {
        animation: none;
    }
}

/* Print Styles */
@media print {
    .matches-widget {
        background: white !important;
        box-shadow: none !important;
    }

    .slider-nav-btn,
    .slider-dots,
    .view-all-btn {
        display: none !important;
    }

    .matches-slide {
        display: block !important;
    }

    .match-card {
        break-inside: avoid;
        margin-bottom: 1rem;
        border: 1px solid #000 !important;
    }
}
