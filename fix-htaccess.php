<?php
/**
 * Quick fix utility for .htaccess issues
 */

$action = $_GET['action'] ?? '';

switch ($action) {
    case 'simple':
        // Copy simple .htaccess
        if (file_exists('.htaccess.simple')) {
            if (copy('.htaccess.simple', '.htaccess')) {
                echo "Simple .htaccess applied successfully!";
            } else {
                echo "Failed to apply simple .htaccess";
            }
        } else {
            // Create a basic .htaccess
            $simple_htaccess = "# Basic .htaccess for News Website
RewriteEngine On
Options -Indexes

<Files \"database.php\">
    Require all denied
</Files>

<Files \"*.log\">
    Require all denied
</Files>
";
            if (file_put_contents('.htaccess', $simple_htaccess)) {
                echo "Basic .htaccess created successfully!";
            } else {
                echo "Failed to create .htaccess";
            }
        }
        break;
        
    case 'disable':
        // Rename .htaccess to disable it
        if (file_exists('.htaccess')) {
            if (rename('.htaccess', '.htaccess.disabled')) {
                echo ".htaccess disabled successfully!";
            } else {
                echo "Failed to disable .htaccess";
            }
        } else {
            echo ".htaccess file not found";
        }
        break;
        
    case 'restore':
        // Restore from backup
        if (file_exists('.htaccess.disabled')) {
            if (rename('.htaccess.disabled', '.htaccess')) {
                echo ".htaccess restored successfully!";
            } else {
                echo "Failed to restore .htaccess";
            }
        } else {
            echo "No backup found";
        }
        break;
        
    default:
        echo "Invalid action";
}
?>
