/*
 * Urgent Design Fixes - إصلاحات التصميم العاجلة
 * ملف CSS لإصلاح المشاكل العاجلة في التصميم
 */

/* ==========================================================================
   Badge System Fixes - إصلاح نظام الشارات
   ========================================================================== */

.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    text-decoration: none;
    transition: all var(--transition-fast);
    line-height: 1;
}

.badge-urgent {
    background: var(--error-600);
    color: white;
}

.badge-urgent:hover {
    background: var(--error-700);
    color: white;
}

.badge-category {
    background: var(--primary-600);
    color: white;
}

.badge-category:hover {
    background: var(--primary-700);
    color: white;
}

.badge-category-link {
    background: var(--primary-100);
    color: var(--primary-800);
}

.badge-category-link:hover {
    background: var(--primary-200);
    color: var(--primary-900);
}

.badge-featured {
    background: var(--warning-600);
    color: white;
}

.badge-featured:hover {
    background: var(--warning-700);
    color: white;
}

/* ==========================================================================
   Typography Fixes - إصلاح الطباعة
   ========================================================================== */

.heading-section {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--neutral-800);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    line-height: var(--leading-tight);
}

.heading-section i {
    color: var(--primary-600);
}

.heading-widget {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-2);
    border-bottom: 2px solid var(--neutral-200);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    line-height: var(--leading-tight);
}

.heading-widget i {
    color: var(--primary-600);
}

.heading-article {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
    line-height: var(--leading-tight);
}

.heading-article a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.heading-article a:hover {
    color: var(--primary-600);
}

.heading-sub {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--neutral-800);
    margin-bottom: var(--space-1);
    line-height: var(--leading-tight);
}

.heading-sub a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.heading-sub a:hover {
    color: var(--primary-600);
}

/* ==========================================================================
   Image System Fixes - إصلاح نظام الصور
   ========================================================================== */

.image-hero {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--radius-xl);
    transition: transform var(--transition-base);
}

.image-hero:hover {
    transform: scale(1.02);
}

.image-card {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    transition: transform var(--transition-base);
}

.image-card:hover {
    transform: scale(1.05);
}

.image-thumbnail {
    width: 60px;
    height: 45px;
    object-fit: cover;
    border-radius: var(--radius-md);
    flex-shrink: 0;
}

.image-small {
    width: 45px;
    height: 35px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    flex-shrink: 0;
}

.image-placeholder {
    background: linear-gradient(135deg, var(--neutral-200) 0%, var(--neutral-300) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neutral-500);
    font-size: var(--text-2xl);
}

.image-placeholder-featured {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    opacity: 0.8;
}

/* ==========================================================================
   Button System Fixes - إصلاح نظام الأزرار
   ========================================================================== */

.btn-filter {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    border-radius: var(--radius-lg);
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    line-height: 1;
}

.btn-filter-active {
    background: var(--primary-600);
    color: white;
}

.btn-filter-active:hover {
    background: var(--primary-700);
    color: white;
}

.btn-filter-inactive {
    background: var(--neutral-200);
    color: var(--neutral-700);
}

.btn-filter-inactive:hover {
    background: var(--neutral-300);
    color: var(--neutral-800);
}

.btn-link {
    color: var(--primary-600);
    text-decoration: none;
    font-weight: var(--font-medium);
    transition: color var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}

.btn-link:hover {
    color: var(--primary-700);
}

/* ==========================================================================
   Card System Fixes - إصلاح نظام البطاقات
   ========================================================================== */

.news-card-unified {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-base);
    overflow: hidden;
    transition: all var(--transition-base);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.news-card-unified:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
}

.news-card-content-unified {
    padding: var(--space-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news-card-meta-unified {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-3);
}

.news-card-date-unified {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.news-card-excerpt-unified {
    color: var(--neutral-600);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-card-footer-unified {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--space-3);
    border-top: 1px solid var(--neutral-200);
    margin-top: auto;
}

.news-card-views-unified {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* ==========================================================================
   Sidebar Fixes - إصلاح الشريط الجانبي
   ========================================================================== */

.sidebar-article-unified {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.sidebar-article-unified:hover {
    background-color: var(--neutral-50);
}

.sidebar-article-content-unified {
    flex: 1;
    min-width: 0;
}

.sidebar-article-meta-unified {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
    margin-top: var(--space-1);
}

/* ==========================================================================
   Override Tailwind Classes - تجاوز فئات Tailwind
   ========================================================================== */

/* Badge overrides */
.bg-red-600 {
    background-color: var(--error-600) !important;
}

.bg-blue-600 {
    background-color: var(--primary-600) !important;
}

.bg-blue-100 {
    background-color: var(--primary-100) !important;
}

.text-blue-800 {
    color: var(--primary-800) !important;
}

.text-blue-600 {
    color: var(--primary-600) !important;
}

.text-gray-800 {
    color: var(--neutral-800) !important;
}

.text-gray-700 {
    color: var(--neutral-700) !important;
}

.text-gray-600 {
    color: var(--neutral-600) !important;
}

.text-gray-500 {
    color: var(--neutral-500) !important;
}

/* Button overrides */
.bg-gray-200 {
    background-color: var(--neutral-200) !important;
}

.hover\:bg-gray-300:hover {
    background-color: var(--neutral-300) !important;
}

.hover\:bg-blue-700:hover {
    background-color: var(--primary-700) !important;
}

/* Border overrides */
.border-gray-200 {
    border-color: var(--neutral-200) !important;
}

/* ==========================================================================
   Responsive Fixes - إصلاحات التصميم المتجاوب
   ========================================================================== */

@media (max-width: 767px) {
    .heading-section {
        font-size: var(--text-xl);
        margin-bottom: var(--space-4);
    }
    
    .heading-widget {
        font-size: var(--text-base);
        margin-bottom: var(--space-3);
    }
    
    .image-hero {
        height: 250px;
    }
    
    .image-card {
        height: 150px;
    }
    
    .news-card-content-unified {
        padding: var(--space-4);
    }
    
    .btn-filter {
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-xs);
    }
}

@media (max-width: 480px) {
    .heading-section {
        font-size: var(--text-lg);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-1);
    }
    
    .image-hero {
        height: 200px;
        border-radius: var(--radius-lg);
    }
    
    .badge {
        font-size: 0.625rem; /* 10px */
        padding: var(--space-1) var(--space-2);
    }
}
