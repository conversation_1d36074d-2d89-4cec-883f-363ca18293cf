/**
 * Homepage Enhancements JavaScript
 * تحسينات الصفحة الرئيسية
 */

class HomepageEnhancements {
    constructor() {
        this.currentPage = 1;
        this.isLoading = false;
        this.hasMoreContent = true;
        this.scrollThreshold = 300;
        this.animationObserver = null;
        this.lastScrollTop = 0;
        
        this.init();
    }
    
    init() {
        this.createProgressBar();
        this.createScrollToTopButton();
        this.createStickyNav();
        this.setupScrollAnimations();
        this.setupInfiniteScroll();
        this.setupSmoothScrolling();
        this.setupEventListeners();
        this.setupAccessibility();
        this.initializeAnimations();

        // Dispatch custom event
        document.dispatchEvent(new CustomEvent('homepageEnhanced', {
            detail: { version: '1.0.0', features: this.getEnabledFeatures() }
        }));
    }

    getEnabledFeatures() {
        return [
            'smooth-scrolling',
            'scroll-to-top',
            'sticky-navigation',
            'scroll-animations',
            'infinite-scroll',
            'progress-bar'
        ];
    }
    
    // Progress Bar
    createProgressBar() {
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-progress';
        progressBar.id = 'scroll-progress';
        document.body.appendChild(progressBar);
    }
    
    updateProgressBar() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const progress = (scrollTop / scrollHeight) * 100;
        
        const progressBar = document.getElementById('scroll-progress');
        if (progressBar) {
            progressBar.style.width = Math.min(progress, 100) + '%';
        }
    }
    
    // Scroll to Top Button
    createScrollToTopButton() {
        const button = document.createElement('button');
        button.className = 'scroll-to-top';
        button.id = 'scroll-to-top';
        button.innerHTML = '<i class="fas fa-arrow-up"></i>';
        button.title = 'العودة لأعلى';
        button.setAttribute('aria-label', 'العودة لأعلى الصفحة');
        
        button.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        document.body.appendChild(button);
    }
    
    toggleScrollToTopButton() {
        const button = document.getElementById('scroll-to-top');
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > this.scrollThreshold) {
            button.classList.add('visible');
        } else {
            button.classList.remove('visible');
        }
    }
    
    // Sticky Navigation
    createStickyNav() {
        const originalNav = document.querySelector('nav');
        if (!originalNav) return;
        
        const stickyNav = originalNav.cloneNode(true);
        stickyNav.className = 'sticky-nav';
        stickyNav.id = 'sticky-nav';
        
        // تبسيط المحتوى للشريط المثبت
        const container = stickyNav.querySelector('.container');
        if (container) {
            container.classList.add('py-2');
        }
        
        document.body.appendChild(stickyNav);
    }
    
    toggleStickyNav() {
        const stickyNav = document.getElementById('sticky-nav');
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const breakingNews = document.getElementById('breaking-news-ticker');
        const breakingNewsHeight = breakingNews ? breakingNews.offsetHeight : 0;
        
        if (scrollTop > (200 + breakingNewsHeight)) {
            stickyNav.classList.add('visible');
        } else {
            stickyNav.classList.remove('visible');
        }
    }
    
    // Scroll Animations
    setupScrollAnimations() {
        // إضافة classes للعناصر التي نريد تحريكها
        this.addAnimationClasses();
        
        // إعداد Intersection Observer
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        this.animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);
        
        // مراقبة العناصر
        this.observeAnimatedElements();
    }
    
    addAnimationClasses() {
        // Hero section
        const heroSection = document.querySelector('section');
        if (heroSection) {
            heroSection.classList.add('fade-in', 'hero-section');
        }
        
        // News cards
        const newsCards = document.querySelectorAll('.news-card');
        newsCards.forEach((card, index) => {
            card.classList.add('stagger-animation');
            card.style.transitionDelay = `${index * 0.1}s`;
        });
        
        // Sidebar widgets
        const sidebarWidgets = document.querySelectorAll('.lg\\:col-span-1 > div');
        sidebarWidgets.forEach((widget, index) => {
            widget.classList.add('slide-left', 'sidebar-widget');
            widget.style.transitionDelay = `${index * 0.2}s`;
        });
        
        // Section titles
        const sectionTitles = document.querySelectorAll('h2, h3');
        sectionTitles.forEach(title => {
            title.classList.add('slide-up');
        });
        
        // Category buttons
        const categoryButtons = document.querySelectorAll('.bg-gray-200');
        categoryButtons.forEach(button => {
            button.classList.add('category-pill');
        });
    }
    
    observeAnimatedElements() {
        const animatedElements = document.querySelectorAll(
            '.fade-in, .slide-up, .slide-left, .slide-right, .scale-in, .stagger-animation'
        );
        
        animatedElements.forEach(element => {
            this.animationObserver.observe(element);
        });
    }
    
    // Infinite Scroll / Load More
    setupInfiniteScroll() {
        this.createLoadMoreButton();
        this.createInfiniteScrollLoader();
        
        // اختيار نوع التحميل بناءً على تفضيل المستخدم
        const useInfiniteScroll = this.shouldUseInfiniteScroll();
        
        if (useInfiniteScroll) {
            this.setupInfiniteScrollListener();
        } else {
            this.showLoadMoreButton();
        }
    }
    
    createLoadMoreButton() {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;
        
        const container = document.createElement('div');
        container.className = 'load-more-container';
        container.innerHTML = `
            <button class="load-more-btn" id="load-more-btn">
                <span class="loading-spinner"></span>
                <span class="btn-text">تحميل المزيد</span>
            </button>
        `;
        
        pagination.parentNode.insertBefore(container, pagination);
        pagination.style.display = 'none';
        
        const button = document.getElementById('load-more-btn');
        button.addEventListener('click', () => this.loadMoreArticles());
    }
    
    createInfiniteScrollLoader() {
        const loader = document.createElement('div');
        loader.className = 'infinite-scroll-loading';
        loader.id = 'infinite-scroll-loading';
        loader.innerHTML = `
            <div class="spinner"></div>
            <p>جاري تحميل المزيد من الأخبار...</p>
        `;
        
        const mainContent = document.querySelector('.lg\\:col-span-3');
        if (mainContent) {
            mainContent.appendChild(loader);
        }
    }
    
    shouldUseInfiniteScroll() {
        // استخدام infinite scroll على الأجهزة المحمولة
        return window.innerWidth <= 768;
    }
    
    showLoadMoreButton() {
        const button = document.getElementById('load-more-btn');
        if (button) {
            button.style.display = 'inline-block';
        }
    }
    
    setupInfiniteScrollListener() {
        let ticking = false;
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.checkInfiniteScroll();
                    ticking = false;
                });
                ticking = true;
            }
        });
    }
    
    checkInfiniteScroll() {
        if (this.isLoading || !this.hasMoreContent) return;
        
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight;
        const clientHeight = window.innerHeight;
        
        if (scrollTop + clientHeight >= scrollHeight - 1000) {
            this.loadMoreArticles();
        }
    }
    
    async loadMoreArticles() {
        if (this.isLoading || !this.hasMoreContent) return;
        
        this.isLoading = true;
        this.showLoadingState();
        
        try {
            const response = await fetch(`index.php?page=${this.currentPage + 1}&ajax=1`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) throw new Error('Network response was not ok');
            
            const data = await response.json();
            
            if (data.success && data.articles.length > 0) {
                this.appendArticles(data.articles);
                this.currentPage++;
                
                if (data.articles.length < data.per_page) {
                    this.hasMoreContent = false;
                    this.hideLoadMoreButton();
                }
            } else {
                this.hasMoreContent = false;
                this.hideLoadMoreButton();
            }
        } catch (error) {
            console.error('Error loading more articles:', error);
            this.showErrorMessage();
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }
    
    appendArticles(articles) {
        const articlesGrid = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.xl\\:grid-cols-3');
        if (!articlesGrid) return;
        
        articles.forEach((article, index) => {
            const articleElement = this.createArticleElement(article);
            articleElement.classList.add('stagger-animation');
            articleElement.style.transitionDelay = `${index * 0.1}s`;
            
            articlesGrid.appendChild(articleElement);
            
            // مراقبة العنصر الجديد للأنيميشن
            this.animationObserver.observe(articleElement);
        });
    }
    
    createArticleElement(article) {
        const articleEl = document.createElement('article');
        articleEl.className = 'bg-white rounded-lg shadow-md overflow-hidden news-card';
        
        articleEl.innerHTML = `
            <div class="aspect-w-16 aspect-h-9">
                ${article.image_url ? 
                    `<img src="${article.image_url}" alt="${article.title}" class="w-full h-48 object-cover">` :
                    `<div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                        <i class="fas fa-newspaper text-4xl text-gray-400"></i>
                    </div>`
                }
            </div>
            <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                    ${article.category_name ? 
                        `<a href="category.php?slug=${article.category_slug}" class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium hover:bg-blue-200 transition-colors">
                            ${article.category_name}
                        </a>` : ''
                    }
                    <span class="text-xs text-gray-500">
                        <i class="fas fa-clock ml-1"></i>
                        ${this.formatTime(article.published_at)}
                    </span>
                </div>
                <h3 class="font-bold text-lg mb-2 leading-tight">
                    <a href="article.php?slug=${article.slug}" class="hover:text-blue-600 transition-colors">
                        ${article.title}
                    </a>
                </h3>
                <p class="text-gray-600 text-sm mb-3 line-clamp-3">
                    ${article.excerpt}
                </p>
                <div class="flex items-center justify-between text-xs text-gray-500">
                    <span>
                        <i class="fas fa-eye ml-1"></i>
                        ${this.formatNumber(article.views)}
                    </span>
                    <span>
                        ${this.formatDate(article.published_at)}
                    </span>
                </div>
            </div>
        `;
        
        return articleEl;
    }
    
    showLoadingState() {
        const button = document.getElementById('load-more-btn');
        const loader = document.getElementById('infinite-scroll-loading');
        
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
            button.querySelector('.btn-text').textContent = 'جاري التحميل...';
        }
        
        if (loader && this.shouldUseInfiniteScroll()) {
            loader.classList.add('visible');
        }
    }
    
    hideLoadingState() {
        const button = document.getElementById('load-more-btn');
        const loader = document.getElementById('infinite-scroll-loading');
        
        if (button) {
            button.classList.remove('loading');
            button.disabled = false;
            button.querySelector('.btn-text').textContent = 'تحميل المزيد';
        }
        
        if (loader) {
            loader.classList.remove('visible');
        }
    }
    
    hideLoadMoreButton() {
        const button = document.getElementById('load-more-btn');
        if (button) {
            button.style.display = 'none';
        }
        
        const container = button?.parentElement;
        if (container) {
            container.innerHTML = '<p class="text-gray-500 text-center">تم عرض جميع الأخبار المتاحة</p>';
        }
    }
    
    showErrorMessage() {
        const container = document.querySelector('.load-more-container');
        if (container) {
            container.innerHTML = `
                <div class="text-center text-red-600">
                    <p>حدث خطأ أثناء تحميل المزيد من الأخبار</p>
                    <button onclick="location.reload()" class="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }
    }
    
    // Smooth Scrolling for Internal Links
    setupSmoothScrolling() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (!link) return;
            
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }
    
    // Event Listeners
    setupEventListeners() {
        let ticking = false;
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.updateProgressBar();
                    this.toggleScrollToTopButton();
                    this.toggleStickyNav();
                    ticking = false;
                });
                ticking = true;
            }
        });
        
        // Resize listener
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // Visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.refreshAnimations();
            }
        });
    }
    
    handleResize() {
        // إعادة تقييم نوع التحميل عند تغيير حجم الشاشة
        const shouldUseInfinite = this.shouldUseInfiniteScroll();
        const button = document.getElementById('load-more-btn');
        
        if (shouldUseInfinite && button && button.style.display !== 'none') {
            this.setupInfiniteScrollListener();
            button.style.display = 'none';
        } else if (!shouldUseInfinite && button) {
            button.style.display = 'inline-block';
        }
    }
    
    initializeAnimations() {
        // تشغيل الأنيميشن للعناصر المرئية فوراً
        const visibleElements = document.querySelectorAll('.fade-in, .slide-up, .slide-left, .slide-right, .scale-in');
        
        visibleElements.forEach(element => {
            const rect = element.getBoundingClientRect();
            if (rect.top < window.innerHeight && rect.bottom > 0) {
                element.classList.add('visible');
            }
        });
    }
    
    refreshAnimations() {
        // إعادة تشغيل الأنيميشن للعناصر الجديدة
        const newElements = document.querySelectorAll('.stagger-animation:not(.visible)');
        newElements.forEach(element => {
            this.animationObserver.observe(element);
        });
    }
    
    // Utility Functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    formatNumber(num) {
        return new Intl.NumberFormat('ar-EG').format(num);
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG');
    }

    formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' });
    }

    // Enhanced error handling
    handleError(error, context) {
        console.error(`Homepage Enhancement Error (${context}):`, error);

        // Send error to analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'exception', {
                'description': `${context}: ${error.message}`,
                'fatal': false
            });
        }

        // Show user-friendly message for critical errors
        if (context === 'load_more' || context === 'infinite_scroll') {
            this.showErrorMessage();
        }
    }

    // Performance monitoring
    measurePerformance(name, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();

        console.log(`${name} took ${end - start} milliseconds`);

        // Log to analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'timing_complete', {
                'name': name,
                'value': Math.round(end - start)
            });
        }

        return result;
    }

    // Accessibility improvements
    setupAccessibility() {
        // Add ARIA labels
        const scrollToTop = document.getElementById('scroll-to-top');
        if (scrollToTop) {
            scrollToTop.setAttribute('aria-label', 'العودة إلى أعلى الصفحة');
            scrollToTop.setAttribute('role', 'button');
        }

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'Home':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }
                    break;
                case 'End':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                    }
                    break;
            }
        });

        // Respect reduced motion preferences
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.disableAnimations();
        }
    }

    disableAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Public Methods
    destroy() {
        if (this.animationObserver) {
            this.animationObserver.disconnect();
        }
        
        // إزالة العناصر المضافة
        const elementsToRemove = [
            'scroll-progress',
            'scroll-to-top',
            'sticky-nav',
            'infinite-scroll-loading'
        ];
        
        elementsToRemove.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.remove();
            }
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.homepageEnhancements = new HomepageEnhancements();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HomepageEnhancements;
}
