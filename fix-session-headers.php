<?php
/**
 * إصلاح مشاكل PHP Session والـ Headers
 */

// تفعيل Output Buffering لتجنب مشاكل الـ headers
ob_start();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشاكل PHP Session والـ Headers</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; border-left: 4px solid #007bff; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح مشاكل PHP Session والـ Headers</h1>";

$fixes_applied = [];
$errors = [];

// 1. إنشاء نسخ احتياطية
echo "<div class='section info'>";
echo "<h2>1. إنشاء نسخ احتياطية</h2>";

$files_to_backup = ['index.php', 'matches.php', 'config/config.php'];
foreach ($files_to_backup as $file) {
    if (file_exists($file)) {
        $backup_name = $file . '.backup.' . date('Y-m-d-H-i-s');
        if (copy($file, $backup_name)) {
            echo "<p style='color: green;'>✅ تم إنشاء نسخة احتياطية: $backup_name</p>";
            $fixes_applied[] = "نسخة احتياطية من $file";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء نسخة احتياطية من $file</p>";
            $errors[] = "فشل في إنشاء نسخة احتياطية من $file";
        }
    }
}
echo "</div>";

// 2. إنشاء ملف session_init.php منفصل
echo "<div class='section info'>";
echo "<h2>2. إنشاء ملف session_init.php منفصل</h2>";

$session_init_content = '<?php
/**
 * تهيئة الجلسة والإعدادات الأساسية
 * يجب تحميل هذا الملف قبل أي إخراج HTML
 */

// تفعيل Output Buffering
if (!ob_get_level()) {
    ob_start();
}

// تحديد المسار الجذر
if (!defined(\'ROOT_PATH\')) {
    define(\'ROOT_PATH\', __DIR__);
}

// تحميل ملف التكوين
require_once ROOT_PATH . \'/config/config.php\';

// بدء الجلسة إذا لم تبدأ بعد
if (session_status() === PHP_SESSION_NONE) {
    // إعدادات الجلسة قبل البدء
    ini_set(\'session.cookie_lifetime\', SESSION_LIFETIME);
    ini_set(\'session.gc_maxlifetime\', SESSION_LIFETIME);
    ini_set(\'session.cookie_httponly\', 1);
    ini_set(\'session.cookie_secure\', isset($_SERVER[\'HTTPS\']));
    ini_set(\'session.use_strict_mode\', 1);
    ini_set(\'session.cookie_samesite\', \'Lax\');
    
    // بدء الجلسة
    session_start();
}

// تحميل ملف الدوال
require_once ROOT_PATH . \'/includes/functions.php\';

// تجديد معرف الجلسة دورياً لتحسين الأمان
if (!isset($_SESSION[\'last_regeneration\'])) {
    $_SESSION[\'last_regeneration\'] = time();
} elseif (time() - $_SESSION[\'last_regeneration\'] > 300) { // كل 5 دقائق
    session_regenerate_id(true);
    $_SESSION[\'last_regeneration\'] = time();
}

// دالة للتحقق من صحة الجلسة
function validateSession() {
    // التحقق من انتهاء صلاحية الجلسة
    if (isset($_SESSION[\'expires\']) && $_SESSION[\'expires\'] < time()) {
        session_destroy();
        return false;
    }
    
    // التحقق من IP Address (اختياري)
    if (isset($_SESSION[\'ip_address\']) && $_SESSION[\'ip_address\'] !== $_SERVER[\'REMOTE_ADDR\']) {
        session_destroy();
        return false;
    }
    
    return true;
}

// تعيين انتهاء صلاحية الجلسة
if (!isset($_SESSION[\'expires\'])) {
    $_SESSION[\'expires\'] = time() + SESSION_LIFETIME;
    $_SESSION[\'ip_address\'] = $_SERVER[\'REMOTE_ADDR\'];
}

// التحقق من صحة الجلسة
validateSession();
?>';

if (file_put_contents('includes/session_init.php', $session_init_content)) {
    echo "<p style='color: green;'>✅ تم إنشاء ملف session_init.php</p>";
    $fixes_applied[] = "إنشاء ملف session_init.php";
} else {
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف session_init.php</p>";
    $errors[] = "فشل في إنشاء ملف session_init.php";
}
echo "</div>";

// 3. تحديث ملف config.php لإزالة إعدادات الجلسة
echo "<div class='section info'>";
echo "<h2>3. تحديث ملف config.php</h2>";

if (file_exists('config/config.php')) {
    $config_content = file_get_contents('config/config.php');
    
    // إزالة إعدادات الجلسة من config.php
    $config_content = preg_replace('/\/\/ إعدادات الجلسة.*?^\}/ms', '', $config_content);
    $config_content = preg_replace('/if \(session_status\(\) === PHP_SESSION_NONE\) \{.*?\}/s', '', $config_content);
    
    // تنظيف الأسطر الفارغة الزائدة
    $config_content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $config_content);
    
    if (file_put_contents('config/config.php', $config_content)) {
        echo "<p style='color: green;'>✅ تم تحديث config.php وإزالة إعدادات الجلسة</p>";
        $fixes_applied[] = "تحديث config.php";
    } else {
        echo "<p style='color: red;'>❌ فشل في تحديث config.php</p>";
        $errors[] = "فشل في تحديث config.php";
    }
} else {
    echo "<p style='color: red;'>❌ ملف config.php غير موجود</p>";
}
echo "</div>";

// 4. إنشاء index.php جديد ومحسن
echo "<div class='section info'>";
echo "<h2>4. إنشاء index.php جديد ومحسن</h2>";

$new_index_content = '<?php
/**
 * الصفحة الرئيسية - محسنة لتجنب مشاكل الـ Headers
 */

// تهيئة الجلسة والإعدادات الأساسية (قبل أي إخراج)
require_once \'includes/session_init.php\';

// معالج AJAX لتحميل المزيد من المقالات
if (isset($_GET[\'ajax\']) && $_GET[\'ajax\'] == \'1\') {
    header(\'Content-Type: application/json\');

    $page = isset($_GET[\'page\']) ? (int)$_GET[\'page\'] : 1;
    $per_page = (int)getSetting(\'articles_per_page\', 12);

    try {
        $articles = getArticles($page, $per_page);

        if (!empty($articles)) {
            echo json_encode([
                \'success\' => true,
                \'articles\' => $articles,
                \'page\' => $page,
                \'per_page\' => $per_page,
                \'has_more\' => count($articles) == $per_page
            ]);
        } else {
            echo json_encode([
                \'success\' => false,
                \'message\' => \'لا توجد مقالات أخرى\',
                \'articles\' => []
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            \'success\' => false,
            \'message\' => \'حدث خطأ أثناء تحميل المقالات\',
            \'error\' => $e->getMessage()
        ]);
    }
    exit;
}

// إعداد الصفحة
$page_title = \'الرئيسية\';
$page_description = getSetting(\'site_description\', \'موقع إخباري شامل\');

// الحصول على رقم الصفحة
$page = isset($_GET[\'page\']) ? (int)$_GET[\'page\'] : 1;
$per_page = (int)getSetting(\'articles_per_page\', 12);

// الحصول على المقالات
$articles = getArticles($page, $per_page);
$total_articles = getTotalArticles();
$total_pages = ceil($total_articles / $per_page);

// الحصول على المقالات المميزة
$featured_articles = getFeaturedArticles(4);

// الحصول على أحدث المقالات للشريط الجانبي
$latest_articles = getLatestArticles(8);

// الحصول على المباريات القادمة (إذا كان نظام المباريات مفعل)
$upcoming_matches = [];
if (file_exists(\'includes/matches_functions.php\')) {
    require_once \'includes/matches_functions.php\';
    try {
        $upcoming_matches = getUpcomingMatches(5);
    } catch (Exception $e) {
        // نظام المباريات غير مفعل أو غير مثبت
        $upcoming_matches = [];
    }
}

// تحميل الهيدر (بعد تهيئة جميع المتغيرات)
include \'includes/header.php\';
?>

<!-- المحتوى يبدأ هنا -->
';

// إضافة باقي محتوى index.php (من السطر 88 فما بعد)
if (file_exists('index.php')) {
    $old_index = file_get_contents('index.php');
    $lines = explode("\n", $old_index);
    
    // البحث عن بداية المحتوى HTML (السطر الذي يحتوي على <!-- Football Matches Widget -->)
    $content_start = 0;
    foreach ($lines as $i => $line) {
        if (strpos($line, '<!-- Football Matches Widget -->') !== false) {
            $content_start = $i;
            break;
        }
    }
    
    if ($content_start > 0) {
        $html_content = implode("\n", array_slice($lines, $content_start));
        $new_index_content .= $html_content;
    }
}

if (file_put_contents('index.php', $new_index_content)) {
    echo "<p style='color: green;'>✅ تم إنشاء index.php جديد ومحسن</p>";
    $fixes_applied[] = "إنشاء index.php محسن";
} else {
    echo "<p style='color: red;'>❌ فشل في إنشاء index.php الجديد</p>";
    $errors[] = "فشل في إنشاء index.php الجديد";
}
echo "</div>";

// 5. تحديث matches.php
echo "<div class='section info'>";
echo "<h2>5. تحديث matches.php</h2>";

if (file_exists('matches.php')) {
    $matches_content = file_get_contents('matches.php');
    
    // إضافة تهيئة الجلسة في البداية
    $new_matches_content = '<?php
/**
 * صفحة المباريات - محسنة لتجنب مشاكل الـ Headers
 */

// تهيئة الجلسة والإعدادات الأساسية (قبل أي إخراج)
require_once \'includes/session_init.php\';

// تحميل دوال المباريات
require_once \'includes/matches_functions.php\';

';
    
    // إضافة باقي محتوى matches.php (بعد إزالة الأسطر الأولى)
    $lines = explode("\n", $matches_content);
    $content_start = 0;
    
    // البحث عن بداية المحتوى الفعلي
    foreach ($lines as $i => $line) {
        if (strpos($line, 'session_start') !== false || 
            strpos($line, 'require_once') !== false ||
            strpos($line, 'include') !== false) {
            continue;
        }
        if (strpos($line, '$page_title') !== false) {
            $content_start = $i;
            break;
        }
    }
    
    if ($content_start > 0) {
        $php_content = implode("\n", array_slice($lines, $content_start));
        $new_matches_content .= $php_content;
    }
    
    if (file_put_contents('matches.php', $new_matches_content)) {
        echo "<p style='color: green;'>✅ تم تحديث matches.php</p>";
        $fixes_applied[] = "تحديث matches.php";
    } else {
        echo "<p style='color: red;'>❌ فشل في تحديث matches.php</p>";
        $errors[] = "فشل في تحديث matches.php";
    }
} else {
    echo "<p style='color: red;'>❌ ملف matches.php غير موجود</p>";
}
echo "</div>";

// 6. ملخص الإصلاحات
echo "<div class='section success'>";
echo "<h2>6. ملخص الإصلاحات المطبقة</h2>";

if (!empty($fixes_applied)) {
    echo "<h3>الإصلاحات المطبقة:</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li style='color: green;'>✅ $fix</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: blue;'>ℹ️ لم يتم تطبيق إصلاحات</p>";
}

if (!empty($errors)) {
    echo "<h3>الأخطاء:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>❌ $error</li>";
    }
    echo "</ul>";
}

echo "<h3>التحسينات المطبقة:</h3>";
echo "<ul>";
echo "<li>🔧 <strong>فصل تهيئة الجلسة:</strong> إنشاء ملف session_init.php منفصل</li>";
echo "<li>📋 <strong>إعادة ترتيب الكود:</strong> وضع تهيئة الجلسة قبل أي إخراج</li>";
echo "<li>🛡️ <strong>تحسين الأمان:</strong> إضافة تحقق من صحة الجلسة</li>";
echo "<li>⚡ <strong>تحسين الأداء:</strong> استخدام Output Buffering</li>";
echo "<li>🧹 <strong>تنظيف الكود:</strong> إزالة التكرارات والكود غير المستخدم</li>";
echo "</ul>";
echo "</div>";

// 7. اختبار النتائج
echo "<div class='section info'>";
echo "<h2>7. اختبار النتائج</h2>";
echo "<div style='text-align: center;'>";
echo "<a href='index.php' target='_blank' class='btn btn-primary'>اختبار الصفحة الرئيسية</a>";
echo "<a href='matches.php' target='_blank' class='btn btn-success'>اختبار صفحة المباريات</a>";
echo "<a href='session-diagnosis.php' target='_blank' class='btn btn-warning'>إعادة التشخيص</a>";
echo "<a href='test-session-functionality.php' target='_blank' class='btn btn-danger'>اختبار وظائف الجلسة</a>";
echo "</div>";

echo "<p style='margin-top: 20px; text-align: center; color: #27ae60; font-weight: bold;'>";
echo "🎉 تم إصلاح مشاكل PHP Session والـ Headers بنجاح!";
echo "</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

// تفريغ Output Buffer
ob_end_flush();
?>
