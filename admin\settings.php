<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

checkAuth();

// التحقق من صلاحيات المدير
if ($_SESSION['user_role'] !== 'admin') {
    header("Location: dashboard.php");
    exit();
}

$page_title = 'إعدادات الموقع';
$database = new Database();
$db = $database->connect();

$success_message = '';
$error_message = '';

// حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $settings = [
            'site_name' => trim($_POST['site_name']),
            'site_description' => trim($_POST['site_description']),
            'site_keywords' => trim($_POST['site_keywords']),
            'articles_per_page' => (int)$_POST['articles_per_page'],
            'auto_fetch_rss' => isset($_POST['auto_fetch_rss']) ? 'true' : 'false',
            'rss_fetch_interval' => (int)$_POST['rss_fetch_interval'],
            'facebook_url' => trim($_POST['facebook_url']),
            'twitter_url' => trim($_POST['twitter_url']),
            'instagram_url' => trim($_POST['instagram_url']),
            'youtube_url' => trim($_POST['youtube_url']),
            'telegram_url' => trim($_POST['telegram_url']),
            'contact_email' => trim($_POST['contact_email']),
            'contact_phone' => trim($_POST['contact_phone']),
            'contact_address' => trim($_POST['contact_address']),
            'google_analytics' => trim($_POST['google_analytics']),
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? 'true' : 'false',
            'allow_comments' => isset($_POST['allow_comments']) ? 'true' : 'false',
            'cache_enabled' => isset($_POST['cache_enabled']) ? 'true' : 'false',
            'cache_lifetime' => (int)$_POST['cache_lifetime']
        ];
        
        foreach ($settings as $key => $value) {
            $stmt = $db->prepare("
                INSERT INTO settings (setting_key, setting_value, setting_type) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
            ");
            
            $type = 'text';
            if (in_array($key, ['articles_per_page', 'rss_fetch_interval', 'cache_lifetime'])) {
                $type = 'number';
            } elseif (in_array($key, ['auto_fetch_rss', 'maintenance_mode', 'allow_comments', 'cache_enabled'])) {
                $type = 'boolean';
            }
            
            $stmt->execute([$key, $value, $type]);
        }
        
        $success_message = 'تم حفظ الإعدادات بنجاح';
        
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
$stmt = $db->query("SELECT setting_key, setting_value FROM settings");
$current_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $current_settings[$row['setting_key']] = $row['setting_value'];
}

// قيم افتراضية
$defaults = [
    'site_name' => 'موقع الأخبار',
    'site_description' => 'موقع إخباري شامل يقدم آخر الأخبار والتطورات',
    'site_keywords' => 'أخبار، عاجل، تقنية، رياضة، اقتصاد، سياسة',
    'articles_per_page' => 12,
    'auto_fetch_rss' => 'true',
    'rss_fetch_interval' => 3600,
    'facebook_url' => '',
    'twitter_url' => '',
    'instagram_url' => '',
    'youtube_url' => '',
    'telegram_url' => '',
    'contact_email' => '',
    'contact_phone' => '',
    'contact_address' => '',
    'google_analytics' => '',
    'maintenance_mode' => 'false',
    'allow_comments' => 'true',
    'cache_enabled' => 'true',
    'cache_lifetime' => 3600
];

// دمج الإعدادات الحالية مع القيم الافتراضية
$settings = array_merge($defaults, $current_settings);

include 'includes/header.php';
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">إعدادات الموقع</h1>
        <div class="flex space-x-2 space-x-reverse">
            <button onclick="resetSettings()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                <i class="fas fa-undo ml-2"></i>إعادة تعيين
            </button>
        </div>
    </div>

    <!-- Messages -->
    <?php if ($success_message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <form method="POST" class="space-y-6">
        <!-- إعدادات عامة -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-6 text-gray-800">الإعدادات العامة</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                    <input type="text" name="site_name" 
                           value="<?php echo htmlspecialchars($settings['site_name']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">عدد المقالات في الصفحة</label>
                    <select name="articles_per_page" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="6" <?php echo $settings['articles_per_page'] == 6 ? 'selected' : ''; ?>>6 مقالات</option>
                        <option value="9" <?php echo $settings['articles_per_page'] == 9 ? 'selected' : ''; ?>>9 مقالات</option>
                        <option value="12" <?php echo $settings['articles_per_page'] == 12 ? 'selected' : ''; ?>>12 مقال</option>
                        <option value="15" <?php echo $settings['articles_per_page'] == 15 ? 'selected' : ''; ?>>15 مقال</option>
                        <option value="20" <?php echo $settings['articles_per_page'] == 20 ? 'selected' : ''; ?>>20 مقال</option>
                    </select>
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف الموقع</label>
                    <textarea name="site_description" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($settings['site_description']); ?></textarea>
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الكلمات المفتاحية (مفصولة بفواصل)</label>
                    <input type="text" name="site_keywords" 
                           value="<?php echo htmlspecialchars($settings['site_keywords']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
        </div>

        <!-- إعدادات RSS -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-6 text-gray-800">إعدادات RSS</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">فترة جلب RSS (بالثواني)</label>
                    <select name="rss_fetch_interval" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="1800" <?php echo $settings['rss_fetch_interval'] == 1800 ? 'selected' : ''; ?>>30 دقيقة</option>
                        <option value="3600" <?php echo $settings['rss_fetch_interval'] == 3600 ? 'selected' : ''; ?>>ساعة واحدة</option>
                        <option value="7200" <?php echo $settings['rss_fetch_interval'] == 7200 ? 'selected' : ''; ?>>ساعتان</option>
                        <option value="21600" <?php echo $settings['rss_fetch_interval'] == 21600 ? 'selected' : ''; ?>>6 ساعات</option>
                        <option value="43200" <?php echo $settings['rss_fetch_interval'] == 43200 ? 'selected' : ''; ?>>12 ساعة</option>
                        <option value="86400" <?php echo $settings['rss_fetch_interval'] == 86400 ? 'selected' : ''; ?>>24 ساعة</option>
                    </select>
                </div>

                <div class="flex items-center">
                    <label class="flex items-center">
                        <input type="checkbox" name="auto_fetch_rss" value="1" 
                               <?php echo $settings['auto_fetch_rss'] === 'true' ? 'checked' : ''; ?>
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">جلب RSS تلقائياً</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- وسائل التواصل الاجتماعي -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-6 text-gray-800">وسائل التواصل الاجتماعي</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط فيسبوك</label>
                    <input type="url" name="facebook_url" 
                           value="<?php echo htmlspecialchars($settings['facebook_url']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط تويتر</label>
                    <input type="url" name="twitter_url" 
                           value="<?php echo htmlspecialchars($settings['twitter_url']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط إنستغرام</label>
                    <input type="url" name="instagram_url" 
                           value="<?php echo htmlspecialchars($settings['instagram_url']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط يوتيوب</label>
                    <input type="url" name="youtube_url" 
                           value="<?php echo htmlspecialchars($settings['youtube_url']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط تليغرام</label>
                    <input type="url" name="telegram_url" 
                           value="<?php echo htmlspecialchars($settings['telegram_url']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-6 text-gray-800">معلومات الاتصال</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" name="contact_email" 
                           value="<?php echo htmlspecialchars($settings['contact_email']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                    <input type="tel" name="contact_phone" 
                           value="<?php echo htmlspecialchars($settings['contact_phone']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <textarea name="contact_address" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($settings['contact_address']); ?></textarea>
                </div>
            </div>
        </div>

        <!-- إعدادات متقدمة -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-6 text-gray-800">إعدادات متقدمة</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">كود Google Analytics</label>
                    <textarea name="google_analytics" rows="3" 
                              placeholder="ضع كود Google Analytics هنا"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($settings['google_analytics']); ?></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">مدة التخزين المؤقت (بالثواني)</label>
                    <select name="cache_lifetime" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="1800" <?php echo $settings['cache_lifetime'] == 1800 ? 'selected' : ''; ?>>30 دقيقة</option>
                        <option value="3600" <?php echo $settings['cache_lifetime'] == 3600 ? 'selected' : ''; ?>>ساعة واحدة</option>
                        <option value="7200" <?php echo $settings['cache_lifetime'] == 7200 ? 'selected' : ''; ?>>ساعتان</option>
                        <option value="21600" <?php echo $settings['cache_lifetime'] == 21600 ? 'selected' : ''; ?>>6 ساعات</option>
                        <option value="86400" <?php echo $settings['cache_lifetime'] == 86400 ? 'selected' : ''; ?>>24 ساعة</option>
                    </select>
                </div>

                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="cache_enabled" value="1" 
                               <?php echo $settings['cache_enabled'] === 'true' ? 'checked' : ''; ?>
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">تفعيل التخزين المؤقت</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="allow_comments" value="1" 
                               <?php echo $settings['allow_comments'] === 'true' ? 'checked' : ''; ?>
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">السماح بالتعليقات</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="maintenance_mode" value="1" 
                               <?php echo $settings['maintenance_mode'] === 'true' ? 'checked' : ''; ?>
                               class="rounded border-gray-300 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">وضع الصيانة</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="flex justify-end space-x-3 space-x-reverse">
            <button type="button" onclick="window.location.reload()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                إلغاء
            </button>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-save ml-2"></i>حفظ الإعدادات
            </button>
        </div>
    </form>
</div>

<script>
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        // يمكن إضافة كود AJAX هنا لإعادة تعيين الإعدادات
        alert('سيتم تنفيذ هذه الميزة قريباً');
    }
}
</script>

<?php include 'includes/footer.php'; ?>
