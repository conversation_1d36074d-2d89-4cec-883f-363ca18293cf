<?php
/**
 * اختبار إصلاح خطأ SQL في نظام المباريات
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من وجود ملف دوال المباريات
if (!file_exists('includes/matches_functions.php')) {
    die('ملف دوال المباريات غير موجود: includes/matches_functions.php');
}

require_once 'includes/matches_functions.php';

session_start();

$page_title = 'اختبار إصلاح SQL';
$test_results = [];

// اختبار الدوال المختلفة
function testFunction($function_name, $function_call) {
    global $test_results;
    
    try {
        $start_time = microtime(true);
        $result = $function_call();
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        $test_results[] = [
            'function' => $function_name,
            'status' => 'success',
            'message' => 'تم تنفيذ الدالة بنجاح',
            'count' => is_array($result) ? count($result) : (is_numeric($result) ? $result : 1),
            'time' => $execution_time . ' ms'
        ];
        
        return $result;
    } catch (Exception $e) {
        $test_results[] = [
            'function' => $function_name,
            'status' => 'error',
            'message' => $e->getMessage(),
            'count' => 0,
            'time' => '0 ms'
        ];
        
        return false;
    }
}

// تشغيل الاختبارات
echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح SQL</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
    <link href='https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Tajawal', Arial, sans-serif; }</style>
</head>
<body class='bg-gray-100'>
    <div class='container mx-auto px-4 py-8'>
        <div class='bg-white rounded-lg shadow-lg p-6'>
            <h1 class='text-3xl font-bold text-center mb-8 text-gray-800'>
                <i class='fas fa-bug ml-2 text-red-600'></i>
                اختبار إصلاح خطأ SQL في نظام المباريات
            </h1>";

// اختبار 1: getUpcomingMatches
testFunction('getUpcomingMatches()', function() {
    return getUpcomingMatches(5);
});

// اختبار 2: getFinishedMatches
testFunction('getFinishedMatches()', function() {
    return getFinishedMatches(5);
});

// اختبار 3: getFeaturedMatches
testFunction('getFeaturedMatches()', function() {
    return getFeaturedMatches(3);
});

// اختبار 4: getLiveMatches
testFunction('getLiveMatches()', function() {
    return getLiveMatches();
});

// اختبار 5: getMatches
testFunction('getMatches()', function() {
    return getMatches(10, 0, []);
});

// اختبار 6: searchMatches
testFunction('searchMatches()', function() {
    return searchMatches('الهلال', 5);
});

// اختبار 7: getMatchesStats
testFunction('getMatchesStats()', function() {
    return getMatchesStats();
});

// عرض النتائج
echo "<div class='grid grid-cols-1 gap-4 mb-8'>";

foreach ($test_results as $result) {
    $status_color = $result['status'] === 'success' ? 'green' : 'red';
    $status_icon = $result['status'] === 'success' ? 'check-circle' : 'times-circle';
    $status_text = $result['status'] === 'success' ? 'نجح' : 'فشل';
    
    echo "<div class='bg-{$status_color}-50 border border-{$status_color}-200 rounded-lg p-4'>
            <div class='flex items-center justify-between'>
                <div class='flex items-center'>
                    <i class='fas fa-{$status_icon} text-{$status_color}-600 ml-3'></i>
                    <div>
                        <h3 class='font-semibold text-{$status_color}-800'>{$result['function']}</h3>
                        <p class='text-sm text-{$status_color}-700'>{$result['message']}</p>
                    </div>
                </div>
                <div class='text-left'>
                    <div class='text-sm text-{$status_color}-600'>النتائج: {$result['count']}</div>
                    <div class='text-xs text-{$status_color}-500'>الوقت: {$result['time']}</div>
                </div>
            </div>
          </div>";
}

echo "</div>";

// ملخص النتائج
$success_count = count(array_filter($test_results, function($r) { return $r['status'] === 'success'; }));
$total_count = count($test_results);
$success_rate = round(($success_count / $total_count) * 100, 1);

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6'>
        <h2 class='text-xl font-semibold mb-4 text-blue-800'>
            <i class='fas fa-chart-pie ml-2'></i>ملخص النتائج
        </h2>
        
        <div class='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div class='bg-white p-4 rounded-lg text-center'>
                <div class='text-2xl font-bold text-blue-600'>$total_count</div>
                <div class='text-sm text-blue-800'>إجمالي الاختبارات</div>
            </div>
            
            <div class='bg-white p-4 rounded-lg text-center'>
                <div class='text-2xl font-bold text-green-600'>$success_count</div>
                <div class='text-sm text-green-800'>اختبارات ناجحة</div>
            </div>
            
            <div class='bg-white p-4 rounded-lg text-center'>
                <div class='text-2xl font-bold text-purple-600'>$success_rate%</div>
                <div class='text-sm text-purple-800'>معدل النجاح</div>
            </div>
        </div>
      </div>";

// التوصيات
if ($success_count === $total_count) {
    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6'>
            <div class='flex items-center'>
                <i class='fas fa-check-circle text-green-600 ml-2'></i>
                <strong>ممتاز!</strong> تم إصلاح جميع أخطاء SQL بنجاح. نظام المباريات يعمل بشكل صحيح.
            </div>
          </div>";
} else {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6'>
            <div class='flex items-center'>
                <i class='fas fa-exclamation-triangle text-red-600 ml-2'></i>
                <strong>تحذير!</strong> لا تزال هناك أخطاء في بعض الدوال. يرجى مراجعة الأخطاء أعلاه.
            </div>
          </div>";
}

// روابط سريعة
echo "<div class='bg-white border border-gray-200 rounded-lg p-6'>
        <h2 class='text-xl font-semibold mb-4 text-gray-800'>
            <i class='fas fa-link ml-2'></i>اختبار الصفحات
        </h2>
        
        <div class='grid grid-cols-2 md:grid-cols-4 gap-4'>
            <a href='matches.php' class='bg-blue-600 text-white px-4 py-3 rounded text-center hover:bg-blue-700 transition-colors'>
                <i class='fas fa-futbol block mb-1'></i>صفحة المباريات
            </a>
            
            <a href='admin/matches.php' class='bg-green-600 text-white px-4 py-3 rounded text-center hover:bg-green-700 transition-colors'>
                <i class='fas fa-cogs block mb-1'></i>إدارة المباريات
            </a>
            
            <a href='test-matches.php' class='bg-purple-600 text-white px-4 py-3 rounded text-center hover:bg-purple-700 transition-colors'>
                <i class='fas fa-vial block mb-1'></i>اختبار النظام
            </a>
            
            <a href='index.php' class='bg-orange-600 text-white px-4 py-3 rounded text-center hover:bg-orange-700 transition-colors'>
                <i class='fas fa-home block mb-1'></i>الصفحة الرئيسية
            </a>
        </div>
      </div>";

echo "        </div>
    </div>
</body>
</html>";
?>
