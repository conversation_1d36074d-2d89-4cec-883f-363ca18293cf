<?php
require_once 'config/database.php';

// إنشاء قاعدة البيانات والجداول
if (createDatabase()) {
    echo "<!DOCTYPE html>
    <html lang='ar' dir='rtl'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>إعداد الموقع الإخباري</title>
        <script src='https://cdn.tailwindcss.com'></script>
        <link href='https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap' rel='stylesheet'>
        <style>body { font-family: 'Tajawal', Arial, sans-serif; }</style>
    </head>
    <body class='bg-gray-100'>
        <div class='min-h-screen flex items-center justify-center'>
            <div class='bg-white p-8 rounded-lg shadow-lg max-w-md w-full'>
                <div class='text-center'>
                    <div class='bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center'>
                        <svg class='w-8 h-8 text-green-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 13l4 4L19 7'></path>
                        </svg>
                    </div>
                    <h1 class='text-2xl font-bold text-gray-800 mb-4'>تم إعداد الموقع بنجاح!</h1>
                    <p class='text-gray-600 mb-6'>تم إنشاء قاعدة البيانات والجداول بنجاح. يمكنك الآن استخدام الموقع.</p>
                    
                    <div class='bg-blue-50 p-4 rounded-lg mb-6 text-right'>
                        <h3 class='font-semibold text-blue-800 mb-2'>معلومات تسجيل الدخول الإداري:</h3>
                        <p class='text-blue-700'><strong>اسم المستخدم:</strong> admin</p>
                        <p class='text-blue-700'><strong>كلمة المرور:</strong> admin123</p>
                    </div>
                    
                    <div class='space-y-3'>
                        <a href='index.php' class='block w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors'>
                            الذهاب إلى الموقع
                        </a>
                        <a href='admin/login.php' class='block w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors'>
                            لوحة التحكم الإدارية
                        </a>
                    </div>
                    
                    <div class='mt-6 p-4 bg-yellow-50 rounded-lg'>
                        <p class='text-yellow-800 text-sm'>
                            <strong>تنبيه:</strong> يُنصح بحذف ملف setup.php بعد الانتهاء من الإعداد لأسباب أمنية.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>";
} else {
    echo "<!DOCTYPE html>
    <html lang='ar' dir='rtl'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>خطأ في الإعداد</title>
        <script src='https://cdn.tailwindcss.com'></script>
        <link href='https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap' rel='stylesheet'>
        <style>body { font-family: 'Tajawal', Arial, sans-serif; }</style>
    </head>
    <body class='bg-gray-100'>
        <div class='min-h-screen flex items-center justify-center'>
            <div class='bg-white p-8 rounded-lg shadow-lg max-w-md w-full'>
                <div class='text-center'>
                    <div class='bg-red-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center'>
                        <svg class='w-8 h-8 text-red-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'></path>
                        </svg>
                    </div>
                    <h1 class='text-2xl font-bold text-gray-800 mb-4'>خطأ في الإعداد</h1>
                    <p class='text-gray-600 mb-6'>حدث خطأ أثناء إعداد قاعدة البيانات. يرجى التحقق من إعدادات قاعدة البيانات.</p>
                    
                    <div class='bg-red-50 p-4 rounded-lg mb-6 text-right'>
                        <h3 class='font-semibold text-red-800 mb-2'>تحقق من:</h3>
                        <ul class='text-red-700 text-sm space-y-1'>
                            <li>• تشغيل خادم MySQL</li>
                            <li>• صحة بيانات الاتصال في config/database.php</li>
                            <li>• صلاحيات إنشاء قاعدة البيانات</li>
                        </ul>
                    </div>
                    
                    <button onclick='location.reload()' class='w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors'>
                        إعادة المحاولة
                    </button>
                </div>
            </div>
        </div>
    </body>
    </html>";
}
?>

