/*
 * Homepage Enhancements CSS
 * تحسينات الصفحة الرئيسية
 */

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Progress Bar */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8, #1e40af);
    z-index: 9999;
    transition: width 0.1s ease-out;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* Sticky Navigation */
.sticky-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    transition: transform 0.3s ease-in-out;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sticky-nav.visible {
    transform: translateY(0);
}

/* Scroll to <PERSON> Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.scroll-to-top:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(0) scale(1.1);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-up {
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.slide-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-left {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.7s ease, transform 0.7s ease;
}

.slide-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-right {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.7s ease, transform 0.7s ease;
}

.slide-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* Staggered Animation */
.stagger-animation {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.stagger-animation.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin: 2rem 0;
}

.load-more-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.load-more-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.load-more-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.load-more-btn .loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

.load-more-btn.loading .loading-spinner {
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Infinite Scroll Loading */
.infinite-scroll-loading {
    display: none;
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

.infinite-scroll-loading.visible {
    display: block;
}

.infinite-scroll-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Enhanced News Cards */
.news-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.news-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.news-card:hover::before {
    left: 100%;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.news-card img {
    transition: transform 0.3s ease;
}

.news-card:hover img {
    transform: scale(1.05);
}

/* Hero Section Enhancements */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.1) 100%);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.hero-section:hover::before {
    opacity: 1;
}

/* Sidebar Enhancements */
.sidebar-widget {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.sidebar-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Category Pills */
.category-pill {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-pill::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.3s ease;
}

.category-pill:hover::before {
    left: 100%;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
    
    .sticky-nav {
        padding: 0.5rem 1rem;
    }
    
    .fade-in,
    .slide-up,
    .slide-left,
    .slide-right {
        transform: translateY(20px);
    }
    
    .fade-in.visible,
    .slide-up.visible,
    .slide-left.visible,
    .slide-right.visible {
        transform: translateY(0);
    }
}

@media (max-width: 480px) {
    .scroll-to-top {
        bottom: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
    
    .load-more-btn {
        padding: 10px 25px;
        font-size: 14px;
    }
}

/* Performance Optimizations */
.news-card,
.hero-section,
.sidebar-widget {
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
    
    .fade-in,
    .slide-up,
    .slide-left,
    .slide-right,
    .scale-in,
    .stagger-animation,
    .news-card,
    .hero-section,
    .sidebar-widget {
        transition: none;
        animation: none;
    }
    
    .scroll-to-top,
    .sticky-nav {
        transition: opacity 0.2s ease;
    }
}

/* Loading States */
.content-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.content-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 10;
}
