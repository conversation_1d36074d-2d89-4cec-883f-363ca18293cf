<?php
/**
 * إصلاح مشاكل التكرار في الموقع
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح التكرارات</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-danger { background: #e74c3c; }";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح مشاكل التكرار</h1>";

$fixes_applied = [];
$errors = [];

// 1. فحص وإصلاح التكرار في الصفحة الرئيسية
echo "<div class='section info'>";
echo "<h2>1. فحص الصفحة الرئيسية</h2>";

if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    
    // فحص التكرار في سلايدر المباريات
    $matches_widget_count = substr_count($index_content, 'matches-widget');
    $matches_slider_count = substr_count($index_content, 'matches-slider');
    
    echo "<p>عدد عناصر سلايدر المباريات: $matches_widget_count</p>";
    echo "<p>عدد حاويات السلايدر: $matches_slider_count</p>";
    
    if ($matches_widget_count > 1) {
        echo "<p style='color: red;'>❌ تم العثور على تكرار في سلايدر المباريات</p>";
        
        // البحث عن التكرار وإزالته
        $pattern = '/<!-- Football Matches Widget -->.*?<\/section>/s';
        preg_match_all($pattern, $index_content, $matches, PREG_OFFSET_CAPTURE);
        
        if (count($matches[0]) > 1) {
            // إزالة التكرارات الإضافية
            $new_content = $index_content;
            for ($i = count($matches[0]) - 1; $i > 0; $i--) {
                $start = $matches[0][$i][1];
                $length = strlen($matches[0][$i][0]);
                $new_content = substr_replace($new_content, '', $start, $length);
            }
            
            // إنشاء نسخة احتياطية
            copy('index.php', 'index.php.backup.' . date('Y-m-d-H-i-s'));
            
            // حفظ الملف المحدث
            if (file_put_contents('index.php', $new_content)) {
                $fixes_applied[] = "إزالة التكرار في سلايدر المباريات";
                echo "<p style='color: green;'>✅ تم إصلاح التكرار في سلايدر المباريات</p>";
            } else {
                $errors[] = "فشل في حفظ الملف المحدث";
                echo "<p style='color: red;'>❌ فشل في إصلاح التكرار</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ لا يوجد تكرار في سلايدر المباريات</p>";
    }
    
    // فحص التكرار في المقالات
    $articles_foreach_count = substr_count($index_content, 'foreach ($articles as $article)');
    echo "<p>عدد حلقات عرض المقالات: $articles_foreach_count</p>";
    
    if ($articles_foreach_count > 1) {
        echo "<p style='color: orange;'>⚠️ يوجد أكثر من حلقة لعرض المقالات - قد يكون هذا طبيعي</p>";
    } else {
        echo "<p style='color: green;'>✅ لا يوجد تكرار في حلقات المقالات</p>";
    }
    
} else {
    $errors[] = "ملف index.php غير موجود";
    echo "<p style='color: red;'>❌ ملف index.php غير موجود</p>";
}
echo "</div>";

// 2. فحص وإصلاح التكرار في ملفات CSS
echo "<div class='section info'>";
echo "<h2>2. فحص ملفات CSS</h2>";

$css_files = [
    'assets/css/homepage-enhancements.css',
    'assets/css/matches-slider.css',
    'assets/css/style.css'
];

$duplicate_css_rules = [];

foreach ($css_files as $css_file) {
    if (file_exists($css_file)) {
        $css_content = file_get_contents($css_file);
        
        // البحث عن القواعد المكررة
        preg_match_all('/\.([a-zA-Z0-9_-]+)\s*\{[^}]*\}/s', $css_content, $rules);
        
        $rule_counts = array_count_values($rules[1]);
        $duplicates = array_filter($rule_counts, function($count) { return $count > 1; });
        
        if (!empty($duplicates)) {
            echo "<p style='color: orange;'>⚠️ تم العثور على قواعد CSS مكررة في $css_file:</p>";
            echo "<ul>";
            foreach ($duplicates as $rule => $count) {
                echo "<li>.$rule (مكرر $count مرات)</li>";
            }
            echo "</ul>";
            $duplicate_css_rules[$css_file] = $duplicates;
        } else {
            echo "<p style='color: green;'>✅ لا توجد قواعد CSS مكررة في $css_file</p>";
        }
    } else {
        echo "<p style='color: gray;'>ℹ️ الملف $css_file غير موجود</p>";
    }
}
echo "</div>";

// 3. فحص وإصلاح التكرار في JavaScript
echo "<div class='section info'>";
echo "<h2>3. فحص ملفات JavaScript</h2>";

if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    
    // فحص تكرار كود JavaScript
    $script_tags = substr_count($index_content, '<script>');
    $script_tags += substr_count($index_content, '<script ');
    
    echo "<p>عدد علامات script: $script_tags</p>";
    
    // فحص تكرار دوال JavaScript
    $js_functions = [
        'updateSlider',
        'nextSlide',
        'prevSlide',
        'initSlider'
    ];
    
    foreach ($js_functions as $func) {
        $count = substr_count($index_content, "function $func");
        if ($count > 1) {
            echo "<p style='color: red;'>❌ الدالة $func مكررة $count مرات</p>";
        } elseif ($count == 1) {
            echo "<p style='color: green;'>✅ الدالة $func موجودة مرة واحدة</p>";
        } else {
            echo "<p style='color: gray;'>ℹ️ الدالة $func غير موجودة</p>";
        }
    }
}
echo "</div>";

// 4. فحص التكرار في قاعدة البيانات
echo "<div class='section info'>";
echo "<h2>4. فحص التكرار في قاعدة البيانات</h2>";

try {
    $database = new Database();
    $db = $database->connect();
    
    // فحص المقالات المكررة
    $stmt = $db->query("
        SELECT title, COUNT(*) as count 
        FROM articles 
        GROUP BY title 
        HAVING COUNT(*) > 1
    ");
    $duplicate_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($duplicate_articles)) {
        echo "<p style='color: orange;'>⚠️ تم العثور على مقالات مكررة:</p>";
        echo "<ul>";
        foreach ($duplicate_articles as $article) {
            echo "<li>" . $article['title'] . " (مكرر " . $article['count'] . " مرات)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ لا توجد مقالات مكررة</p>";
    }
    
    // فحص المباريات المكررة
    if (file_exists('includes/matches_functions.php')) {
        $stmt = $db->query("
            SELECT home_team, away_team, match_date, COUNT(*) as count 
            FROM matches 
            GROUP BY home_team, away_team, match_date 
            HAVING COUNT(*) > 1
        ");
        $duplicate_matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($duplicate_matches)) {
            echo "<p style='color: orange;'>⚠️ تم العثور على مباريات مكررة:</p>";
            echo "<ul>";
            foreach ($duplicate_matches as $match) {
                echo "<li>" . $match['home_team'] . " vs " . $match['away_team'] . " في " . $match['match_date'] . " (مكرر " . $match['count'] . " مرات)</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: green;'>✅ لا توجد مباريات مكررة</p>";
        }
    }
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص قاعدة البيانات: " . $e->getMessage();
    echo "<p style='color: red;'>❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 5. ملخص الإصلاحات
echo "<div class='section success'>";
echo "<h2>5. ملخص الإصلاحات</h2>";

if (!empty($fixes_applied)) {
    echo "<h3>الإصلاحات المطبقة:</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li style='color: green;'>✅ $fix</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: blue;'>ℹ️ لم يتم العثور على مشاكل تحتاج إصلاح</p>";
}

if (!empty($errors)) {
    echo "<h3>الأخطاء:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>❌ $error</li>";
    }
    echo "</ul>";
}
echo "</div>";

// 6. توصيات إضافية
echo "<div class='section warning'>";
echo "<h2>6. توصيات إضافية</h2>";
echo "<ul>";
echo "<li>🔄 <strong>إعادة تحميل الصفحة:</strong> امسح ذاكرة التخزين المؤقت للمتصفح</li>";
echo "<li>🧹 <strong>تنظيف الكود:</strong> راجع الكود يدوياً للتأكد من عدم وجود تكرارات</li>";
echo "<li>📱 <strong>اختبار الأجهزة:</strong> اختبر الموقع على أجهزة مختلفة</li>";
echo "<li>🔍 <strong>مراجعة دورية:</strong> قم بفحص دوري للتكرارات</li>";
echo "</ul>";
echo "</div>";

// 7. روابط الاختبار
echo "<div class='section info'>";
echo "<h2>7. اختبار النتائج</h2>";
echo "<a href='index.php' target='_blank' class='btn btn-primary'>اختبار الصفحة الرئيسية</a>";
echo "<a href='matches.php' target='_blank' class='btn btn-success'>اختبار صفحة المباريات</a>";
echo "<a href='advanced-diagnosis.php' target='_blank' class='btn btn-danger'>إعادة التشخيص</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
