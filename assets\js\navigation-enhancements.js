/**
 * Navigation Enhancements JavaScript
 * تحسينات التنقل وزر العودة للأعلى
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ==========================================================================
    // Navigation Scroll Effect
    // ==========================================================================
    
    const navigation = document.querySelector('.main-navigation');
    const backToTopBtn = document.querySelector('.back-to-top');
    
    if (navigation) {
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Add scrolled class to navigation
            if (scrollTop > 100) {
                navigation.classList.add('scrolled');
            } else {
                navigation.classList.remove('scrolled');
            }
            
            // Show/hide back to top button
            if (backToTopBtn) {
                if (scrollTop > 300) {
                    backToTopBtn.classList.add('visible');
                    
                    // Add pulse effect after 5 seconds
                    setTimeout(() => {
                        if (backToTopBtn.classList.contains('visible')) {
                            backToTopBtn.classList.add('pulse');
                        }
                    }, 5000);
                } else {
                    backToTopBtn.classList.remove('visible', 'pulse');
                }
            }
        });
    }
    
    // ==========================================================================
    // Mobile Navigation Toggle
    // ==========================================================================
    
    const mobileToggle = document.querySelector('.mobile-nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Change icon
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                if (navMenu.classList.contains('active')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navigation.contains(e.target)) {
                navMenu.classList.remove('active');
                const icon = mobileToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Close mobile menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navMenu.classList.remove('active');
                const icon = mobileToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            });
        });
    }
    
    // ==========================================================================
    // Back to Top Functionality
    // ==========================================================================
    
    if (backToTopBtn) {
        backToTopBtn.addEventListener('click', function() {
            // Smooth scroll to top
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            
            // Remove pulse effect
            backToTopBtn.classList.remove('pulse');
        });
    }
    
    // ==========================================================================
    // Active Navigation Link
    // ==========================================================================
    
    function setActiveNavLink() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.php';
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            
            if (href === currentPage || 
                (currentPage === '' && href === 'index.php') ||
                (currentPage === 'index.php' && href === 'index.php')) {
                link.classList.add('active');
            }
        });
    }
    
    setActiveNavLink();
    
    // ==========================================================================
    // Smooth Scrolling for Anchor Links
    // ==========================================================================
    
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                e.preventDefault();
                
                const offsetTop = targetElement.offsetTop - (navigation ? navigation.offsetHeight : 0);
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // ==========================================================================
    // Keyboard Navigation
    // ==========================================================================
    
    document.addEventListener('keydown', function(e) {
        // ESC key closes mobile menu
        if (e.key === 'Escape' && navMenu && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-bars';
            }
        }
        
        // Home key scrolls to top
        if (e.key === 'Home' && e.ctrlKey && backToTopBtn) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    });
    
    // ==========================================================================
    // Performance Optimization
    // ==========================================================================
    
    // Throttle scroll events for better performance
    let ticking = false;
    
    function updateScrollEffects() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Update navigation
        if (navigation) {
            if (scrollTop > 100) {
                navigation.classList.add('scrolled');
            } else {
                navigation.classList.remove('scrolled');
            }
        }
        
        // Update back to top button
        if (backToTopBtn) {
            if (scrollTop > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible', 'pulse');
            }
        }
        
        ticking = false;
    }
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }
    
    // Replace the original scroll listener with throttled version
    window.removeEventListener('scroll', arguments.callee);
    window.addEventListener('scroll', requestTick);
    
    console.log('Navigation enhancements loaded successfully');
});