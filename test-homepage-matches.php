<?php
/**
 * اختبار جدول المباريات في الصفحة الرئيسية
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من وجود ملف دوال المباريات
if (!file_exists('includes/matches_functions.php')) {
    die('ملف دوال المباريات غير موجود: includes/matches_functions.php');
}

require_once 'includes/matches_functions.php';

session_start();

$page_title = 'اختبار جدول المباريات في الصفحة الرئيسية';
$test_results = [];

// اختبار الدوال المختلفة
function testFunction($function_name, $function_call, $description = '') {
    global $test_results;
    
    try {
        $start_time = microtime(true);
        $result = $function_call();
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        $test_results[] = [
            'function' => $function_name,
            'description' => $description,
            'status' => 'success',
            'message' => 'تم تنفيذ الدالة بنجاح',
            'count' => is_array($result) ? count($result) : (is_numeric($result) ? $result : 1),
            'time' => $execution_time . ' ms',
            'data' => is_array($result) ? array_slice($result, 0, 3) : null // عرض أول 3 عناصر فقط
        ];
        
        return $result;
    } catch (Exception $e) {
        $test_results[] = [
            'function' => $function_name,
            'description' => $description,
            'status' => 'error',
            'message' => $e->getMessage(),
            'count' => 0,
            'time' => '0 ms',
            'data' => null
        ];
        
        return false;
    }
}

// تشغيل الاختبارات
echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار جدول المباريات في الصفحة الرئيسية</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
    <link href='https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Tajawal', Arial, sans-serif; }</style>
</head>
<body class='bg-gray-100'>
    <div class='container mx-auto px-4 py-8'>
        <div class='bg-white rounded-lg shadow-lg p-6'>
            <h1 class='text-3xl font-bold text-center mb-8 text-gray-800'>
                <i class='fas fa-futbol ml-2 text-green-600'></i>
                اختبار جدول المباريات في الصفحة الرئيسية
            </h1>";

// اختبار 1: جلب المباريات القادمة للصفحة الرئيسية
$upcoming_matches = testFunction(
    'getUpcomingMatches(5)', 
    function() { return getUpcomingMatches(5); },
    'جلب 5 مباريات قادمة لعرضها في الصفحة الرئيسية'
);

// اختبار 2: التحقق من وجود البيانات المطلوبة
if ($upcoming_matches) {
    testFunction(
        'تحقق من البيانات المطلوبة',
        function() use ($upcoming_matches) {
            $required_fields = ['id', 'home_team', 'away_team', 'match_date', 'competition', 'status'];
            $missing_fields = [];
            
            foreach ($upcoming_matches as $match) {
                foreach ($required_fields as $field) {
                    if (!isset($match[$field])) {
                        $missing_fields[] = $field;
                    }
                }
            }
            
            if (!empty($missing_fields)) {
                throw new Exception('حقول مفقودة: ' . implode(', ', array_unique($missing_fields)));
            }
            
            return count($required_fields);
        },
        'التحقق من وجود جميع الحقول المطلوبة في بيانات المباريات'
    );
}

// اختبار 3: تنسيق التاريخ
testFunction(
    'formatMatchDate()',
    function() use ($upcoming_matches) {
        if (!empty($upcoming_matches)) {
            $match = $upcoming_matches[0];
            return [
                'full' => formatMatchDate($match['match_date'], 'full'),
                'date_only' => formatMatchDate($match['match_date'], 'date_only'),
                'time_only' => formatMatchDate($match['match_date'], 'time_only'),
                'arabic' => formatMatchDate($match['match_date'], 'arabic')
            ];
        }
        return [];
    },
    'اختبار تنسيق التاريخ بصيغ مختلفة'
);

// اختبار 4: المباريات المميزة
testFunction(
    'المباريات المميزة',
    function() use ($upcoming_matches) {
        $featured_count = 0;
        if (!empty($upcoming_matches)) {
            foreach ($upcoming_matches as $match) {
                if (isset($match['is_featured']) && $match['is_featured']) {
                    $featured_count++;
                }
            }
        }
        return $featured_count;
    },
    'عدد المباريات المميزة في القائمة'
);

// اختبار 5: الشعارات
testFunction(
    'شعارات الفرق',
    function() use ($upcoming_matches) {
        $logos_count = 0;
        if (!empty($upcoming_matches)) {
            foreach ($upcoming_matches as $match) {
                if (!empty($match['home_team_logo'])) $logos_count++;
                if (!empty($match['away_team_logo'])) $logos_count++;
            }
        }
        return $logos_count;
    },
    'عدد شعارات الفرق المتوفرة'
);

// عرض النتائج
echo "<div class='grid grid-cols-1 gap-4 mb-8'>";

foreach ($test_results as $result) {
    $status_color = $result['status'] === 'success' ? 'green' : 'red';
    $status_icon = $result['status'] === 'success' ? 'check-circle' : 'times-circle';
    $status_text = $result['status'] === 'success' ? 'نجح' : 'فشل';
    
    echo "<div class='bg-{$status_color}-50 border border-{$status_color}-200 rounded-lg p-4'>
            <div class='flex items-center justify-between mb-2'>
                <div class='flex items-center'>
                    <i class='fas fa-{$status_icon} text-{$status_color}-600 ml-3'></i>
                    <div>
                        <h3 class='font-semibold text-{$status_color}-800'>{$result['function']}</h3>
                        <p class='text-sm text-{$status_color}-700'>{$result['description']}</p>
                    </div>
                </div>
                <div class='text-left'>
                    <div class='text-sm text-{$status_color}-600'>النتائج: {$result['count']}</div>
                    <div class='text-xs text-{$status_color}-500'>الوقت: {$result['time']}</div>
                </div>
            </div>";
    
    if ($result['status'] === 'success' && $result['data']) {
        echo "<div class='mt-3 p-3 bg-white rounded border'>
                <h4 class='font-medium text-gray-800 mb-2'>عينة من البيانات:</h4>
                <pre class='text-xs text-gray-600 overflow-x-auto'>" . 
                htmlspecialchars(json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . 
                "</pre>
              </div>";
    }
    
    if ($result['status'] === 'error') {
        echo "<div class='mt-3 p-3 bg-red-100 rounded border border-red-300'>
                <h4 class='font-medium text-red-800 mb-2'>تفاصيل الخطأ:</h4>
                <p class='text-sm text-red-700'>{$result['message']}</p>
              </div>";
    }
    
    echo "</div>";
}

echo "</div>";

// ملخص النتائج
$success_count = count(array_filter($test_results, function($r) { return $r['status'] === 'success'; }));
$total_count = count($test_results);
$success_rate = round(($success_count / $total_count) * 100, 1);

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6'>
        <h2 class='text-xl font-semibold mb-4 text-blue-800'>
            <i class='fas fa-chart-pie ml-2'></i>ملخص النتائج
        </h2>
        
        <div class='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div class='bg-white p-4 rounded-lg text-center'>
                <div class='text-2xl font-bold text-blue-600'>$total_count</div>
                <div class='text-sm text-blue-800'>إجمالي الاختبارات</div>
            </div>
            
            <div class='bg-white p-4 rounded-lg text-center'>
                <div class='text-2xl font-bold text-green-600'>$success_count</div>
                <div class='text-sm text-green-800'>اختبارات ناجحة</div>
            </div>
            
            <div class='bg-white p-4 rounded-lg text-center'>
                <div class='text-2xl font-bold text-purple-600'>$success_rate%</div>
                <div class='text-sm text-purple-800'>معدل النجاح</div>
            </div>
        </div>
      </div>";

// معاينة جدول المباريات
if ($upcoming_matches && $success_count > 0) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-6'>
            <h2 class='text-xl font-semibold mb-4 text-green-800'>
                <i class='fas fa-eye ml-2'></i>معاينة جدول المباريات
            </h2>
            
            <div class='bg-white rounded-lg p-4 border'>
                <div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>";
    
    foreach (array_slice($upcoming_matches, 0, 3) as $match) {
        $featured_class = $match['is_featured'] ? 'border-yellow-400 bg-yellow-50' : 'border-gray-200 bg-gray-50';
        
        echo "<div class='$featured_class border rounded-lg p-3'>
                <div class='flex items-center justify-between mb-2'>
                    <div class='text-xs text-gray-600 bg-blue-100 px-2 py-1 rounded-full'>
                        " . formatMatchDate($match['match_date'], 'date_only') . "
                    </div>
                    <div class='text-xs text-gray-500'>
                        " . mb_substr($match['competition'], 0, 15) . "
                    </div>
                </div>
                
                <div class='flex items-center justify-between mb-2'>
                    <div class='flex items-center flex-1'>
                        <span class='text-sm font-semibold text-gray-800 truncate'>
                            " . mb_substr($match['home_team'], 0, 12) . "
                        </span>
                    </div>
                    <div class='mx-2'>
                        <span class='text-xs text-gray-400 font-bold'>VS</span>
                    </div>
                    <div class='flex items-center flex-1 justify-end'>
                        <span class='text-sm font-semibold text-gray-800 truncate'>
                            " . mb_substr($match['away_team'], 0, 12) . "
                        </span>
                    </div>
                </div>
                
                <div class='text-center text-xs text-gray-600'>
                    <i class='fas fa-clock ml-1'></i>
                    " . formatMatchDate($match['match_date'], 'time_only') . "
                </div>";
        
        if ($match['is_featured']) {
            echo "<div class='mt-2 text-center'>
                    <span class='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800'>
                        <i class='fas fa-star ml-1'></i>مباراة مميزة
                    </span>
                  </div>";
        }
        
        echo "</div>";
    }
    
    echo "        </div>
            </div>
          </div>";
}

// التوصيات
if ($success_count === $total_count) {
    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6'>
            <div class='flex items-center'>
                <i class='fas fa-check-circle text-green-600 ml-2'></i>
                <strong>ممتاز!</strong> جدول المباريات جاهز للعرض في الصفحة الرئيسية. جميع الاختبارات نجحت.
            </div>
          </div>";
} else {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6'>
            <div class='flex items-center'>
                <i class='fas fa-exclamation-triangle text-red-600 ml-2'></i>
                <strong>تحذير!</strong> هناك مشاكل في بعض الاختبارات. يرجى مراجعة الأخطاء أعلاه.
            </div>
          </div>";
}

// روابط سريعة
echo "<div class='bg-white border border-gray-200 rounded-lg p-6'>
        <h2 class='text-xl font-semibold mb-4 text-gray-800'>
            <i class='fas fa-link ml-2'></i>اختبار الصفحات
        </h2>
        
        <div class='grid grid-cols-2 md:grid-cols-4 gap-4'>
            <a href='index.php' class='bg-blue-600 text-white px-4 py-3 rounded text-center hover:bg-blue-700 transition-colors'>
                <i class='fas fa-home block mb-1'></i>الصفحة الرئيسية
            </a>
            
            <a href='matches.php' class='bg-green-600 text-white px-4 py-3 rounded text-center hover:bg-green-700 transition-colors'>
                <i class='fas fa-futbol block mb-1'></i>صفحة المباريات
            </a>
            
            <a href='admin/matches.php' class='bg-purple-600 text-white px-4 py-3 rounded text-center hover:bg-purple-700 transition-colors'>
                <i class='fas fa-cogs block mb-1'></i>إدارة المباريات
            </a>
            
            <a href='test-sql-fix.php' class='bg-orange-600 text-white px-4 py-3 rounded text-center hover:bg-orange-700 transition-colors'>
                <i class='fas fa-vial block mb-1'></i>اختبار SQL
            </a>
        </div>
      </div>";

echo "        </div>
    </div>
</body>
</html>";
?>
