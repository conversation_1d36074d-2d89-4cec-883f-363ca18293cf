/*
 * Design System - نظام التصميم الموحد
 * ملف CSS رئيسي يحتوي على جميع المتغيرات والقواعد الأساسية
 */

/* ==========================================================================
   CSS Variables - المتغيرات الأساسية
   ========================================================================== */

:root {
    /* Primary Colors - الألوان الأساسية */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Secondary Colors - الألوان الثانوية */
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;

    /* Success Colors - ألوان النجاح */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;

    /* Warning Colors - ألوان التحذير */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;

    /* Error Colors - ألوان الخطأ */
    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-200: #fecaca;
    --error-300: #fca5a5;
    --error-400: #f87171;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    --error-800: #991b1b;
    --error-900: #7f1d1d;

    /* Neutral Colors - الألوان المحايدة */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;

    /* Typography - الخطوط */
    --font-family-primary: 'Tajawal', 'Arial', sans-serif;
    --font-family-secondary: 'Inter', 'Arial', sans-serif;

    /* Font Sizes - أحجام الخطوط */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */
    --text-5xl: 3rem;        /* 48px */
    --text-6xl: 3.75rem;     /* 60px */

    /* Font Weights - أوزان الخطوط */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;

    /* Line Heights - ارتفاع الأسطر */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Spacing - المسافات */
    --space-1: 0.25rem;      /* 4px */
    --space-2: 0.5rem;       /* 8px */
    --space-3: 0.75rem;      /* 12px */
    --space-4: 1rem;         /* 16px */
    --space-5: 1.25rem;      /* 20px */
    --space-6: 1.5rem;       /* 24px */
    --space-8: 2rem;         /* 32px */
    --space-10: 2.5rem;      /* 40px */
    --space-12: 3rem;        /* 48px */
    --space-16: 4rem;        /* 64px */
    --space-20: 5rem;        /* 80px */
    --space-24: 6rem;        /* 96px */

    /* Border Radius - انحناء الحواف */
    --radius-sm: 0.125rem;   /* 2px */
    --radius-base: 0.25rem;  /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-3xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;

    /* Shadows - الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transitions - الانتقالات */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;

    /* Z-Index - ترتيب العناصر */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;

    /* Breakpoints - نقاط التوقف */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* ==========================================================================
   Base Styles - الأنماط الأساسية
   ========================================================================== */

* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    color: var(--neutral-800);
    background-color: var(--neutral-50);
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ==========================================================================
   Typography - الطباعة
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin: 0 0 var(--space-4) 0;
    color: var(--neutral-900);
}

h1 {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
}

h2 {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
}

h3 {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
}

h4 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
}

h5 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
}

h6 {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
}

p {
    margin: 0 0 var(--space-4) 0;
    line-height: var(--leading-relaxed);
}

a {
    color: var(--primary-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-700);
}

a:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* ==========================================================================
   Layout Components - مكونات التخطيط
   ========================================================================== */

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--space-6);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--space-8);
    }
}

/* Grid System - نظام الشبكة */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Flexbox Utilities - أدوات Flexbox */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.space-x-4 > * + * {
    margin-right: var(--space-4);
}

.space-y-4 > * + * {
    margin-top: var(--space-4);
}

/* ==========================================================================
   Component Base Classes - الفئات الأساسية للمكونات
   ========================================================================== */

/* Cards - البطاقات */
.card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-base);
    overflow: hidden;
    transition: all var(--transition-base);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Buttons - الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    line-height: 1;
}

.btn-primary {
    background-color: var(--primary-600);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-700);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-200);
    color: var(--secondary-800);
}

.btn-secondary:hover {
    background-color: var(--secondary-300);
    color: var(--secondary-900);
}

.btn-success {
    background-color: var(--success-600);
    color: white;
}

.btn-success:hover {
    background-color: var(--success-700);
    color: white;
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
}

/* Badges - الشارات */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    border-radius: var(--radius-full);
}

.badge-primary {
    background-color: var(--primary-100);
    color: var(--primary-800);
}

.badge-success {
    background-color: var(--success-100);
    color: var(--success-800);
}

.badge-warning {
    background-color: var(--warning-100);
    color: var(--warning-800);
}

.badge-error {
    background-color: var(--error-100);
    color: var(--error-800);
}

/* ==========================================================================
   Responsive Design - التصميم المتجاوب
   ========================================================================== */

@media (max-width: 767px) {
    .container {
        padding: 0 var(--space-4);
    }
    
    h1 {
        font-size: var(--text-3xl);
    }
    
    h2 {
        font-size: var(--text-2xl);
    }
    
    h3 {
        font-size: var(--text-xl);
    }
}

/* ==========================================================================
   Accessibility - إمكانية الوصول
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Focus Styles - أنماط التركيز */
*:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Screen Reader Only - للقارئات الشاشة فقط */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
