<?php
/**
 * اختبار دالة getUpcomingMatches
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/matches_functions.php';

try {
    echo "<h1>اختبار دالة getUpcomingMatches</h1>";
    
    // اختبار الاتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->connect();
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // التحقق من وجود جدول المباريات
    $stmt = $db->query("SHOW TABLES LIKE 'matches'");
    if ($stmt->fetch()) {
        echo "<p>✅ جدول المباريات موجود</p>";
    } else {
        echo "<p>❌ جدول المباريات غير موجود</p>";
        exit;
    }
    
    // عرض جميع المباريات في قاعدة البيانات
    $stmt = $db->query("SELECT * FROM matches ORDER BY match_date ASC");
    $all_matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>جميع المباريات في قاعدة البيانات (" . count($all_matches) . " مباراة):</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الفريق المضيف</th><th>الفريق الضيف</th><th>تاريخ المباراة</th><th>الحالة</th><th>مميزة</th></tr>";
    
    foreach ($all_matches as $match) {
        echo "<tr>";
        echo "<td>" . $match['id'] . "</td>";
        echo "<td>" . $match['home_team'] . "</td>";
        echo "<td>" . $match['away_team'] . "</td>";
        echo "<td>" . $match['match_date'] . "</td>";
        echo "<td>" . $match['status'] . "</td>";
        echo "<td>" . ($match['is_featured'] ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض التاريخ والوقت الحالي
    echo "<h2>التاريخ والوقت الحالي:</h2>";
    echo "<p>التاريخ الحالي: " . date('Y-m-d H:i:s') . "</p>";
    echo "<p>المنطقة الزمنية: " . date_default_timezone_get() . "</p>";
    
    // اختبار دالة getUpcomingMatches
    echo "<h2>اختبار دالة getUpcomingMatches:</h2>";
    
    $upcoming_matches = getUpcomingMatches(10);
    echo "<p>عدد المباريات القادمة: " . count($upcoming_matches) . "</p>";
    
    if (!empty($upcoming_matches)) {
        echo "<h3>المباريات القادمة:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الفريق المضيف</th><th>الفريق الضيف</th><th>تاريخ المباراة</th><th>البطولة</th><th>مميزة</th></tr>";
        
        foreach ($upcoming_matches as $match) {
            echo "<tr>";
            echo "<td>" . $match['id'] . "</td>";
            echo "<td>" . $match['home_team'] . "</td>";
            echo "<td>" . $match['away_team'] . "</td>";
            echo "<td>" . $match['match_date'] . "</td>";
            echo "<td>" . $match['competition'] . "</td>";
            echo "<td>" . ($match['is_featured'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ لا توجد مباريات قادمة</p>";
        
        // التحقق من السبب
        echo "<h3>تحليل السبب:</h3>";
        
        // عدد المباريات المجدولة
        $stmt = $db->query("SELECT COUNT(*) FROM matches WHERE status = 'scheduled'");
        $scheduled_count = $stmt->fetchColumn();
        echo "<p>عدد المباريات المجدولة: " . $scheduled_count . "</p>";
        
        // عدد المباريات في المستقبل
        $stmt = $db->query("SELECT COUNT(*) FROM matches WHERE match_date > NOW()");
        $future_count = $stmt->fetchColumn();
        echo "<p>عدد المباريات في المستقبل: " . $future_count . "</p>";
        
        // عدد المباريات المجدولة في المستقبل
        $stmt = $db->query("SELECT COUNT(*) FROM matches WHERE match_date > NOW() AND status = 'scheduled'");
        $upcoming_count = $stmt->fetchColumn();
        echo "<p>عدد المباريات المجدولة في المستقبل: " . $upcoming_count . "</p>";
        
        // عرض المباريات القريبة من التاريخ الحالي
        echo "<h3>المباريات القريبة من التاريخ الحالي:</h3>";
        $stmt = $db->query("SELECT *, 
                           TIMESTAMPDIFF(HOUR, NOW(), match_date) as hours_diff 
                           FROM matches 
                           ORDER BY ABS(TIMESTAMPDIFF(HOUR, NOW(), match_date)) ASC 
                           LIMIT 5");
        $nearby_matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الفريق المضيف</th><th>الفريق الضيف</th><th>تاريخ المباراة</th><th>الفرق بالساعات</th><th>الحالة</th></tr>";
        
        foreach ($nearby_matches as $match) {
            echo "<tr>";
            echo "<td>" . $match['home_team'] . "</td>";
            echo "<td>" . $match['away_team'] . "</td>";
            echo "<td>" . $match['match_date'] . "</td>";
            echo "<td>" . $match['hours_diff'] . " ساعة</td>";
            echo "<td>" . $match['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
