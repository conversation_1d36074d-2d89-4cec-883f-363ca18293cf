<?php
/**
 * Test file to verify RSSParser path fix
 */

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح مسار RSSParser</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
    <div class='container mx-auto px-4 py-8'>
        <div class='bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto'>
            <h1 class='text-2xl font-bold text-center mb-6 text-gray-800'>
                <i class='fas fa-vial ml-2 text-blue-600'></i>
                اختبار إصلاح مسار RSSParser
            </h1>";

$tests = [];

// Test 1: Include RSSParser class
echo "<div class='mb-4'>";
echo "<h3 class='font-semibold text-lg mb-2'>1. اختبار تضمين فئة RSSParser</h3>";
try {
    require_once 'classes/RSSParser.php';
    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>";
    echo "<i class='fas fa-check ml-2'></i>تم تضمين فئة RSSParser بنجاح";
    echo "</div>";
    $tests['include'] = true;
} catch (Exception $e) {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>";
    echo "<i class='fas fa-times ml-2'></i>فشل في تضمين فئة RSSParser: " . $e->getMessage();
    echo "</div>";
    $tests['include'] = false;
}
echo "</div>";

// Test 2: Instantiate RSSParser
echo "<div class='mb-4'>";
echo "<h3 class='font-semibold text-lg mb-2'>2. اختبار إنشاء كائن RSSParser</h3>";
if ($tests['include']) {
    try {
        $rssParser = new RSSParser();
        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>";
        echo "<i class='fas fa-check ml-2'></i>تم إنشاء كائن RSSParser بنجاح";
        echo "</div>";
        $tests['instantiate'] = true;
    } catch (Exception $e) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>";
        echo "<i class='fas fa-times ml-2'></i>فشل في إنشاء كائن RSSParser: " . $e->getMessage();
        echo "</div>";
        $tests['instantiate'] = false;
    }
} else {
    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>";
    echo "<i class='fas fa-exclamation-triangle ml-2'></i>تم تخطي هذا الاختبار بسبب فشل الاختبار السابق";
    echo "</div>";
    $tests['instantiate'] = false;
}
echo "</div>";

// Test 3: Test RSS feed method
echo "<div class='mb-4'>";
echo "<h3 class='font-semibold text-lg mb-2'>3. اختبار دالة testRSSFeed</h3>";
if ($tests['instantiate']) {
    try {
        // Test with a simple RSS URL
        $test_url = 'https://feeds.bbci.co.uk/arabic/rss.xml';
        $result = $rssParser->testRSSFeed($test_url);
        
        if ($result['success']) {
            echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>";
            echo "<i class='fas fa-check ml-2'></i>دالة testRSSFeed تعمل بنجاح";
            echo "<br><small>تم العثور على {$result['items_count']} عنصر في الموجز</small>";
            echo "</div>";
            $tests['test_method'] = true;
        } else {
            echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>";
            echo "<i class='fas fa-exclamation-triangle ml-2'></i>دالة testRSSFeed تعمل لكن فشل في اختبار الموجز: " . $result['error'];
            echo "</div>";
            $tests['test_method'] = true; // الدالة تعمل حتى لو فشل الاختبار
        }
    } catch (Exception $e) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>";
        echo "<i class='fas fa-times ml-2'></i>فشل في اختبار دالة testRSSFeed: " . $e->getMessage();
        echo "</div>";
        $tests['test_method'] = false;
    }
} else {
    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>";
    echo "<i class='fas fa-exclamation-triangle ml-2'></i>تم تخطي هذا الاختبار بسبب فشل الاختبار السابق";
    echo "</div>";
    $tests['test_method'] = false;
}
echo "</div>";

// Test 4: Check database connection in RSSParser
echo "<div class='mb-4'>";
echo "<h3 class='font-semibold text-lg mb-2'>4. اختبار اتصال قاعدة البيانات في RSSParser</h3>";
if ($tests['instantiate']) {
    try {
        // Try to access a method that uses database
        require_once 'config/database.php';
        $database = new Database();
        $db = $database->connect();
        
        if ($db) {
            echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>";
            echo "<i class='fas fa-check ml-2'></i>اتصال قاعدة البيانات يعمل بنجاح";
            echo "</div>";
            $tests['database'] = true;
        } else {
            echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>";
            echo "<i class='fas fa-times ml-2'></i>فشل في الاتصال بقاعدة البيانات";
            echo "</div>";
            $tests['database'] = false;
        }
    } catch (Exception $e) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>";
        echo "<i class='fas fa-times ml-2'></i>خطأ في اتصال قاعدة البيانات: " . $e->getMessage();
        echo "</div>";
        $tests['database'] = false;
    }
} else {
    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>";
    echo "<i class='fas fa-exclamation-triangle ml-2'></i>تم تخطي هذا الاختبار بسبب فشل الاختبار السابق";
    echo "</div>";
    $tests['database'] = false;
}
echo "</div>";

// Summary
$passed = array_sum($tests);
$total = count($tests);
$success_rate = $total > 0 ? round(($passed / $total) * 100, 1) : 0;

echo "<div class='mt-8 p-6 bg-gray-50 rounded-lg'>";
echo "<h2 class='text-xl font-bold mb-4'>ملخص النتائج</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>";
echo "<div class='bg-green-100 p-4 rounded-lg text-center'>";
echo "<div class='text-2xl font-bold text-green-800'>$passed</div>";
echo "<div class='text-green-600'>اختبارات نجحت</div>";
echo "</div>";

echo "<div class='bg-red-100 p-4 rounded-lg text-center'>";
$failed = $total - $passed;
echo "<div class='text-2xl font-bold text-red-800'>$failed</div>";
echo "<div class='text-red-600'>اختبارات فشلت</div>";
echo "</div>";

echo "<div class='bg-blue-100 p-4 rounded-lg text-center'>";
echo "<div class='text-2xl font-bold text-blue-800'>$success_rate%</div>";
echo "<div class='text-blue-600'>معدل النجاح</div>";
echo "</div>";
echo "</div>";

if ($success_rate >= 75) {
    echo "<div class='bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded'>";
    echo "<div class='flex items-center'>";
    echo "<i class='fas fa-check-circle text-green-600 text-2xl ml-3'></i>";
    echo "<div>";
    echo "<h3 class='font-bold text-lg'>ممتاز! تم إصلاح المشكلة</h3>";
    echo "<p>فئة RSSParser تعمل بشكل صحيح الآن. يمكنك استخدام وظائف RSS في لوحة التحكم.</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded'>";
    echo "<div class='flex items-center'>";
    echo "<i class='fas fa-times-circle text-red-600 text-2xl ml-3'></i>";
    echo "<div>";
    echo "<h3 class='font-bold text-lg'>لا تزال هناك مشاكل</h3>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل استخدام وظائف RSS.</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

echo "<div class='mt-6 text-center'>";
echo "<a href='admin/rss-sources.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors ml-4'>";
echo "<i class='fas fa-rss ml-2'></i>اختبار إدارة مصادر RSS";
echo "</a>";
echo "<a href='test-complete.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors'>";
echo "<i class='fas fa-vial ml-2'></i>اختبار شامل للنظام";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
