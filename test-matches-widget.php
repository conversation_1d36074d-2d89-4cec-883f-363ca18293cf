<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جدول المباريات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/matches-widget.css">
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .test-section { margin: 2rem 0; padding: 1.5rem; border: 2px solid #e5e7eb; border-radius: 12px; }
        .test-pass { color: #059669; font-weight: 600; }
        .test-fail { color: #dc2626; font-weight: 600; }
        .test-info { color: #2563eb; font-weight: 500; }
    </style>
</head>
<body class="bg-gray-50 p-4">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
            <i class="fas fa-futbol ml-2 text-green-600"></i>
            اختبار جدول المباريات
        </h1>

        <!-- Test 1: CSS Loading -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">
                <i class="fas fa-palette ml-2"></i>
                اختبار تحميل ملفات CSS
            </h2>
            <div id="css-test">
                <p class="test-info">جاري فحص تحميل ملفات CSS...</p>
            </div>
        </div>

        <!-- Test 2: JavaScript Loading -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">
                <i class="fas fa-code ml-2"></i>
                اختبار تحميل JavaScript
            </h2>
            <div id="js-test">
                <p class="test-info">جاري فحص تحميل JavaScript...</p>
            </div>
        </div>

        <!-- Test 3: Database Connection -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">
                <i class="fas fa-database ml-2"></i>
                اختبار الاتصال بقاعدة البيانات
            </h2>
            <div id="db-test">
                <?php
                try {
                    require_once 'config/database.php';
                    require_once 'includes/matches_functions.php';
                    
                    $database = new Database();
                    $db = $database->connect();
                    
                    if ($db) {
                        echo '<p class="test-pass"><i class="fas fa-check ml-1"></i> تم الاتصال بقاعدة البيانات بنجاح</p>';
                        
                        // Test matches table
                        $stmt = $db->query("SHOW TABLES LIKE 'matches'");
                        if ($stmt->rowCount() > 0) {
                            echo '<p class="test-pass"><i class="fas fa-check ml-1"></i> جدول المباريات موجود</p>';
                            
                            // Count matches
                            $stmt = $db->query("SELECT COUNT(*) FROM matches");
                            $count = $stmt->fetchColumn();
                            echo '<p class="test-info"><i class="fas fa-info-circle ml-1"></i> عدد المباريات في قاعدة البيانات: ' . $count . '</p>';
                            
                            if ($count > 0) {
                                echo '<p class="test-pass"><i class="fas fa-check ml-1"></i> توجد بيانات مباريات للعرض</p>';
                            } else {
                                echo '<p class="test-fail"><i class="fas fa-times ml-1"></i> لا توجد بيانات مباريات (قم بتشغيل add_sample_matches.php)</p>';
                            }
                        } else {
                            echo '<p class="test-fail"><i class="fas fa-times ml-1"></i> جدول المباريات غير موجود</p>';
                        }
                    } else {
                        echo '<p class="test-fail"><i class="fas fa-times ml-1"></i> فشل الاتصال بقاعدة البيانات</p>';
                    }
                } catch (Exception $e) {
                    echo '<p class="test-fail"><i class="fas fa-times ml-1"></i> خطأ: ' . $e->getMessage() . '</p>';
                }
                ?>
            </div>
        </div>

        <!-- Test 4: Widget Display -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">
                <i class="fas fa-eye ml-2"></i>
                اختبار عرض جدول المباريات
            </h2>
            
            <?php
            try {
                $upcoming_matches = getUpcomingMatches(9);
                if (!empty($upcoming_matches)):
            ?>
            
            <!-- Football Matches Widget -->
            <section class="bg-gradient-to-r from-green-600 to-blue-600 py-8 mb-8 matches-widget">
                <div class="container mx-auto px-4">
                    <div class="bg-white rounded-xl shadow-2xl overflow-hidden relative z-10">
                        <!-- Header -->
                        <div class="widget-header bg-gradient-to-r from-green-500 to-blue-500 text-white p-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 rounded-full p-3 ml-4">
                                        <i class="fas fa-futbol text-2xl"></i>
                                    </div>
                                    <div>
                                        <h2 class="text-2xl font-bold">مواعيد المباريات</h2>
                                        <p class="text-green-100 text-sm">المباريات القادمة</p>
                                    </div>
                                </div>
                                <div class="hidden md:flex items-center space-x-4 space-x-reverse">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold"><?php echo count($upcoming_matches); ?></div>
                                        <div class="text-xs text-green-100">مباراة قادمة</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Matches Slider -->
                        <div class="p-6">
                            <div class="matches-slider" id="matchesSlider">
                                <!-- Navigation Buttons -->
                                <button class="slider-nav-btn prev" id="prevBtn" aria-label="المباراة السابقة">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                                <button class="slider-nav-btn next" id="nextBtn" aria-label="المباراة التالية">
                                    <i class="fas fa-chevron-left"></i>
                                </button>

                                <!-- Slider Container -->
                                <div class="matches-slider-container" id="sliderContainer">
                                    <?php
                                    $matches_per_slide = 3;
                                    $all_matches = array_slice($upcoming_matches, 0, 9);
                                    $total_slides = ceil(count($all_matches) / 3);

                                    for ($slide = 0; $slide < $total_slides; $slide++):
                                        $slide_matches = array_slice($all_matches, $slide * 3, 3);
                                    ?>
                                    <div class="matches-slide">
                                        <?php foreach ($slide_matches as $match): ?>
                                        <div class="match-card <?php echo $match['is_featured'] ? 'featured-match' : 'bg-gray-50'; ?> rounded-lg p-4 border border-gray-200">
                                            <!-- Match Date & Competition -->
                                            <div class="flex items-center justify-between mb-3">
                                                <div class="match-date-badge">
                                                    <i class="fas fa-calendar ml-1"></i>
                                                    <?php echo formatMatchDate($match['match_date'], 'date_only'); ?>
                                                </div>
                                                <div class="competition-badge">
                                                    <?php echo mb_substr($match['competition'], 0, 15) . (mb_strlen($match['competition']) > 15 ? '...' : ''); ?>
                                                </div>
                                            </div>

                                            <!-- Teams -->
                                            <div class="flex items-center justify-between mb-3">
                                                <!-- Home Team -->
                                                <div class="flex items-center flex-1">
                                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center ml-2">
                                                        <i class="fas fa-shield-alt text-gray-500 text-xs"></i>
                                                    </div>
                                                    <span class="team-name truncate">
                                                        <?php echo mb_substr($match['home_team'], 0, 12) . (mb_strlen($match['home_team']) > 12 ? '...' : ''); ?>
                                                    </span>
                                                </div>

                                                <!-- VS -->
                                                <div class="mx-3">
                                                    <span class="vs-indicator">VS</span>
                                                </div>

                                                <!-- Away Team -->
                                                <div class="flex items-center flex-1 justify-end">
                                                    <span class="team-name truncate">
                                                        <?php echo mb_substr($match['away_team'], 0, 12) . (mb_strlen($match['away_team']) > 12 ? '...' : ''); ?>
                                                    </span>
                                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-2">
                                                        <i class="fas fa-shield-alt text-gray-500 text-xs"></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Match Time & Status -->
                                            <div class="flex items-center justify-between">
                                                <div class="match-time">
                                                    <i class="fas fa-clock ml-1"></i>
                                                    <?php echo formatMatchDate($match['match_date'], 'time_only'); ?>
                                                </div>
                                                <?php if ($match['venue']): ?>
                                                <div class="match-venue">
                                                    <i class="fas fa-map-marker-alt ml-1"></i>
                                                    <?php echo mb_substr($match['venue'], 0, 10) . (mb_strlen($match['venue']) > 10 ? '...' : ''); ?>
                                                </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Featured Match Indicator -->
                                            <?php if ($match['is_featured']): ?>
                                            <div class="mt-2 text-center">
                                                <span class="featured-indicator">
                                                    <i class="fas fa-star ml-1"></i>مباراة مميزة
                                                </span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php endfor; ?>
                                </div>

                                <!-- Slider Dots -->
                                <div class="slider-dots" id="sliderDots">
                                    <?php for ($i = 0; $i < $total_slides; $i++): ?>
                                    <button class="slider-dot <?php echo $i === 0 ? 'active' : ''; ?>"
                                            data-slide="<?php echo $i; ?>"
                                            aria-label="الشريحة <?php echo $i + 1; ?>"></button>
                                    <?php endfor; ?>
                                </div>
                            </div>

                            <!-- View All Matches Button -->
                            <div class="mt-6 text-center">
                                <a href="matches.php" class="view-all-btn">
                                    <i class="fas fa-futbol ml-2"></i>
                                    عرض جميع المباريات
                                    <i class="fas fa-arrow-left mr-2"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div class="test-section">
                <p class="test-pass"><i class="fas fa-check ml-1"></i> تم عرض جدول المباريات بنجاح</p>
                <p class="test-info"><i class="fas fa-info-circle ml-1"></i> عدد المباريات المعروضة: <?php echo count($upcoming_matches); ?></p>
                <p class="test-info"><i class="fas fa-info-circle ml-1"></i> عدد الشرائح: <?php echo $total_slides; ?></p>
            </div>

            <?php else: ?>
            <div class="test-section">
                <p class="test-fail"><i class="fas fa-times ml-1"></i> لا توجد مباريات للعرض</p>
                <p class="test-info">قم بتشغيل الأمر: <code>php add_sample_matches.php</code></p>
            </div>
            <?php endif; ?>

            <?php
            } catch (Exception $e) {
                echo '<div class="test-section">';
                echo '<p class="test-fail"><i class="fas fa-times ml-1"></i> خطأ في عرض المباريات: ' . $e->getMessage() . '</p>';
                echo '</div>';
            }
            ?>
        </div>

        <!-- Test Results Summary -->
        <div class="test-section bg-blue-50 border-blue-200">
            <h2 class="text-xl font-semibold mb-4 text-blue-800">
                <i class="fas fa-clipboard-check ml-2"></i>
                ملخص نتائج الاختبار
            </h2>
            <div id="test-summary">
                <p class="test-info">جاري تجميع نتائج الاختبار...</p>
            </div>
        </div>
    </div>

    <!-- Load JavaScript -->
    <script src="assets/js/matches-widget.js"></script>
    
    <script>
        // Test CSS Loading
        function testCSS() {
            const testElement = document.createElement('div');
            testElement.className = 'matches-widget';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);
            
            const styles = window.getComputedStyle(testElement);
            const hasCSS = styles.position === 'relative';
            
            document.body.removeChild(testElement);
            
            const cssTest = document.getElementById('css-test');
            if (hasCSS) {
                cssTest.innerHTML = '<p class="test-pass"><i class="fas fa-check ml-1"></i> تم تحميل ملف CSS بنجاح</p>';
                return true;
            } else {
                cssTest.innerHTML = '<p class="test-fail"><i class="fas fa-times ml-1"></i> فشل تحميل ملف CSS</p>';
                return false;
            }
        }

        // Test JavaScript Loading
        function testJS() {
            const jsTest = document.getElementById('js-test');
            if (typeof MatchesWidget !== 'undefined') {
                jsTest.innerHTML = '<p class="test-pass"><i class="fas fa-check ml-1"></i> تم تحميل ملف JavaScript بنجاح</p>';
                return true;
            } else {
                jsTest.innerHTML = '<p class="test-fail"><i class="fas fa-times ml-1"></i> فشل تحميل ملف JavaScript</p>';
                return false;
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const cssResult = testCSS();
                const jsResult = testJS();
                
                // Update summary
                const summary = document.getElementById('test-summary');
                let passCount = 0;
                let totalTests = 4;
                
                if (cssResult) passCount++;
                if (jsResult) passCount++;
                
                // Check if widget is displayed
                const widget = document.getElementById('matchesSlider');
                if (widget) passCount++;
                
                // Check database (from PHP)
                const dbTest = document.getElementById('db-test');
                if (dbTest.innerHTML.includes('test-pass')) passCount++;
                
                const percentage = Math.round((passCount / totalTests) * 100);
                
                summary.innerHTML = `
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center p-4 bg-white rounded-lg">
                            <div class="text-2xl font-bold text-green-600">${passCount}/${totalTests}</div>
                            <div class="text-sm text-gray-600">اختبارات نجحت</div>
                        </div>
                        <div class="text-center p-4 bg-white rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">${percentage}%</div>
                            <div class="text-sm text-gray-600">معدل النجاح</div>
                        </div>
                    </div>
                    ${percentage === 100 ? 
                        '<p class="test-pass text-center"><i class="fas fa-trophy ml-1"></i> جميع الاختبارات نجحت! جدول المباريات يعمل بشكل مثالي</p>' :
                        '<p class="test-fail text-center"><i class="fas fa-exclamation-triangle ml-1"></i> بعض الاختبارات فشلت. يرجى مراجعة التفاصيل أعلاه</p>'
                    }
                `;
            }, 1000);
        });
    </script>
</body>
</html>
