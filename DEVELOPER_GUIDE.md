# دليل المطور - موقع الأخبار PHP

## 🎯 نظرة عامة

هذا دليل شامل للمطورين الذين يريدون فهم أو تطوير موقع الأخبار PHP.

## 🏗️ هيكل النظام

### الملفات الأساسية

```
├── config/
│   ├── config.php          # الإعدادات العامة والثوابت
│   └── database.php        # إعدادات قاعدة البيانات وإنشاء الجداول
├── includes/
│   ├── functions.php       # الدوال المساعدة الأساسية
│   ├── header.php          # رأس الصفحة للموقع الرئيسي
│   └── footer.php          # تذييل الصفحة للموقع الرئيسي
├── classes/
│   └── RSSParser.php       # فئة تحليل وجلب RSS
├── admin/
│   ├── includes/
│   │   ├── auth.php        # نظام المصادقة
│   │   ├── header.php      # رأس لوحة التحكم
│   │   └── footer.php      # تذييل لوحة التحكم
│   └── [صفحات الإدارة]
└── cron/
    └── fetch-rss.php       # مهمة جلب RSS المجدولة
```

## 🗄️ قاعدة البيانات

### الجداول الأساسية

#### جدول `articles`
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- title (VARCHAR(500)) - عنوان المقال
- slug (VARCHAR(500)) - الرابط الودود
- content (LONGTEXT) - محتوى المقال
- excerpt (TEXT) - مقتطف المقال
- image_url (VARCHAR(500)) - رابط الصورة
- source_url (VARCHAR(500)) - رابط المصدر
- author (VARCHAR(200)) - الكاتب
- category_id (INT, FOREIGN KEY) - معرف التصنيف
- rss_source_id (INT, FOREIGN KEY) - معرف مصدر RSS
- published_at (TIMESTAMP) - تاريخ النشر
- is_featured (BOOLEAN) - مقال مميز
- views (INT) - عدد المشاهدات
- created_at (TIMESTAMP) - تاريخ الإنشاء
- updated_at (TIMESTAMP) - تاريخ التحديث
```

#### جدول `categories`
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- name (VARCHAR(100)) - اسم التصنيف
- slug (VARCHAR(100)) - الرابط الودود
- description (TEXT) - وصف التصنيف
- created_at (TIMESTAMP) - تاريخ الإنشاء
```

#### جدول `rss_sources`
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- name (VARCHAR(200)) - اسم المصدر
- url (VARCHAR(500)) - رابط RSS
- category_id (INT, FOREIGN KEY) - التصنيف الافتراضي
- fetch_interval (INT) - فترة الجلب بالثواني
- last_fetched (TIMESTAMP) - آخر وقت جلب
- is_active (BOOLEAN) - حالة التفعيل
- created_at (TIMESTAMP) - تاريخ الإنشاء
```

#### جدول `users`
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- username (VARCHAR(50)) - اسم المستخدم
- email (VARCHAR(100)) - البريد الإلكتروني
- password (VARCHAR(255)) - كلمة المرور المشفرة
- full_name (VARCHAR(100)) - الاسم الكامل
- role (ENUM: 'admin', 'editor') - الدور
- is_active (BOOLEAN) - حالة التفعيل
- last_login (TIMESTAMP) - آخر دخول
- created_at (TIMESTAMP) - تاريخ الإنشاء
```

#### جدول `settings`
```sql
- id (INT, PRIMARY KEY, AUTO_INCREMENT)
- setting_key (VARCHAR(100)) - مفتاح الإعداد
- setting_value (TEXT) - قيمة الإعداد
- setting_type (ENUM: 'text', 'number', 'boolean', 'json') - نوع الإعداد
- description (TEXT) - وصف الإعداد
- updated_at (TIMESTAMP) - تاريخ التحديث
```

## 🔧 الدوال الأساسية

### في `includes/functions.php`

#### دوال قاعدة البيانات
```php
getSetting($key, $default)           // جلب إعداد من قاعدة البيانات
updateSetting($key, $value)          // تحديث إعداد
getArticles($page, $per_page, ...)   // جلب المقالات مع التصفح
getArticle($id_or_slug)              // جلب مقال واحد
getFeaturedArticles($limit)          // جلب المقالات المميزة
getCategories()                      // جلب التصنيفات
```

#### دوال المساعدة
```php
createSlug($text)                    // إنشاء slug من النص
createUniqueSlug($text, $table, $id) // إنشاء slug فريد
formatArabicDate($date)              // تنسيق التاريخ بالعربية
sanitizeInput($input)                // تنظيف المدخلات
generatePagination($current, $total, $base) // إنشاء روابط التصفح
```

#### دوال المصادقة
```php
isLoggedIn()                         // التحقق من تسجيل الدخول
isAdmin()                            // التحقق من صلاحيات الإدارة
```

## 🤖 نظام RSS

### فئة `RSSParser`

#### الدوال الرئيسية
```php
testRSSFeed($url)                    // اختبار مصدر RSS
fetchRSSFeed($rss_source_id)         // جلب من مصدر محدد
fetchAllActiveFeeds()                // جلب من جميع المصادر النشطة
```

#### آلية العمل
1. جلب محتوى XML من الرابط
2. تحليل العناصر (RSS أو Atom)
3. استخراج البيانات (العنوان، المحتوى، الصورة، إلخ)
4. تنظيف وتنسيق المحتوى
5. التحقق من عدم وجود المقال مسبقاً
6. حفظ المقال في قاعدة البيانات

## 🔐 نظام المصادقة

### في `admin/includes/auth.php`

```php
checkAuth()                          // التحقق من تسجيل الدخول
requireAdmin()                       // يتطلب صلاحيات إدارة
```

### آلية العمل
1. التحقق من وجود جلسة نشطة
2. التحقق من صحة بيانات المستخدم
3. التحقق من الصلاحيات حسب الحاجة
4. إعادة توجيه إلى صفحة الدخول إذا لزم الأمر

## 📱 واجهة المستخدم

### التقنيات المستخدمة
- **Tailwind CSS**: للتصميم والتنسيق
- **Font Awesome**: للأيقونات
- **Chart.js**: للرسوم البيانية في لوحة التحكم
- **JavaScript**: للتفاعل والـ AJAX

### هيكل الصفحات
```php
// هيكل صفحة عادية
<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
session_start();

// منطق الصفحة هنا

include 'includes/header.php';
?>
<!-- محتوى HTML -->
<?php include 'includes/footer.php'; ?>
```

## 🔄 نظام AJAX

### في لوحة التحكم
- جلب RSS: `admin/fetch-rss.php`
- تحميل المقالات: `admin/articles.php?ajax=load_articles`

### مثال على استخدام AJAX
```javascript
fetch('fetch-rss.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`تم جلب ${data.articles_count} مقال جديد`);
        }
    });
```

## 🛠️ إضافة ميزات جديدة

### إضافة صفحة جديدة في لوحة التحكم

1. **إنشاء الملف**:
```php
<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

checkAuth();
$page_title = 'عنوان الصفحة';

// منطق الصفحة

include 'includes/header.php';
?>
<!-- محتوى الصفحة -->
<?php include 'includes/footer.php'; ?>
```

2. **إضافة رابط في الشريط الجانبي** (`admin/includes/header.php`):
```php
<a href="new-page.php" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors">
    <i class="fas fa-icon ml-3 w-5"></i>
    اسم الصفحة
</a>
```

### إضافة دالة جديدة

1. **في `includes/functions.php`**:
```php
/**
 * وصف الدالة
 */
function newFunction($param1, $param2 = null) {
    $database = new Database();
    $db = $database->connect();
    
    // منطق الدالة
    
    return $result;
}
```

### إضافة جدول جديد

1. **في `config/database.php`** داخل دالة `createDatabase()`:
```php
$pdo->exec("CREATE TABLE IF NOT EXISTS new_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");
```

## 🐛 تصحيح الأخطاء

### ملفات السجلات
- `logs/rss-cron.log`: سجل جلب RSS
- `logs/error.log`: سجل الأخطاء العامة

### تفعيل وضع التصحيح
في `config/config.php`:
```php
define('DEBUG_MODE', true);
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### أدوات الاختبار
- `test-complete.php`: اختبار شامل للنظام
- `test-connection.php`: اختبار الاتصال بقاعدة البيانات

## 🔒 الأمان

### الممارسات المطبقة
1. **Prepared Statements**: لمنع SQL Injection
2. **تشفير كلمات المرور**: باستخدام `password_hash()`
3. **تنظيف المدخلات**: باستخدام `htmlspecialchars()` و `strip_tags()`
4. **التحقق من الجلسات**: في كل صفحة إدارية
5. **التحقق من الصلاحيات**: حسب دور المستخدم

### توصيات إضافية
- استخدم HTTPS في الإنتاج
- قم بتحديث كلمات المرور بانتظام
- احذف ملفات الاختبار في الإنتاج
- قم بعمل نسخ احتياطية دورية

## 📈 الأداء

### تحسينات مطبقة
1. **فهرسة قاعدة البيانات**: على الحقول المهمة
2. **التصفح المحدود**: لتجنب تحميل جميع البيانات
3. **التخزين المؤقت**: للإعدادات والبيانات المتكررة

### تحسينات إضافية ممكنة
- تخزين مؤقت للصفحات
- ضغط الصور
- تحسين استعلامات قاعدة البيانات
- استخدام CDN للملفات الثابتة

## 🚀 النشر

### خطوات النشر
1. رفع الملفات إلى الخادم
2. إعداد قاعدة البيانات
3. تعديل إعدادات الاتصال
4. إعداد Cron Jobs
5. تأمين الملفات والمجلدات
6. اختبار جميع الوظائف

### متطلبات الخادم
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx
- امتدادات PHP المطلوبة

---

هذا الدليل يغطي الجوانب الأساسية للنظام. لأي استفسارات إضافية، راجع الكود المصدري أو ملفات التوثيق الأخرى.
