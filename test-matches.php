<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/matches_functions.php';

session_start();

$page_title = 'اختبار نظام المباريات';

// تشغيل SQL لإنشاء الجداول
$database = new Database();
$db = $database->connect();

$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'create_tables') {
        try {
            $sql_file = file_get_contents('database/matches_table.sql');
            $statements = explode(';', $sql_file);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $db->exec($statement);
                }
            }
            
            $success_message = 'تم إنشاء جداول المباريات بنجاح!';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء إنشاء الجداول: ' . $e->getMessage();
        }
    }
}

// التحقق من وجود الجداول
$tables_exist = false;
try {
    $stmt = $db->query("SHOW TABLES LIKE 'matches'");
    $tables_exist = $stmt->rowCount() > 0;
} catch (Exception $e) {
    // الجداول غير موجودة
}

// الحصول على الإحصائيات إذا كانت الجداول موجودة
$stats = null;
if ($tables_exist) {
    try {
        $stats = getMatchesStats();
    } catch (Exception $e) {
        $error_message = 'خطأ في جلب الإحصائيات: ' . $e->getMessage();
    }
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
            <i class="fas fa-futbol ml-2 text-green-600"></i>
            اختبار نظام المباريات
        </h1>

        <!-- الرسائل -->
        <?php if ($success_message): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-check-circle ml-2"></i><?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-exclamation-triangle ml-2"></i><?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- حالة النظام -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-blue-800">
                <i class="fas fa-info-circle ml-2"></i>حالة النظام
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">قاعدة البيانات</h3>
                    <div class="flex items-center">
                        <?php if ($tables_exist): ?>
                        <i class="fas fa-check-circle text-green-600 ml-2"></i>
                        <span class="text-green-600">جداول المباريات موجودة</span>
                        <?php else: ?>
                        <i class="fas fa-times-circle text-red-600 ml-2"></i>
                        <span class="text-red-600">جداول المباريات غير موجودة</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">ملفات النظام</h3>
                    <div class="space-y-1">
                        <div class="flex items-center text-sm">
                            <i class="fas fa-check-circle text-green-600 ml-2"></i>
                            <span>matches.php - صفحة عرض المباريات</span>
                        </div>
                        <div class="flex items-center text-sm">
                            <i class="fas fa-check-circle text-green-600 ml-2"></i>
                            <span>admin/matches.php - إدارة المباريات</span>
                        </div>
                        <div class="flex items-center text-sm">
                            <i class="fas fa-check-circle text-green-600 ml-2"></i>
                            <span>includes/matches_functions.php - دوال المباريات</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إنشاء الجداول -->
        <?php if (!$tables_exist): ?>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-yellow-800">
                <i class="fas fa-database ml-2"></i>إنشاء جداول قاعدة البيانات
            </h2>
            
            <p class="text-gray-700 mb-4">
                يجب إنشاء جداول قاعدة البيانات أولاً لتشغيل نظام المباريات. سيتم إنشاء الجداول التالية:
            </p>
            
            <ul class="list-disc list-inside text-gray-700 mb-6 space-y-1">
                <li><strong>matches</strong> - جدول المباريات الرئيسي</li>
                <li><strong>competitions</strong> - جدول البطولات</li>
                <li><strong>teams</strong> - جدول الفرق</li>
            </ul>
            
            <form method="POST">
                <input type="hidden" name="action" value="create_tables">
                <button type="submit" class="bg-yellow-600 text-white px-6 py-3 rounded-lg hover:bg-yellow-700 transition-colors">
                    <i class="fas fa-plus ml-2"></i>إنشاء الجداول والبيانات التجريبية
                </button>
            </form>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <?php if ($tables_exist && $stats): ?>
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-green-800">
                <i class="fas fa-chart-bar ml-2"></i>إحصائيات المباريات
            </h2>
            
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-blue-600"><?php echo $stats['total']; ?></div>
                    <div class="text-sm text-blue-800">إجمالي المباريات</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-yellow-600"><?php echo $stats['upcoming']; ?></div>
                    <div class="text-sm text-yellow-800">مباريات قادمة</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-red-600"><?php echo $stats['live']; ?></div>
                    <div class="text-sm text-red-800">مباريات جارية</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-green-600"><?php echo $stats['finished']; ?></div>
                    <div class="text-sm text-green-800">مباريات منتهية</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-purple-600"><?php echo $stats['featured']; ?></div>
                    <div class="text-sm text-purple-800">مباريات مميزة</div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- اختبار الوظائف -->
        <?php if ($tables_exist): ?>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-purple-800">
                <i class="fas fa-vial ml-2"></i>اختبار الوظائف
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-3">اختبار عرض المباريات</h3>
                    <div class="space-y-2">
                        <button onclick="testUpcomingMatches()" class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                            اختبار المباريات القادمة
                        </button>
                        <button onclick="testLiveMatches()" class="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors">
                            اختبار المباريات الجارية
                        </button>
                        <button onclick="testFinishedMatches()" class="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                            اختبار المباريات المنتهية
                        </button>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-3">اختبار الإدارة</h3>
                    <div class="space-y-2">
                        <a href="admin/matches.php" class="block w-full bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors text-center">
                            فتح لوحة إدارة المباريات
                        </a>
                        <a href="matches.php" class="block w-full bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors text-center">
                            فتح صفحة عرض المباريات
                        </a>
                    </div>
                </div>
            </div>
            
            <div id="test-results" class="mt-4 bg-white p-4 rounded-lg border hidden">
                <h3 class="font-semibold text-gray-800 mb-2">نتائج الاختبار:</h3>
                <div id="test-output" class="text-sm text-gray-600"></div>
            </div>
        </div>
        <?php endif; ?>

        <!-- الميزات المتاحة -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-star ml-2"></i>الميزات المتاحة
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-table ml-2 text-blue-600"></i>جدول المباريات
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ عرض المباريات القادمة والسابقة</li>
                        <li>✅ تصميم متجاوب</li>
                        <li>✅ فلترة حسب البطولة والتاريخ</li>
                        <li>✅ تبويبات منظمة</li>
                    </ul>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-cogs ml-2 text-green-600"></i>إدارة المباريات
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ إضافة وتعديل المباريات</li>
                        <li>✅ رفع شعارات الفرق</li>
                        <li>✅ إدارة النتائج والحالات</li>
                        <li>✅ نماذج منبثقة سهلة</li>
                    </ul>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-mobile-alt ml-2 text-purple-600"></i>التجاوب والتصميم
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ متوافق مع جميع الأجهزة</li>
                        <li>✅ تصميم عصري وجذاب</li>
                        <li>✅ دعم اللغة العربية</li>
                        <li>✅ تكامل مع التصميم الحالي</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-link ml-2"></i>روابط سريعة
            </h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="matches.php" class="bg-blue-600 text-white px-4 py-3 rounded text-center hover:bg-blue-700 transition-colors">
                    <i class="fas fa-futbol block mb-1"></i>عرض المباريات
                </a>
                
                <a href="admin/matches.php" class="bg-green-600 text-white px-4 py-3 rounded text-center hover:bg-green-700 transition-colors">
                    <i class="fas fa-cogs block mb-1"></i>إدارة المباريات
                </a>
                
                <a href="test-all-features.php" class="bg-purple-600 text-white px-4 py-3 rounded text-center hover:bg-purple-700 transition-colors">
                    <i class="fas fa-vial block mb-1"></i>اختبار شامل
                </a>
                
                <a href="index.php" class="bg-orange-600 text-white px-4 py-3 rounded text-center hover:bg-orange-700 transition-colors">
                    <i class="fas fa-home block mb-1"></i>الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// اختبار المباريات القادمة
function testUpcomingMatches() {
    fetch('matches.php?tab=upcoming')
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const matches = doc.querySelectorAll('tbody tr');
            
            showTestResult(`تم العثور على ${matches.length} مباراة قادمة`, 'success');
        })
        .catch(error => {
            showTestResult('خطأ في اختبار المباريات القادمة: ' + error.message, 'error');
        });
}

// اختبار المباريات الجارية
function testLiveMatches() {
    fetch('matches.php?tab=live')
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const matches = doc.querySelectorAll('tbody tr');
            
            showTestResult(`تم العثور على ${matches.length} مباراة جارية`, 'success');
        })
        .catch(error => {
            showTestResult('خطأ في اختبار المباريات الجارية: ' + error.message, 'error');
        });
}

// اختبار المباريات المنتهية
function testFinishedMatches() {
    fetch('matches.php?tab=finished')
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const matches = doc.querySelectorAll('tbody tr');
            
            showTestResult(`تم العثور على ${matches.length} مباراة منتهية`, 'success');
        })
        .catch(error => {
            showTestResult('خطأ في اختبار المباريات المنتهية: ' + error.message, 'error');
        });
}

// عرض نتيجة الاختبار
function showTestResult(message, type) {
    const resultsDiv = document.getElementById('test-results');
    const outputDiv = document.getElementById('test-output');
    
    const colorClass = type === 'success' ? 'text-green-600' : 'text-red-600';
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-times-circle';
    
    outputDiv.innerHTML = `<i class="${icon} ml-2"></i><span class="${colorClass}">${message}</span>`;
    resultsDiv.classList.remove('hidden');
    
    // إخفاء النتيجة بعد 5 ثوان
    setTimeout(() => {
        resultsDiv.classList.add('hidden');
    }, 5000);
}
</script>

<?php include 'includes/footer.php'; ?>
