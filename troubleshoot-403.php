<?php
/**
 * 403 Forbidden Error Troubleshooting Tool
 * This file helps diagnose permission and access issues
 */

// Define root path
define('ROOT_PATH', __DIR__);

?>
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>403 Error Troubleshooting - News Website</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .status-ok { color: #10B981; }
        .status-warning { color: #F59E0B; }
        .status-error { color: #EF4444; }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">403 Forbidden Error Troubleshooting</h1>
            
            <!-- Server Information -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Server Information</h2>
                <div class="bg-gray-50 p-4 rounded">
                    <p><strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                    <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                    <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></p>
                    <p><strong>Script Path:</strong> <?php echo __FILE__; ?></p>
                    <p><strong>Current Directory:</strong> <?php echo getcwd(); ?></p>
                    <p><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Unknown'; ?></p>
                </div>
            </div>

            <!-- File Permissions Check -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">File Permissions Check</h2>
                <div class="space-y-2">
                    <?php
                    $files_to_check = [
                        'index.php',
                        'article.php',
                        'category.php',
                        'search.php',
                        'setup.php',
                        'admin/login.php',
                        'admin/dashboard.php',
                        'config/database.php',
                        'includes/functions.php',
                        '.htaccess'
                    ];

                    foreach ($files_to_check as $file) {
                        $full_path = ROOT_PATH . '/' . $file;
                        $exists = file_exists($full_path);
                        $readable = $exists ? is_readable($full_path) : false;
                        $perms = $exists ? substr(sprintf('%o', fileperms($full_path)), -4) : 'N/A';
                        
                        $status_class = $exists && $readable ? 'status-ok' : 'status-error';
                        $status_text = $exists && $readable ? 'OK' : ($exists ? 'Not Readable' : 'Missing');
                        
                        echo "<div class='flex justify-between items-center p-2 border rounded'>";
                        echo "<span>$file</span>";
                        echo "<span class='$status_class'>$status_text (Perms: $perms)</span>";
                        echo "</div>";
                    }
                    ?>
                </div>
            </div>

            <!-- Directory Permissions Check -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Directory Permissions Check</h2>
                <div class="space-y-2">
                    <?php
                    $dirs_to_check = [
                        '.',
                        'admin',
                        'admin/includes',
                        'config',
                        'includes',
                        'classes',
                        'logs',
                        'cron'
                    ];

                    foreach ($dirs_to_check as $dir) {
                        $full_path = ROOT_PATH . '/' . $dir;
                        $exists = is_dir($full_path);
                        $readable = $exists ? is_readable($full_path) : false;
                        $writable = $exists ? is_writable($full_path) : false;
                        $perms = $exists ? substr(sprintf('%o', fileperms($full_path)), -4) : 'N/A';
                        
                        $status_class = $exists && $readable ? 'status-ok' : 'status-error';
                        $status_text = $exists && $readable ? 'OK' : ($exists ? 'Not Accessible' : 'Missing');
                        
                        echo "<div class='flex justify-between items-center p-2 border rounded'>";
                        echo "<span>$dir/</span>";
                        echo "<span class='$status_class'>$status_text (Perms: $perms, Writable: " . ($writable ? 'Yes' : 'No') . ")</span>";
                        echo "</div>";
                    }
                    ?>
                </div>
            </div>

            <!-- .htaccess Analysis -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">.htaccess Analysis</h2>
                <?php
                $htaccess_path = ROOT_PATH . '/.htaccess';
                if (file_exists($htaccess_path)) {
                    $htaccess_content = file_get_contents($htaccess_path);
                    $htaccess_size = filesize($htaccess_path);
                    echo "<div class='bg-gray-50 p-4 rounded'>";
                    echo "<p><strong>Status:</strong> <span class='status-ok'>Found</span></p>";
                    echo "<p><strong>Size:</strong> $htaccess_size bytes</p>";
                    echo "<p><strong>Readable:</strong> " . (is_readable($htaccess_path) ? "<span class='status-ok'>Yes</span>" : "<span class='status-error'>No</span>") . "</p>";
                    
                    // Check for problematic rules
                    $problematic_patterns = [
                        'Deny from all',
                        'Require all denied',
                        'RedirectMatch 403',
                        'RewriteRule.*\[F\]'
                    ];
                    
                    echo "<p><strong>Potential Issues:</strong></p>";
                    echo "<ul class='list-disc list-inside ml-4'>";
                    foreach ($problematic_patterns as $pattern) {
                        if (preg_match("/$pattern/i", $htaccess_content)) {
                            echo "<li class='status-warning'>Found: $pattern</li>";
                        }
                    }
                    echo "</ul>";
                    echo "</div>";
                } else {
                    echo "<div class='bg-yellow-50 p-4 rounded'>";
                    echo "<p class='status-warning'>.htaccess file not found</p>";
                    echo "</div>";
                }
                ?>
            </div>

            <!-- PHP Configuration -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">PHP Configuration</h2>
                <div class="bg-gray-50 p-4 rounded">
                    <p><strong>Error Reporting:</strong> <?php echo error_reporting(); ?></p>
                    <p><strong>Display Errors:</strong> <?php echo ini_get('display_errors') ? 'On' : 'Off'; ?></p>
                    <p><strong>Log Errors:</strong> <?php echo ini_get('log_errors') ? 'On' : 'Off'; ?></p>
                    <p><strong>Error Log:</strong> <?php echo ini_get('error_log') ?: 'Default'; ?></p>
                    <p><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></p>
                    <p><strong>Max Execution Time:</strong> <?php echo ini_get('max_execution_time'); ?>s</p>
                </div>
            </div>

            <!-- Quick Fixes -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Quick Fixes</h2>
                <div class="bg-blue-50 p-4 rounded">
                    <h3 class="font-semibold mb-2">Try these solutions:</h3>
                    <ol class="list-decimal list-inside space-y-1">
                        <li>Temporarily rename .htaccess to .htaccess.backup</li>
                        <li>Use the simple .htaccess file (.htaccess.simple)</li>
                        <li>Check file permissions (should be 644 for files, 755 for directories)</li>
                        <li>Ensure the web server user has read access to all files</li>
                        <li>Check Apache error logs for specific error messages</li>
                    </ol>
                </div>
            </div>

            <!-- Test Links -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Test Links</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                    <?php
                    $test_links = [
                        'index.php' => 'Home Page',
                        'test-connection.php' => 'Connection Test',
                        'setup.php' => 'Database Setup',
                        'admin/login.php' => 'Admin Login',
                        'article.php' => 'Article Page',
                        'search.php' => 'Search Page'
                    ];

                    foreach ($test_links as $link => $title) {
                        echo "<a href='$link' class='block p-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200 text-center'>$title</a>";
                    }
                    ?>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex space-x-4">
                <button onclick="location.reload()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Refresh Check
                </button>
                <button onclick="useSimpleHtaccess()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Use Simple .htaccess
                </button>
                <button onclick="disableHtaccess()" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                    Disable .htaccess
                </button>
            </div>
        </div>
    </div>

    <script>
        function useSimpleHtaccess() {
            if (confirm('This will replace your current .htaccess with a simpler version. Continue?')) {
                fetch('fix-htaccess.php?action=simple')
                    .then(response => response.text())
                    .then(data => {
                        alert(data);
                        location.reload();
                    });
            }
        }

        function disableHtaccess() {
            if (confirm('This will rename .htaccess to .htaccess.disabled. Continue?')) {
                fetch('fix-htaccess.php?action=disable')
                    .then(response => response.text())
                    .then(data => {
                        alert(data);
                        location.reload();
                    });
            }
        }
    </script>
</body>
</html>
