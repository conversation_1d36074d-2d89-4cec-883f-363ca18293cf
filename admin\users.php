<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

checkAuth();

// التحقق من صلاحيات المدير
if ($_SESSION['user_role'] !== 'admin') {
    header("Location: dashboard.php");
    exit();
}

$page_title = 'إدارة المستخدمين';
$database = new Database();
$db = $database->connect();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$user_id = $_GET['id'] ?? null;
$edit_id = $_GET['edit'] ?? null;

$success_message = '';
$error_message = '';

// حذف مستخدم
if ($action === 'delete' && $user_id) {
    // منع حذف المستخدم الحالي
    if ($user_id == $_SESSION['user_id']) {
        $error_message = 'لا يمكنك حذف حسابك الخاص';
    } else {
        try {
            $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
            if ($stmt->execute([$user_id])) {
                $success_message = 'تم حذف المستخدم بنجاح';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء حذف المستخدم';
        }
    }
    $action = 'list';
}

// تبديل حالة التفعيل
if ($action === 'toggle_active' && $user_id) {
    // منع تعطيل المستخدم الحالي
    if ($user_id == $_SESSION['user_id']) {
        $error_message = 'لا يمكنك تعطيل حسابك الخاص';
    } else {
        try {
            $stmt = $db->prepare("UPDATE users SET is_active = NOT is_active WHERE id = ?");
            if ($stmt->execute([$user_id])) {
                $success_message = 'تم تحديث حالة المستخدم';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تحديث المستخدم';
        }
    }
    $action = 'list';
}

// إضافة أو تعديل مستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && in_array($action, ['add', 'edit'])) {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $full_name = trim($_POST['full_name']);
    $role = $_POST['role'];
    $password = $_POST['password'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if (!empty($username) && !empty($email) && !empty($full_name)) {
        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = 'البريد الإلكتروني غير صحيح';
        } else {
            try {
                if ($action === 'add') {
                    if (empty($password)) {
                        $error_message = 'كلمة المرور مطلوبة للمستخدم الجديد';
                    } else {
                        // التحقق من عدم وجود مستخدم بنفس اسم المستخدم أو البريد
                        $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
                        $stmt->execute([$username, $email]);
                        if ($stmt->fetchColumn() > 0) {
                            $error_message = 'يوجد مستخدم بهذا الاسم أو البريد الإلكتروني بالفعل';
                        } else {
                            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $db->prepare("
                                INSERT INTO users (username, email, password, full_name, role, is_active) 
                                VALUES (?, ?, ?, ?, ?, ?)
                            ");
                            if ($stmt->execute([$username, $email, $hashed_password, $full_name, $role, $is_active])) {
                                $success_message = 'تم إضافة المستخدم بنجاح';
                                $action = 'list';
                            }
                        }
                    }
                } elseif ($action === 'edit' && $edit_id) {
                    // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم أو البريد
                    $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE (username = ? OR email = ?) AND id != ?");
                    $stmt->execute([$username, $email, $edit_id]);
                    if ($stmt->fetchColumn() > 0) {
                        $error_message = 'يوجد مستخدم آخر بهذا الاسم أو البريد الإلكتروني بالفعل';
                    } else {
                        if (!empty($password)) {
                            // تحديث مع كلمة مرور جديدة
                            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $db->prepare("
                                UPDATE users 
                                SET username = ?, email = ?, password = ?, full_name = ?, role = ?, is_active = ? 
                                WHERE id = ?
                            ");
                            $result = $stmt->execute([$username, $email, $hashed_password, $full_name, $role, $is_active, $edit_id]);
                        } else {
                            // تحديث بدون تغيير كلمة المرور
                            $stmt = $db->prepare("
                                UPDATE users 
                                SET username = ?, email = ?, full_name = ?, role = ?, is_active = ? 
                                WHERE id = ?
                            ");
                            $result = $stmt->execute([$username, $email, $full_name, $role, $is_active, $edit_id]);
                        }
                        
                        if ($result) {
                            $success_message = 'تم تحديث المستخدم بنجاح';
                            $action = 'list';
                        }
                    }
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء حفظ المستخدم: ' . $e->getMessage();
            }
        }
    } else {
        $error_message = 'يرجى إدخال جميع البيانات المطلوبة';
    }
}

// الحصول على بيانات المستخدم للتعديل
$user_data = null;
if (($action === 'edit' && $edit_id) || ($action === 'add' && $edit_id)) {
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$edit_id]);
    $user_data = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($user_data) {
        $action = 'edit';
    }
}

// الحصول على المستخدمين
$stmt = $db->query("SELECT * FROM users ORDER BY created_at DESC");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">إدارة المستخدمين</h1>
        <?php if ($action === 'list'): ?>
        <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <i class="fas fa-plus ml-2"></i>إضافة مستخدم جديد
        </a>
        <?php else: ?>
        <a href="?" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            <i class="fas fa-arrow-right ml-2"></i>العودة للقائمة
        </a>
        <?php endif; ?>
    </div>

    <!-- Messages -->
    <?php if ($success_message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Users List -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold">قائمة المستخدمين</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البريد الإلكتروني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدور</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر دخول</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['full_name']); ?></div>
                                    <div class="text-sm text-gray-500">@<?php echo htmlspecialchars($user['username']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo htmlspecialchars($user['email']); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php if ($user['role'] === 'admin'): ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">مدير</span>
                            <?php else: ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">محرر</span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $user['last_login'] ? formatArabicDate($user['last_login']) : 'لم يسجل دخول بعد'; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php if ($user['is_active']): ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">نشط</span>
                            <?php else: ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">معطل</span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="?edit=<?php echo $user['id']; ?>" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit ml-1"></i>تعديل
                                </a>
                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                <a href="?action=toggle_active&id=<?php echo $user['id']; ?>" class="text-yellow-600 hover:text-yellow-900">
                                    <i class="fas fa-power-off ml-1"></i><?php echo $user['is_active'] ? 'تعطيل' : 'تفعيل'; ?>
                                </a>
                                <a href="?action=delete&id=<?php echo $user['id']; ?>" 
                                   onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')" 
                                   class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash ml-1"></i>حذف
                                </a>
                                <?php else: ?>
                                <span class="text-gray-400 cursor-not-allowed" title="لا يمكنك تعديل حسابك الخاص">
                                    <i class="fas fa-user-shield ml-1"></i>أنت
                                </span>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <?php else: ?>
    <!-- Add/Edit Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold mb-6">
            <?php echo $action === 'add' ? 'إضافة مستخدم جديد' : 'تعديل المستخدم'; ?>
        </h2>

        <form method="POST" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم *</label>
                    <input type="text" name="username" required 
                           value="<?php echo $user_data['username'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                    <input type="email" name="email" required 
                           value="<?php echo $user_data['email'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                    <input type="text" name="full_name" required 
                           value="<?php echo $user_data['full_name'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الدور *</label>
                    <select name="role" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="editor" <?php echo ($user_data['role'] ?? '') === 'editor' ? 'selected' : ''; ?>>محرر</option>
                        <option value="admin" <?php echo ($user_data['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>مدير</option>
                    </select>
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور <?php echo $action === 'edit' ? '(اتركها فارغة إذا لم ترد تغييرها)' : '*'; ?>
                    </label>
                    <input type="password" name="password" 
                           <?php echo $action === 'add' ? 'required' : ''; ?>
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="lg:col-span-2">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" 
                               <?php echo ($user_data['is_active'] ?? 1) ? 'checked' : ''; ?>
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">مستخدم نشط</span>
                    </label>
                </div>
            </div>

            <div class="flex justify-end space-x-3 space-x-reverse">
                <a href="?" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    إلغاء
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <?php echo $action === 'add' ? 'إضافة المستخدم' : 'تحديث المستخدم'; ?>
                </button>
            </div>
        </form>
    </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
