@echo off
echo ========================================
echo       موقع الأخبار - تشغيل الخادم
echo ========================================
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP أو إضافته إلى متغير البيئة PATH
    pause
    exit /b 1
)

echo تم العثور على PHP...
echo.

REM عرض معلومات الخادم
echo سيتم تشغيل الخادم على:
echo http://localhost:8000
echo.
echo للوصول إلى لوحة التحكم:
echo http://localhost:8000/admin/login.php
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo ========================================
echo.

REM تشغيل الخادم
php -S localhost:8000

pause
