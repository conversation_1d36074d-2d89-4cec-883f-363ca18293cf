<?php
/**
 * اختبار وظائف الجلسة بعد الإصلاح
 */

// تهيئة الجلسة والإعدادات الأساسية
require_once 'includes/session_init.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار وظائف الجلسة</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo "table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo "th, td { padding: 12px; text-align: right; border: 1px solid #ddd; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🧪 اختبار وظائف الجلسة بعد الإصلاح</h1>";

// 1. اختبار حالة الجلسة
echo "<div class='section success'>";
echo "<h2>1. حالة الجلسة</h2>";

$session_status = session_status();
$status_text = '';
$status_class = '';

switch ($session_status) {
    case PHP_SESSION_DISABLED:
        $status_text = 'معطلة';
        $status_class = 'error';
        break;
    case PHP_SESSION_NONE:
        $status_text = 'لم تبدأ';
        $status_class = 'warning';
        break;
    case PHP_SESSION_ACTIVE:
        $status_text = 'نشطة';
        $status_class = 'success';
        break;
}

echo "<table>";
echo "<tr><th>المعلومة</th><th>القيمة</th><th>الحالة</th></tr>";
echo "<tr class='$status_class'>";
echo "<td>حالة الجلسة</td>";
echo "<td>$status_text ($session_status)</td>";
echo "<td>" . ($session_status == PHP_SESSION_ACTIVE ? '✅ نشطة' : '❌ غير نشطة') . "</td>";
echo "</tr>";

echo "<tr>";
echo "<td>معرف الجلسة</td>";
echo "<td>" . (session_id() ?: 'غير محدد') . "</td>";
echo "<td>" . (session_id() ? '✅ موجود' : '❌ غير موجود') . "</td>";
echo "</tr>";

echo "<tr>";
echo "<td>اسم الجلسة</td>";
echo "<td>" . session_name() . "</td>";
echo "<td>✅ محدد</td>";
echo "</tr>";

echo "<tr>";
echo "<td>مسار حفظ الجلسة</td>";
echo "<td>" . session_save_path() . "</td>";
echo "<td>" . (is_writable(session_save_path()) ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة') . "</td>";
echo "</tr>";
echo "</table>";
echo "</div>";

// 2. اختبار إعدادات الجلسة
echo "<div class='section info'>";
echo "<h2>2. إعدادات الجلسة</h2>";

$session_settings = [
    'session.cookie_lifetime' => ini_get('session.cookie_lifetime'),
    'session.gc_maxlifetime' => ini_get('session.gc_maxlifetime'),
    'session.cookie_httponly' => ini_get('session.cookie_httponly'),
    'session.cookie_secure' => ini_get('session.cookie_secure'),
    'session.use_strict_mode' => ini_get('session.use_strict_mode'),
    'session.cookie_samesite' => ini_get('session.cookie_samesite'),
    'session.use_cookies' => ini_get('session.use_cookies'),
    'session.auto_start' => ini_get('session.auto_start')
];

echo "<table>";
echo "<tr><th>الإعداد</th><th>القيمة</th><th>الحالة</th></tr>";

foreach ($session_settings as $setting => $value) {
    $status = '✅ محدد';
    $class = 'success';
    
    if ($setting === 'session.cookie_httponly' && $value != '1') {
        $status = '⚠️ يُنصح بتفعيله';
        $class = 'warning';
    }
    
    if ($setting === 'session.use_strict_mode' && $value != '1') {
        $status = '⚠️ يُنصح بتفعيله';
        $class = 'warning';
    }
    
    echo "<tr class='$class'>";
    echo "<td>$setting</td>";
    echo "<td>$value</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 3. اختبار متغيرات الجلسة
echo "<div class='section info'>";
echo "<h2>3. متغيرات الجلسة</h2>";

// إضافة بعض متغيرات الاختبار
$_SESSION['test_string'] = 'مرحبا بك';
$_SESSION['test_number'] = 12345;
$_SESSION['test_array'] = ['item1', 'item2', 'item3'];
$_SESSION['test_timestamp'] = time();

echo "<table>";
echo "<tr><th>المتغير</th><th>القيمة</th><th>النوع</th></tr>";

foreach ($_SESSION as $key => $value) {
    $display_value = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    $type = gettype($value);
    
    echo "<tr>";
    echo "<td>$key</td>";
    echo "<td>" . htmlspecialchars($display_value) . "</td>";
    echo "<td>$type</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 4. اختبار دوال الجلسة المخصصة
echo "<div class='section success'>";
echo "<h2>4. اختبار الدوال المخصصة</h2>";

echo "<table>";
echo "<tr><th>الدالة</th><th>النتيجة</th><th>الحالة</th></tr>";

// اختبار isLoggedIn()
$is_logged_in = function_exists('isLoggedIn') ? isLoggedIn() : 'الدالة غير موجودة';
echo "<tr>";
echo "<td>isLoggedIn()</td>";
echo "<td>" . ($is_logged_in === true ? 'true' : ($is_logged_in === false ? 'false' : $is_logged_in)) . "</td>";
echo "<td>" . (function_exists('isLoggedIn') ? '✅ تعمل' : '❌ غير موجودة') . "</td>";
echo "</tr>";

// اختبار getUserId()
$user_id = function_exists('getUserId') ? getUserId() : 'الدالة غير موجودة';
echo "<tr>";
echo "<td>getUserId()</td>";
echo "<td>" . ($user_id ?: 'null') . "</td>";
echo "<td>" . (function_exists('getUserId') ? '✅ تعمل' : '❌ غير موجودة') . "</td>";
echo "</tr>";

// اختبار generateCSRFToken()
$csrf_token = function_exists('generateCSRFToken') ? generateCSRFToken() : 'الدالة غير موجودة';
echo "<tr>";
echo "<td>generateCSRFToken()</td>";
echo "<td>" . (is_string($csrf_token) ? substr($csrf_token, 0, 20) . '...' : $csrf_token) . "</td>";
echo "<td>" . (function_exists('generateCSRFToken') ? '✅ تعمل' : '❌ غير موجودة') . "</td>";
echo "</tr>";

// اختبار setFlashMessage()
if (function_exists('setFlashMessage')) {
    setFlashMessage('success', 'رسالة اختبار');
    echo "<tr>";
    echo "<td>setFlashMessage()</td>";
    echo "<td>تم إضافة رسالة اختبار</td>";
    echo "<td>✅ تعمل</td>";
    echo "</tr>";
} else {
    echo "<tr>";
    echo "<td>setFlashMessage()</td>";
    echo "<td>الدالة غير موجودة</td>";
    echo "<td>❌ غير موجودة</td>";
    echo "</tr>";
}

// اختبار getFlashMessages()
if (function_exists('getFlashMessages')) {
    $flash_messages = getFlashMessages(false); // عدم حذف الرسائل
    echo "<tr>";
    echo "<td>getFlashMessages()</td>";
    echo "<td>" . count($flash_messages) . " رسالة</td>";
    echo "<td>✅ تعمل</td>";
    echo "</tr>";
} else {
    echo "<tr>";
    echo "<td>getFlashMessages()</td>";
    echo "<td>الدالة غير موجودة</td>";
    echo "<td>❌ غير موجودة</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// 5. اختبار الأمان
echo "<div class='section warning'>";
echo "<h2>5. اختبار الأمان</h2>";

echo "<table>";
echo "<tr><th>اختبار الأمان</th><th>النتيجة</th><th>الحالة</th></tr>";

// فحص CSRF Token
$csrf_exists = isset($_SESSION['csrf_token']);
echo "<tr>";
echo "<td>CSRF Token</td>";
echo "<td>" . ($csrf_exists ? 'موجود' : 'غير موجود') . "</td>";
echo "<td>" . ($csrf_exists ? '✅ محمي' : '❌ غير محمي') . "</td>";
echo "</tr>";

// فحص انتهاء صلاحية الجلسة
$expires_set = isset($_SESSION['expires']);
echo "<tr>";
echo "<td>انتهاء صلاحية الجلسة</td>";
echo "<td>" . ($expires_set ? date('Y-m-d H:i:s', $_SESSION['expires']) : 'غير محدد') . "</td>";
echo "<td>" . ($expires_set ? '✅ محدد' : '❌ غير محدد') . "</td>";
echo "</tr>";

// فحص IP Address
$ip_tracked = isset($_SESSION['ip_address']);
echo "<tr>";
echo "<td>تتبع IP Address</td>";
echo "<td>" . ($ip_tracked ? $_SESSION['ip_address'] : 'غير محدد') . "</td>";
echo "<td>" . ($ip_tracked ? '✅ محدد' : '❌ غير محدد') . "</td>";
echo "</tr>";

// فحص تجديد معرف الجلسة
$regeneration_tracked = isset($_SESSION['last_regeneration']);
echo "<tr>";
echo "<td>آخر تجديد للمعرف</td>";
echo "<td>" . ($regeneration_tracked ? date('Y-m-d H:i:s', $_SESSION['last_regeneration']) : 'غير محدد') . "</td>";
echo "<td>" . ($regeneration_tracked ? '✅ يعمل' : '❌ لا يعمل') . "</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

// 6. اختبار الأداء
echo "<div class='section info'>";
echo "<h2>6. اختبار الأداء</h2>";

$start_time = microtime(true);

// محاكاة عمليات الجلسة
for ($i = 0; $i < 100; $i++) {
    $_SESSION['test_performance_' . $i] = 'value_' . $i;
}

// حذف المتغيرات
for ($i = 0; $i < 100; $i++) {
    unset($_SESSION['test_performance_' . $i]);
}

$end_time = microtime(true);
$execution_time = round(($end_time - $start_time) * 1000, 2);

echo "<table>";
echo "<tr><th>المقياس</th><th>القيمة</th><th>الحالة</th></tr>";
echo "<tr>";
echo "<td>وقت تنفيذ 200 عملية</td>";
echo "<td>{$execution_time} مللي ثانية</td>";
echo "<td>" . ($execution_time < 100 ? '✅ سريع' : '⚠️ بطيء') . "</td>";
echo "</tr>";

echo "<tr>";
echo "<td>استهلاك الذاكرة</td>";
echo "<td>" . round(memory_get_usage() / 1024 / 1024, 2) . " MB</td>";
echo "<td>✅ طبيعي</td>";
echo "</tr>";

echo "<tr>";
echo "<td>حجم بيانات الجلسة</td>";
echo "<td>" . round(strlen(serialize($_SESSION)) / 1024, 2) . " KB</td>";
echo "<td>✅ طبيعي</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

// 7. نتائج الاختبار
echo "<div class='section success'>";
echo "<h2>7. نتائج الاختبار</h2>";

$total_tests = 0;
$passed_tests = 0;

// حساب النتائج
if ($session_status == PHP_SESSION_ACTIVE) $passed_tests++;
$total_tests++;

if (session_id()) $passed_tests++;
$total_tests++;

if (function_exists('isLoggedIn')) $passed_tests++;
$total_tests++;

if (function_exists('generateCSRFToken')) $passed_tests++;
$total_tests++;

if (isset($_SESSION['csrf_token'])) $passed_tests++;
$total_tests++;

if (isset($_SESSION['expires'])) $passed_tests++;
$total_tests++;

$success_rate = round(($passed_tests / $total_tests) * 100, 1);

echo "<div style='text-align: center; padding: 30px;'>";
echo "<h3 style='color: #2c3e50; margin-bottom: 20px;'>نتيجة الاختبار الشامل</h3>";

if ($success_rate >= 90) {
    echo "<div style='font-size: 3rem; color: #27ae60; margin-bottom: 20px;'>🎉</div>";
    echo "<div style='font-size: 2rem; color: #27ae60; font-weight: bold; margin-bottom: 10px;'>ممتاز!</div>";
    echo "<div style='color: #27ae60;'>جميع وظائف الجلسة تعمل بشكل مثالي</div>";
} elseif ($success_rate >= 70) {
    echo "<div style='font-size: 3rem; color: #f39c12; margin-bottom: 20px;'>⚠️</div>";
    echo "<div style='font-size: 2rem; color: #f39c12; font-weight: bold; margin-bottom: 10px;'>جيد</div>";
    echo "<div style='color: #f39c12;'>معظم وظائف الجلسة تعمل بشكل صحيح</div>";
} else {
    echo "<div style='font-size: 3rem; color: #e74c3c; margin-bottom: 20px;'>❌</div>";
    echo "<div style='font-size: 2rem; color: #e74c3c; font-weight: bold; margin-bottom: 10px;'>يحتاج تحسين</div>";
    echo "<div style='color: #e74c3c;'>بعض وظائف الجلسة لا تعمل بشكل صحيح</div>";
}

echo "<div style='margin-top: 20px;'>";
echo "<span style='font-size: 1.5rem; font-weight: bold;'>$success_rate%</span>";
echo "<span style='margin-left: 10px;'>($passed_tests من $total_tests اختبار نجح)</span>";
echo "</div>";
echo "</div>";
echo "</div>";

// 8. روابط الاختبار
echo "<div class='section info'>";
echo "<h2>8. اختبار الصفحات</h2>";
echo "<div style='text-align: center;'>";
echo "<a href='index.php' target='_blank' class='btn btn-primary'>اختبار الصفحة الرئيسية</a>";
echo "<a href='matches.php' target='_blank' class='btn btn-success'>اختبار صفحة المباريات</a>";
echo "<a href='session-diagnosis.php' target='_blank' class='btn btn-warning'>تشخيص الجلسة</a>";
echo "<a href='header-fixes-final-report.php' target='_blank' class='btn btn-danger'>التقرير النهائي</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
