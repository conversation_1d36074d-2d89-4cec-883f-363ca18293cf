<?php
/**
 * اختبار السلايدر فقط
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/matches_functions.php';

// الحصول على المباريات القادمة
$upcoming_matches = getUpcomingMatches(5);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سلايدر المباريات</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
        }
        
        /* Football Matches Slider Styles */
        .matches-slider {
            position: relative;
            overflow: hidden;
            border-radius: 0.75rem;
        }

        .matches-slider-container {
            display: flex;
            transition: transform 0.5s ease-in-out;
            will-change: transform;
        }

        .matches-slide {
            min-width: 100%;
            display: flex;
            gap: 1rem;
            padding: 0 0.5rem;
        }

        /* Responsive slide layouts */
        @media (min-width: 1024px) {
            .matches-slide .match-card {
                flex: 0 0 calc(33.333% - 0.667rem);
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            .matches-slide .match-card {
                flex: 0 0 calc(50% - 0.5rem);
            }
        }

        @media (max-width: 767px) {
            .matches-slide .match-card {
                flex: 0 0 100%;
            }
        }

        /* Navigation buttons */
        .slider-nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .slider-nav-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .slider-nav-btn.prev {
            left: 15px;
        }

        .slider-nav-btn.next {
            right: 15px;
        }

        .slider-nav-btn i {
            color: #374151;
            font-size: 1.2rem;
        }

        /* Dots indicator */
        .slider-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }

        .slider-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(156, 163, 175, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .slider-dot:hover {
            background: rgba(156, 163, 175, 0.8);
            transform: scale(1.2);
        }

        .slider-dot.active {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            transform: scale(1.3);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
        }

        .match-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .match-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .featured-match {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">

<div class="container mx-auto">
    <h1 class="text-3xl font-bold text-center mb-8">اختبار سلايدر المباريات</h1>
    
    <div class="bg-white rounded-lg shadow-lg p-4 mb-8">
        <h2 class="text-xl font-semibold mb-4">معلومات التشخيص:</h2>
        <p><strong>عدد المباريات القادمة:</strong> <?php echo count($upcoming_matches); ?></p>
        <p><strong>حالة السلايدر:</strong> <?php echo !empty($upcoming_matches) ? '✅ يجب أن يظهر' : '❌ لن يظهر'; ?></p>
    </div>

    <!-- Football Matches Widget -->
    <?php if (!empty($upcoming_matches)): ?>
    <section class="bg-gradient-to-r from-green-600 to-blue-600 py-8 mb-8">
        <div class="container mx-auto px-4">
            <div class="bg-white rounded-xl shadow-2xl overflow-hidden relative z-10">
                <!-- Header -->
                <div class="bg-gradient-to-r from-green-500 to-blue-500 text-white p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="bg-white bg-opacity-20 rounded-full p-3 ml-4">
                                <i class="fas fa-futbol text-2xl"></i>
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold">مواعيد المباريات</h2>
                                <p class="text-green-100 text-sm">المباريات القادمة</p>
                            </div>
                        </div>
                        <div class="hidden md:flex items-center space-x-4 space-x-reverse">
                            <div class="text-center">
                                <div class="text-2xl font-bold"><?php echo count($upcoming_matches); ?></div>
                                <div class="text-xs text-green-100">مباراة قادمة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Matches Slider -->
                <div class="p-6">
                    <div class="matches-slider" id="matchesSlider">
                        <!-- Navigation Buttons -->
                        <button class="slider-nav-btn prev" id="prevBtn" aria-label="المباراة السابقة">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="slider-nav-btn next" id="nextBtn" aria-label="المباراة التالية">
                            <i class="fas fa-chevron-left"></i>
                        </button>

                        <!-- Slider Container -->
                        <div class="matches-slider-container" id="sliderContainer">
                            <?php
                            $matches_per_slide = [
                                'desktop' => 3,
                                'tablet' => 2,
                                'mobile' => 1
                            ];

                            $all_matches = array_slice($upcoming_matches, 0, 9); // أقصى 9 مباريات
                            $total_slides = ceil(count($all_matches) / 3); // حساب عدد الشرائح للديسكتوب

                            for ($slide = 0; $slide < $total_slides; $slide++):
                                $slide_matches = array_slice($all_matches, $slide * 3, 3);
                            ?>
                            <div class="matches-slide">
                                <?php foreach ($slide_matches as $match): ?>
                                <div class="match-card <?php echo $match['is_featured'] ? 'featured-match' : 'bg-gray-50'; ?> rounded-lg p-4 border border-gray-200">
                                    <!-- Match Date & Competition -->
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="text-xs text-gray-600 bg-blue-100 px-2 py-1 rounded-full">
                                            <i class="fas fa-calendar ml-1"></i>
                                            <?php echo formatMatchDate($match['match_date'], 'date_only'); ?>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            <?php echo mb_substr($match['competition'], 0, 15) . (mb_strlen($match['competition']) > 15 ? '...' : ''); ?>
                                        </div>
                                    </div>

                                    <!-- Teams -->
                                    <div class="flex items-center justify-between mb-3">
                                        <!-- Home Team -->
                                        <div class="flex items-center flex-1">
                                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center ml-2">
                                                <i class="fas fa-shield-alt text-gray-500 text-xs"></i>
                                            </div>
                                            <span class="text-sm font-semibold text-gray-800 truncate">
                                                <?php echo mb_substr($match['home_team'], 0, 12) . (mb_strlen($match['home_team']) > 12 ? '...' : ''); ?>
                                            </span>
                                        </div>

                                        <!-- VS -->
                                        <div class="mx-3">
                                            <span class="text-xs text-gray-400 font-bold bg-gray-200 px-2 py-1 rounded">VS</span>
                                        </div>

                                        <!-- Away Team -->
                                        <div class="flex items-center flex-1 justify-end">
                                            <span class="text-sm font-semibold text-gray-800 truncate">
                                                <?php echo mb_substr($match['away_team'], 0, 12) . (mb_strlen($match['away_team']) > 12 ? '...' : ''); ?>
                                            </span>
                                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-2">
                                                <i class="fas fa-shield-alt text-gray-500 text-xs"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Match Time & Status -->
                                    <div class="flex items-center justify-between">
                                        <div class="text-xs text-gray-600">
                                            <i class="fas fa-clock ml-1 text-blue-500"></i>
                                            <?php echo formatMatchDate($match['match_date'], 'time_only'); ?>
                                        </div>
                                        <?php if ($match['venue']): ?>
                                        <div class="text-xs text-gray-500 truncate max-w-20">
                                            <i class="fas fa-map-marker-alt ml-1"></i>
                                            <?php echo mb_substr($match['venue'], 0, 10) . (mb_strlen($match['venue']) > 10 ? '...' : ''); ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Featured Match Indicator -->
                                    <?php if ($match['is_featured']): ?>
                                    <div class="mt-2 text-center">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-star ml-1"></i>مباراة مميزة
                                        </span>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endfor; ?>
                        </div>

                        <!-- Slider Dots -->
                        <div class="slider-dots" id="sliderDots">
                            <?php for ($i = 0; $i < $total_slides; $i++): ?>
                            <button class="slider-dot <?php echo $i === 0 ? 'active' : ''; ?>"
                                    data-slide="<?php echo $i; ?>"
                                    aria-label="الشريحة <?php echo $i + 1; ?>"></button>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <!-- View All Matches Button -->
                    <div class="mt-6 text-center">
                        <a href="matches.php"
                           class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white font-semibold rounded-lg hover:from-green-600 hover:to-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                            <i class="fas fa-futbol ml-2"></i>
                            عرض جميع المباريات
                            <i class="fas fa-arrow-left mr-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php else: ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong>تحذير:</strong> لا توجد مباريات قادمة لعرضها في السلايدر.
    </div>
    <?php endif; ?>

</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة - بدء تشغيل السلايدر');
    
    const slider = document.getElementById('matchesSlider');
    const sliderContainer = document.getElementById('sliderContainer');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const dots = document.querySelectorAll('.slider-dot');

    console.log('عناصر السلايدر:', {
        slider: !!slider,
        sliderContainer: !!sliderContainer,
        prevBtn: !!prevBtn,
        nextBtn: !!nextBtn,
        dotsCount: dots.length
    });

    if (!slider || !sliderContainer || !prevBtn || !nextBtn) {
        console.error('عناصر السلايدر غير موجودة');
        return;
    }

    let currentSlide = 0;
    const totalSlides = <?php echo isset($total_slides) ? $total_slides : 1; ?>;
    
    console.log('إعدادات السلايدر:', {
        currentSlide: currentSlide,
        totalSlides: totalSlides
    });

    // Update slider position
    function updateSlider() {
        const translateX = -currentSlide * 100;
        sliderContainer.style.transform = `translateX(${translateX}%)`;
        console.log('تحديث موضع السلايدر:', translateX + '%');

        // Update dots
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlide);
        });
    }

    // Navigation functions
    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        console.log('الانتقال للشريحة التالية:', currentSlide);
        updateSlider();
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        console.log('الانتقال للشريحة السابقة:', currentSlide);
        updateSlider();
    }

    function goToSlide(slideIndex) {
        currentSlide = slideIndex;
        console.log('الانتقال للشريحة:', currentSlide);
        updateSlider();
    }

    // Add event listeners
    prevBtn.addEventListener('click', prevSlide);
    nextBtn.addEventListener('click', nextSlide);

    // Dots navigation
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => goToSlide(index));
    });

    // Initialize
    updateSlider();
    
    console.log('تم تشغيل السلايدر بنجاح');
});
</script>

</body>
</html>
