<?php
/**
 * إضافة بيانات تجريبية للمباريات
 * Add sample matches data for testing
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->connect();
    
    // إنشاء جدول المباريات إذا لم يكن موجوداً
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS matches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        home_team VARCHAR(100) NOT NULL,
        away_team VARCHAR(100) NOT NULL,
        home_team_logo VARCHAR(255) DEFAULT NULL,
        away_team_logo VARCHAR(255) DEFAULT NULL,
        match_date DATETIME NOT NULL,
        competition VARCHAR(100) NOT NULL,
        venue VARCHAR(100) DEFAULT NULL,
        status ENUM('scheduled', 'live', 'finished', 'postponed', 'cancelled') DEFAULT 'scheduled',
        home_score INT DEFAULT NULL,
        away_score INT DEFAULT NULL,
        match_time VARCHAR(20) DEFAULT NULL,
        description TEXT DEFAULT NULL,
        is_featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($createTableSQL);
    echo "✅ تم إنشاء جدول المباريات بنجاح\n";
    
    // حذف البيانات الموجودة
    $db->exec("DELETE FROM matches");
    echo "🗑️ تم حذف البيانات القديمة\n";
    
    // إضافة بيانات تجريبية
    $sampleMatches = [
        [
            'home_team' => 'الأهلي',
            'away_team' => 'الزمالك',
            'match_date' => date('Y-m-d H:i:s', strtotime('+2 days 20:00')),
            'competition' => 'الدوري المصري الممتاز',
            'venue' => 'استاد القاهرة',
            'status' => 'scheduled',
            'is_featured' => 1
        ],
        [
            'home_team' => 'ريال مدريد',
            'away_team' => 'برشلونة',
            'match_date' => date('Y-m-d H:i:s', strtotime('+3 days 22:00')),
            'competition' => 'الليغا الإسبانية',
            'venue' => 'سانتياغو برنابيو',
            'status' => 'scheduled',
            'is_featured' => 1
        ],
        [
            'home_team' => 'مانشستر يونايتد',
            'away_team' => 'ليفربول',
            'match_date' => date('Y-m-d H:i:s', strtotime('+4 days 17:30')),
            'competition' => 'الدوري الإنجليزي الممتاز',
            'venue' => 'أولد ترافورد',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'باريس سان جيرمان',
            'away_team' => 'مارسيليا',
            'match_date' => date('Y-m-d H:i:s', strtotime('+5 days 21:00')),
            'competition' => 'الدوري الفرنسي',
            'venue' => 'حديقة الأمراء',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'بايرن ميونخ',
            'away_team' => 'بوروسيا دورتموند',
            'match_date' => date('Y-m-d H:i:s', strtotime('+6 days 18:30')),
            'competition' => 'البوندسليغا الألمانية',
            'venue' => 'أليانز أرينا',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'يوفنتوس',
            'away_team' => 'إنتر ميلان',
            'match_date' => date('Y-m-d H:i:s', strtotime('+7 days 20:45')),
            'competition' => 'الدوري الإيطالي',
            'venue' => 'أليانز ستاديوم',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'الهلال',
            'away_team' => 'النصر',
            'match_date' => date('Y-m-d H:i:s', strtotime('+8 days 19:00')),
            'competition' => 'دوري روشن السعودي',
            'venue' => 'استاد الملك فهد',
            'status' => 'scheduled',
            'is_featured' => 1
        ],
        [
            'home_team' => 'الاتحاد',
            'away_team' => 'الشباب',
            'match_date' => date('Y-m-d H:i:s', strtotime('+9 days 17:00')),
            'competition' => 'دوري روشن السعودي',
            'venue' => 'استاد الملك عبدالله',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'الوداد',
            'away_team' => 'الرجاء',
            'match_date' => date('Y-m-d H:i:s', strtotime('+10 days 20:00')),
            'competition' => 'الدوري المغربي',
            'venue' => 'استاد محمد الخامس',
            'status' => 'scheduled',
            'is_featured' => 0
        ],
        [
            'home_team' => 'الترجي',
            'away_team' => 'النجم الساحلي',
            'match_date' => date('Y-m-d H:i:s', strtotime('+11 days 19:30')),
            'competition' => 'الدوري التونسي',
            'venue' => 'استاد رادس',
            'status' => 'scheduled',
            'is_featured' => 0
        ]
    ];
    
    $insertSQL = "INSERT INTO matches (home_team, away_team, match_date, competition, venue, status, is_featured) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $db->prepare($insertSQL);
    
    foreach ($sampleMatches as $match) {
        $stmt->execute([
            $match['home_team'],
            $match['away_team'],
            $match['match_date'],
            $match['competition'],
            $match['venue'],
            $match['status'],
            $match['is_featured']
        ]);
    }
    
    echo "✅ تم إضافة " . count($sampleMatches) . " مباراة تجريبية بنجاح\n";
    
    // عرض المباريات المضافة
    $stmt = $db->query("SELECT * FROM matches ORDER BY match_date ASC");
    $matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📋 المباريات المضافة:\n";
    echo "=" . str_repeat("=", 80) . "\n";
    
    foreach ($matches as $match) {
        $featured = $match['is_featured'] ? ' ⭐' : '';
        echo sprintf(
            "🏆 %s vs %s%s\n   📅 %s | 🏟️ %s | 🏆 %s\n\n",
            $match['home_team'],
            $match['away_team'],
            $featured,
            date('Y-m-d H:i', strtotime($match['match_date'])),
            $match['venue'],
            $match['competition']
        );
    }
    
    echo "🎉 تم إعداد البيانات التجريبية بنجاح! يمكنك الآن زيارة الصفحة الرئيسية لرؤية جدول المباريات.\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
