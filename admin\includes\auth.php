<?php
// التحقق من تسجيل الدخول
function checkAuth() {
    if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
        header("Location: login.php");
        exit();
    }
}

// التحقق من صلاحيات الإدارة
function checkAdminAuth() {
    checkAuth();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        header("Location: dashboard.php");
        exit();
    }
}

// تسجيل الخروج
function logout() {
    session_destroy();
    header("Location: login.php");
    exit();
}

// الحصول على معلومات المستخدم الحالي
function getCurrentUser() {
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'username' => $_SESSION['username'] ?? '',
        'full_name' => $_SESSION['full_name'] ?? '',
        'role' => $_SESSION['user_role'] ?? 'editor'
    ];
}

// التحقق من الصلاحيات
function hasPermission($permission) {
    $user = getCurrentUser();
    if (!$user) return false;
    
    // المدير له جميع الصلاحيات
    if ($user['role'] === 'admin') {
        return true;
    }
    
    // صلاحيات المحرر
    $editor_permissions = [
        'view_articles',
        'create_articles',
        'edit_articles',
        'view_categories',
        'view_rss_sources'
    ];
    
    return in_array($permission, $editor_permissions);
}
?>
