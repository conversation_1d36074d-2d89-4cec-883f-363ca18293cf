/*
 * Design Consistency Fixes - إصلاحات تناسق التصميم
 * ملف CSS لإصلاح مشاكل التناسق وتوحيد التصميم
 */

/* ==========================================================================
   Header Consistency Fixes - إصلاحات تناسق الهيدر
   ========================================================================== */

/* Header Top Bar */
.header-top {
    background: var(--neutral-800);
    color: var(--neutral-200);
    padding: var(--space-2) 0;
    font-size: var(--text-sm);
    border-bottom: 1px solid var(--neutral-700);
}

.header-social-links {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.social-link {
    color: var(--neutral-300);
    transition: color var(--transition-fast);
    font-size: var(--text-base);
}

.social-link.facebook:hover {
    color: #1877f2;
}

.social-link.twitter:hover {
    color: #1da1f2;
}

.social-link.youtube:hover {
    color: #ff0000;
}

.social-link.instagram:hover {
    color: #e4405f;
}

/* Header Main */
.header-main {
    background: white;
    padding: var(--space-4) 0;
    box-shadow: var(--shadow-sm);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-6);
}

.header-brand {
    display: flex;
    align-items: center;
}

.site-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    gap: var(--space-3);
    transition: transform var(--transition-fast);
}

.site-logo:hover {
    transform: translateY(-1px);
}

.logo-icon {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: white;
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    font-size: var(--text-2xl);
    box-shadow: var(--shadow-md);
}

.site-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--neutral-800);
    margin: 0;
    line-height: var(--leading-tight);
}

.site-description {
    font-size: var(--text-sm);
    color: var(--neutral-600);
    margin: 0;
    line-height: var(--leading-normal);
}

/* ==========================================================================
   Navigation Consistency Fixes - إصلاحات تناسق التنقل
   ========================================================================== */

.main-navigation {
    background: var(--primary-600);
    border-top: 1px solid var(--primary-500);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    padding: var(--space-3) 0;
    margin: 0;
    list-style: none;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-1px);
}

/* ==========================================================================
   Content Consistency Fixes - إصلاحات تناسق المحتوى
   ========================================================================== */

/* Main Content Container */
.main-content {
    padding: var(--space-8) 0;
    min-height: calc(100vh - 200px);
}

/* News Cards Consistency */
.news-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-base);
    overflow: hidden;
    transition: all var(--transition-base);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.news-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
}

.news-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition-base);
}

.news-card:hover .news-card-image {
    transform: scale(1.05);
}

.news-card-content {
    padding: var(--space-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-3);
}

.news-card-category {
    background: var(--primary-100);
    color: var(--primary-800);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.news-card-category:hover {
    background: var(--primary-200);
    color: var(--primary-900);
}

.news-card-date {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.news-card-title {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-3);
    color: var(--neutral-900);
}

.news-card-title a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.news-card-title a:hover {
    color: var(--primary-600);
}

.news-card-excerpt {
    color: var(--neutral-600);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--space-3);
    border-top: 1px solid var(--neutral-200);
    margin-top: auto;
}

.news-card-views {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* ==========================================================================
   Button Consistency Fixes - إصلاحات تناسق الأزرار
   ========================================================================== */

/* Override Tailwind button styles */
.btn,
button,
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    line-height: 1;
    gap: var(--space-2);
}

.btn-primary,
.bg-blue-600 {
    background-color: var(--primary-600) !important;
    color: white !important;
}

.btn-primary:hover,
.bg-blue-600:hover {
    background-color: var(--primary-700) !important;
    color: white !important;
    transform: translateY(-1px);
}

.btn-secondary,
.bg-gray-200 {
    background-color: var(--secondary-200) !important;
    color: var(--secondary-800) !important;
}

.btn-secondary:hover,
.bg-gray-200:hover {
    background-color: var(--secondary-300) !important;
    color: var(--secondary-900) !important;
}

.btn-success,
.bg-green-600 {
    background-color: var(--success-600) !important;
    color: white !important;
}

.btn-success:hover,
.bg-green-600:hover {
    background-color: var(--success-700) !important;
    color: white !important;
}

/* ==========================================================================
   Form Consistency Fixes - إصلاحات تناسق النماذج
   ========================================================================== */

input[type="text"],
input[type="email"],
input[type="search"],
textarea,
select {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-family: var(--font-family-primary);
    transition: all var(--transition-fast);
    background: white;
}

input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ==========================================================================
   Sidebar Consistency Fixes - إصلاحات تناسق الشريط الجانبي
   ========================================================================== */

.sidebar-widget {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-base);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
}

.sidebar-widget-title {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid var(--primary-100);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.sidebar-widget-title i {
    color: var(--primary-600);
}

/* ==========================================================================
   Responsive Consistency Fixes - إصلاحات التناسق المتجاوب
   ========================================================================== */

@media (max-width: 767px) {
    .header-container {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .header-brand {
        order: 1;
    }
    
    .header-nav {
        order: 2;
        width: 100%;
        justify-content: center;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--space-3);
    }
    
    .news-card-content {
        padding: var(--space-4);
    }
    
    .sidebar-widget {
        padding: var(--space-4);
        margin-bottom: var(--space-4);
    }
}

/* ==========================================================================
   Color Consistency Overrides - تجاوزات تناسق الألوان
   ========================================================================== */

/* Override Tailwind color classes */
.text-blue-600 {
    color: var(--primary-600) !important;
}

.text-blue-800 {
    color: var(--primary-800) !important;
}

.text-gray-600 {
    color: var(--neutral-600) !important;
}

.text-gray-700 {
    color: var(--neutral-700) !important;
}

.text-gray-800 {
    color: var(--neutral-800) !important;
}

.bg-blue-100 {
    background-color: var(--primary-100) !important;
}

.bg-blue-600 {
    background-color: var(--primary-600) !important;
}

.border-gray-200 {
    border-color: var(--neutral-200) !important;
}

.border-gray-300 {
    border-color: var(--neutral-300) !important;
}
