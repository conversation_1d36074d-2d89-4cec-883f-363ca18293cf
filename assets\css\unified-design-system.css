
:root {
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;
    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-200: #fecaca;
    --error-300: #fca5a5;
    --error-400: #f87171;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    --error-800: #991b1b;
    --error-900: #7f1d1d;
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    --font-family-primary: 'Tajawal', 'Arial', sans-serif;
    --font-family-secondary: 'Inter', 'Arial', sans-serif;
    --text-xs: 0.75rem;      
    --text-sm: 0.875rem;     
    --text-base: 1rem;       
    --text-lg: 1.125rem;     
    --text-xl: 1.25rem;      
    --text-2xl: 1.5rem;      
    --text-3xl: 1.875rem;    
    --text-4xl: 2.25rem;     
    --text-5xl: 3rem;        
    --text-6xl: 3.75rem;     
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --space-1: 0.25rem;      
    --space-2: 0.5rem;       
    --space-3: 0.75rem;      
    --space-4: 1rem;         
    --space-5: 1.25rem;      
    --space-6: 1.5rem;       
    --space-8: 2rem;         
    --space-10: 2.5rem;      
    --space-12: 3rem;        
    --space-16: 4rem;        
    --space-20: 5rem;        
    --space-24: 6rem;        
    --radius-sm: 0.125rem;   
    --radius-base: 0.25rem;  
    --radius-md: 0.375rem;   
    --radius-lg: 0.5rem;     
    --radius-xl: 0.75rem;    
    --radius-2xl: 1rem;      
    --radius-3xl: 1.5rem;    
    --radius-full: 9999px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --transition-fast: 150ms ease-in-out;
    --transition-base: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}
* {
    box-sizing: border-box;
}
html {
    scroll-behavior: smooth;
    font-size: 16px;
}
body {
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    color: var(--neutral-800);
    background-color: var(--neutral-50);
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin: 0 0 var(--space-4) 0;
    color: var(--neutral-900);
}
h1 {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
}
h2 {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
}
h3 {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
}
h4 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
}
h5 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
}
h6 {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
}
p {
    margin: 0 0 var(--space-4) 0;
    line-height: var(--leading-relaxed);
}
a {
    color: var(--primary-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}
a:hover {
    color: var(--primary-700);
}
a:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}
@media (min-width: 640px) {
    .container {
        padding: 0 var(--space-6);
    }
}
@media (min-width: 1024px) {
    .container {
        padding: 0 var(--space-8);
    }
}
.grid {
    display: grid;
    gap: var(--space-6);
}
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}
@media (min-width: 1024px) {
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}
.flex {
    display: flex;
}
.flex-col {
    flex-direction: column;
}
.items-center {
    align-items: center;
}
.justify-center {
    justify-content: center;
}
.justify-between {
    justify-content: space-between;
}
.space-x-4 > * + * {
    margin-right: var(--space-4);
}
.space-y-4 > * + * {
    margin-top: var(--space-4);
}
.card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-base);
    overflow: hidden;
    transition: all var(--transition-base);
}
.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    line-height: 1;
}
.btn-primary {
    background-color: var(--primary-600);
    color: white;
}
.btn-primary:hover {
    background-color: var(--primary-700);
    color: white;
}
.btn-secondary {
    background-color: var(--secondary-200);
    color: var(--secondary-800);
}
.btn-secondary:hover {
    background-color: var(--secondary-300);
    color: var(--secondary-900);
}
.btn-success {
    background-color: var(--success-600);
    color: white;
}
.btn-success:hover {
    background-color: var(--success-700);
    color: white;
}
.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
}
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
}
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    border-radius: var(--radius-full);
}
.badge-primary {
    background-color: var(--primary-100);
    color: var(--primary-800);
}
.badge-success {
    background-color: var(--success-100);
    color: var(--success-800);
}
.badge-warning {
    background-color: var(--warning-100);
    color: var(--warning-800);
}
.badge-error {
    background-color: var(--error-100);
    color: var(--error-800);
}
@media (max-width: 767px) {
    .container {
        padding: 0 var(--space-4);
    }
    h1 {
        font-size: var(--text-3xl);
    }
    h2 {
        font-size: var(--text-2xl);
    }
    h3 {
        font-size: var(--text-xl);
    }
}
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    html {
        scroll-behavior: auto;
    }
}
*:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}


.page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}
.main-content {
    flex: 1;
    padding: var(--space-8) 0;
}
.header-top {
    background: var(--neutral-800);
    color: var(--neutral-200);
    padding: var(--space-2) 0;
    font-size: var(--text-sm);
}
.header-main {
    background: white;
    padding: var(--space-4) 0;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: all var(--transition-base);
}
.header-main.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
    padding: var(--space-3) 0;
}
.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-6);
}
.header-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}
.header-nav {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}
.nav-categories {
    background: var(--primary-600);
    padding: var(--space-3) 0;
}
.nav-categories-list {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.nav-categories-list::-webkit-scrollbar {
    display: none;
}
.nav-category-item {
    color: white;
    text-decoration: none;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-weight: var(--font-medium);
    white-space: nowrap;
    transition: all var(--transition-fast);
    font-size: var(--text-sm);
}
.nav-category-item:hover,
.nav-category-item.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}
.content-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
    margin: var(--space-8) 0;
}
@media (min-width: 1024px) {
    .content-wrapper {
        grid-template-columns: 1fr 300px;
    }
    .content-wrapper.full-width {
        grid-template-columns: 1fr;
    }
}
.main-column {
    min-width: 0;
}
.sidebar-column {
    min-width: 0;
}
.hero-section {
    margin-bottom: var(--space-12);
}
.hero-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
}
@media (min-width: 1024px) {
    .hero-grid {
        grid-template-columns: 2fr 1fr;
    }
}
.hero-main {
    position: relative;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}
.hero-side {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}
.articles-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}
@media (min-width: 640px) {
    .articles-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (min-width: 1024px) {
    .articles-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
@media (min-width: 1280px) {
    .articles-grid.large {
        grid-template-columns: repeat(4, 1fr);
    }
}
.category-header {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: white;
    padding: var(--space-12) 0;
    border-radius: var(--radius-2xl);
    margin-bottom: var(--space-8);
    text-align: center;
    position: relative;
    overflow: hidden;
}
.category-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}
.category-header-content {
    position: relative;
    z-index: 1;
}
.article-header {
    margin-bottom: var(--space-8);
    text-align: center;
}
.article-meta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    margin: var(--space-4) 0;
    flex-wrap: wrap;
}
.article-content {
    max-width: 800px;
    margin: 0 auto;
    line-height: var(--leading-relaxed);
}
.article-content h2,
.article-content h3,
.article-content h4 {
    margin-top: var(--space-8);
    margin-bottom: var(--space-4);
}
.article-content p {
    margin-bottom: var(--space-6);
}
.article-content img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    margin: var(--space-6) 0;
    box-shadow: var(--shadow-md);
}
.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}
@media (min-width: 640px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (min-width: 1024px) {
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}
.footer-section {
    min-width: 0;
}
.footer-bottom {
    border-top: 1px solid var(--neutral-700);
    padding-top: var(--space-6);
    text-align: center;
    color: var(--neutral-400);
}
.search-form {
    display: flex;
    gap: var(--space-2);
    margin-bottom: var(--space-8);
}
.search-input {
    flex: 1;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    transition: all var(--transition-fast);
}
.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
.search-button {
    padding: var(--space-3) var(--space-6);
    background: var(--primary-600);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}
.search-button:hover {
    background: var(--primary-700);
}
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin: var(--space-12) 0;
}
.breadcrumb-wrapper {
    margin-bottom: var(--space-6);
    padding: var(--space-3) 0;
    border-bottom: 1px solid var(--neutral-200);
}
@media (max-width: 1023px) {
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
    .sidebar-column {
        order: -1;
    }
}
@media (max-width: 767px) {
    .main-content {
        padding: var(--space-4) 0;
    }
    .header-container {
        flex-direction: column;
        gap: var(--space-4);
    }
    .header-nav {
        order: 1;
        width: 100%;
        justify-content: center;
    }
    .header-actions {
        order: 2;
    }
    .nav-categories-list {
        justify-content: flex-start;
        padding: 0 var(--space-4);
    }
    .hero-grid {
        gap: var(--space-4);
    }
    .articles-grid {
        gap: var(--space-4);
    }
    .category-header {
        padding: var(--space-8) var(--space-4);
        margin: 0 -var(--space-4) var(--space-6);
        border-radius: 0;
    }
    .article-meta {
        flex-direction: column;
        gap: var(--space-2);
    }
    .search-form {
        flex-direction: column;
    }
    .footer-content {
        gap: var(--space-6);
    }
}
@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-3);
    }
    .header-main {
        padding: var(--space-3) 0;
    }
    .nav-categories {
        padding: var(--space-2) 0;
    }
    .content-wrapper {
        margin: var(--space-4) 0;
        gap: var(--space-4);
    }
    .articles-grid {
        gap: var(--space-3);
    }
}
@media print {
    .header-main,
    .nav-categories,
    .sidebar-column,
    .footer-content,
    .back-to-top,
    .search-form {
        display: none !important;
    }
    .main-content {
        padding: 0;
    }
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 0;
        margin: 0;
    }
    .news-card,
    .article-content {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
        margin-bottom: 1rem;
    }
}


.site-header {
    background: white;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: all var(--transition-base);
}
.site-header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
}
.site-logo {
    display: flex;
    align-items: center;
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--primary-600);
    text-decoration: none;
}
.site-logo:hover {
    color: var(--primary-700);
}
.site-logo i {
    margin-left: var(--space-2);
    font-size: var(--text-2xl);
}
.main-nav {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}
.nav-link {
    color: var(--neutral-700);
    font-weight: var(--font-medium);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}
.nav-link:hover,
.nav-link.active {
    color: var(--primary-600);
    background-color: var(--primary-50);
}
.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--primary-600);
    transition: all var(--transition-fast);
    transform: translateX(-50%);
}
.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--text-xl);
    color: var(--neutral-700);
    cursor: pointer;
    padding: var(--space-2);
}
@media (max-width: 767px) {
    .mobile-nav-toggle {
        display: block;
    }
    .main-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: var(--shadow-lg);
        flex-direction: column;
        padding: var(--space-4);
        gap: var(--space-2);
    }
    .main-nav.active {
        display: flex;
    }
    .nav-link {
        width: 100%;
        text-align: center;
        padding: var(--space-3);
    }
}
.breaking-news {
    background: linear-gradient(135deg, var(--error-600) 0%, var(--error-700) 100%);
    color: white;
    padding: var(--space-2) 0;
    overflow: hidden;
    position: relative;
}
.breaking-news-content {
    display: flex;
    align-items: center;
    animation: scroll-rtl 30s linear infinite;
}
.breaking-news-label {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-weight: var(--font-bold);
    font-size: var(--text-sm);
    margin-left: var(--space-4);
    white-space: nowrap;
}
@keyframes scroll-rtl {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}
.news-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-base);
    overflow: hidden;
    transition: all var(--transition-base);
    height: 100%;
    display: flex;
    flex-direction: column;
}
.news-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
}
.news-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition-base);
}
.news-card:hover .news-card-image {
    transform: scale(1.05);
}
.news-card-content {
    padding: var(--space-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}
.news-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-3);
}
.news-card-category {
    background: var(--primary-100);
    color: var(--primary-800);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
}
.news-card-date {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}
.news-card-title {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-3);
    color: var(--neutral-900);
}
.news-card-title a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}
.news-card-title a:hover {
    color: var(--primary-600);
}
.news-card-excerpt {
    color: var(--neutral-600);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.news-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--space-3);
    border-top: 1px solid var(--neutral-200);
    margin-top: auto;
}
.news-card-views {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}
.news-card-read-more {
    color: var(--primary-600);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-1);
    transition: color var(--transition-fast);
}
.news-card-read-more:hover {
    color: var(--primary-700);
}
.news-card-featured {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    border: 2px solid var(--primary-200);
    position: relative;
}
.news-card-featured::before {
    content: '⭐';
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    font-size: var(--text-lg);
    z-index: 1;
}
.sidebar-widget {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-base);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
}
.sidebar-widget-title {
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid var(--primary-100);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}
.sidebar-widget-title i {
    color: var(--primary-600);
}
.sidebar-article {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}
.sidebar-article:hover {
    background-color: var(--neutral-50);
}
.sidebar-article-image {
    width: 60px;
    height: 45px;
    border-radius: var(--radius-md);
    object-fit: cover;
    flex-shrink: 0;
}
.sidebar-article-content {
    flex: 1;
    min-width: 0;
}
.sidebar-article-title {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-1);
    color: var(--neutral-900);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.sidebar-article-title a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}
.sidebar-article-title a:hover {
    color: var(--primary-600);
}
.sidebar-article-meta {
    color: var(--neutral-500);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}
.site-footer {
    background: var(--neutral-800);
    color: var(--neutral-200);
    padding: var(--space-16) 0 var(--space-8);
    margin-top: var(--space-16);
}
.footer-section-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: white;
    margin-bottom: var(--space-4);
}
.footer-link {
    color: var(--neutral-300);
    text-decoration: none;
    transition: color var(--transition-fast);
    display: block;
    padding: var(--space-1) 0;
}
.footer-link:hover {
    color: white;
}
.footer-social-links {
    display: flex;
    gap: var(--space-4);
    margin-top: var(--space-4);
}
.footer-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--neutral-700);
    color: var(--neutral-300);
    border-radius: var(--radius-full);
    text-decoration: none;
    transition: all var(--transition-fast);
}
.footer-social-link:hover {
    background: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}
.footer-copyright {
    border-top: 1px solid var(--neutral-700);
    margin-top: var(--space-8);
    padding-top: var(--space-8);
    text-align: center;
    color: var(--neutral-400);
}
.form-group {
    margin-bottom: var(--space-4);
}
.form-label {
    display: block;
    font-weight: var(--font-medium);
    color: var(--neutral-700);
    margin-bottom: var(--space-2);
}
.form-input {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: all var(--transition-fast);
    background: white;
}
.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
.form-input:invalid {
    border-color: var(--error-500);
}
.form-textarea {
    resize: vertical;
    min-height: 120px;
}
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}
.back-to-top {
    position: fixed;
    bottom: var(--space-6);
    right: var(--space-6);
    width: 50px;
    height: 50px;
    background: var(--primary-600);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-base);
    opacity: 0;
    visibility: hidden;
    z-index: var(--z-fixed);
}
.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}
.back-to-top:hover {
    background: var(--primary-700);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--neutral-200);
    border-radius: 50%;
    border-top-color: var(--primary-600);
    animation: spin 1s ease-in-out infinite;
}
@keyframes spin {
    to { transform: rotate(360deg); }
}
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--neutral-500);
    margin-bottom: var(--space-6);
}
.breadcrumb-link {
    color: var(--neutral-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}
.breadcrumb-link:hover {
    color: var(--primary-600);
}
.breadcrumb-separator {
    color: var(--neutral-400);
}
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    margin: var(--space-8) 0;
}
.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    color: var(--neutral-700);
    text-decoration: none;
    transition: all var(--transition-fast);
}
.pagination-link:hover,
.pagination-link.active {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}
.pagination-link:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.matches-widget {
    background: linear-gradient(135deg, var(--success-600) 0%, var(--primary-600) 100%);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    margin: var(--space-8) 0;
}
.matches-widget .widget-header {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--primary-500) 100%);
    padding: var(--space-6);
    color: white;
}
.match-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    border: 1px solid var(--neutral-200);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
}
.match-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-300);
}
.match-date-badge {
    background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
    color: var(--primary-800);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}
.vs-indicator {
    background: linear-gradient(135deg, var(--neutral-200) 0%, var(--neutral-300) 100%);
    color: var(--neutral-700);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-weight: var(--font-bold);
    font-size: var(--text-xs);
    box-shadow: var(--shadow-sm);
}
.team-name {
    font-weight: var(--font-semibold);
    color: var(--neutral-800);
    font-size: var(--text-sm);
    transition: color var(--transition-fast);
}
.match-card:hover .team-name {
    color: var(--primary-600);
}
.featured-match {
    background: linear-gradient(135deg, var(--warning-50) 0%, var(--warning-100) 100%);
    border-color: var(--warning-300);
    position: relative;
}
.featured-indicator {
    background: linear-gradient(135deg, var(--warning-200) 0%, var(--warning-300) 100%);
    color: var(--warning-800);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}
@media (max-width: 767px) {
    .news-card-content {
        padding: var(--space-4);
    }
    .sidebar-widget {
        padding: var(--space-4);
        margin-bottom: var(--space-4);
    }
    .back-to-top {
        bottom: var(--space-4);
        right: var(--space-4);
        width: 45px;
        height: 45px;
    }
    .matches-widget .widget-header {
        padding: var(--space-4);
    }
    .match-card {
        padding: var(--space-4);
    }
}

