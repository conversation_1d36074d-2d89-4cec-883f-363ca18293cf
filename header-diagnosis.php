<?php
/**
 * تشخيص شامل لمشاكل الهيدر والتمرير
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تشخيص مشاكل الهيدر</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo "table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo "th, td { padding: 12px; text-align: right; border: 1px solid #ddd; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; border-left: 4px solid #007bff; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 تشخيص شامل لمشاكل الهيدر والتمرير</h1>";

// 1. فحص ملف header.php
echo "<div class='section info'>";
echo "<h2>1. فحص ملف header.php</h2>";

if (file_exists('includes/header.php')) {
    $header_content = file_get_contents('includes/header.php');
    $header_lines = explode("\n", $header_content);
    
    echo "<p>✅ ملف header.php موجود</p>";
    echo "<p><strong>عدد الأسطر:</strong> " . count($header_lines) . "</p>";
    echo "<p><strong>حجم الملف:</strong> " . round(strlen($header_content) / 1024, 2) . " KB</p>";
    
    // فحص العناصر المكررة
    $duplicate_checks = [
        'DOCTYPE' => '/<!DOCTYPE/i',
        'html tag' => '/<html/i',
        'head tag' => '/<head/i',
        'body tag' => '/<body/i',
        'header tag' => '/<header/i',
        'nav tag' => '/<nav/i',
        'main-navigation' => '/main-navigation/i',
        'navigation-enhancements.css' => '/navigation-enhancements\.css/i',
        'navigation-enhancements.js' => '/navigation-enhancements\.js/i'
    ];
    
    echo "<h3>فحص العناصر المكررة:</h3>";
    echo "<table>";
    echo "<tr><th>العنصر</th><th>عدد التكرارات</th><th>الحالة</th></tr>";
    
    foreach ($duplicate_checks as $element => $pattern) {
        preg_match_all($pattern, $header_content, $matches);
        $count = count($matches[0]);
        
        $status = '';
        $class = '';
        if ($element === 'DOCTYPE' || $element === 'html tag' || $element === 'head tag' || $element === 'body tag') {
            if ($count > 1) {
                $status = '❌ تكرار مشكوك فيه';
                $class = 'error';
            } elseif ($count == 1) {
                $status = '✅ طبيعي';
                $class = 'success';
            } else {
                $status = '⚠️ مفقود';
                $class = 'warning';
            }
        } else {
            if ($count > 1) {
                $status = '❌ مكرر';
                $class = 'error';
            } elseif ($count == 1) {
                $status = '✅ طبيعي';
                $class = 'success';
            } else {
                $status = 'ℹ️ غير موجود';
                $class = 'info';
            }
        }
        
        echo "<tr class='$class'>";
        echo "<td>$element</td>";
        echo "<td>$count</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    echo "<p style='color: red;'>❌ ملف header.php غير موجود</p>";
}
echo "</div>";

// 2. فحص استدعاءات الهيدر في الصفحات
echo "<div class='section info'>";
echo "<h2>2. فحص استدعاءات الهيدر في الصفحات</h2>";

$pages_to_check = [
    'index.php' => 'الصفحة الرئيسية',
    'matches.php' => 'صفحة المباريات',
    'article.php' => 'صفحة المقال',
    'category.php' => 'صفحة التصنيف',
    'search.php' => 'صفحة البحث'
];

echo "<table>";
echo "<tr><th>الصفحة</th><th>عدد استدعاءات الهيدر</th><th>الحالة</th><th>التفاصيل</th></tr>";

foreach ($pages_to_check as $page => $description) {
    if (file_exists($page)) {
        $page_content = file_get_contents($page);
        
        // البحث عن استدعاءات الهيدر
        $header_includes = [
            "include 'includes/header.php'",
            'include "includes/header.php"',
            "require 'includes/header.php'",
            'require "includes/header.php"',
            "include_once 'includes/header.php'",
            'include_once "includes/header.php"',
            "require_once 'includes/header.php'",
            'require_once "includes/header.php"'
        ];
        
        $total_includes = 0;
        $include_details = [];
        
        foreach ($header_includes as $include_pattern) {
            $count = substr_count($page_content, $include_pattern);
            if ($count > 0) {
                $total_includes += $count;
                $include_details[] = "$include_pattern ($count مرة)";
            }
        }
        
        $status = '';
        $class = '';
        if ($total_includes > 1) {
            $status = '❌ تكرار';
            $class = 'error';
        } elseif ($total_includes == 1) {
            $status = '✅ طبيعي';
            $class = 'success';
        } else {
            $status = '⚠️ لا يوجد';
            $class = 'warning';
        }
        
        echo "<tr class='$class'>";
        echo "<td>$description</td>";
        echo "<td>$total_includes</td>";
        echo "<td>$status</td>";
        echo "<td>" . implode('<br>', $include_details) . "</td>";
        echo "</tr>";
    } else {
        echo "<tr>";
        echo "<td>$description</td>";
        echo "<td>-</td>";
        echo "<td>❌ الملف غير موجود</td>";
        echo "<td>-</td>";
        echo "</tr>";
    }
}
echo "</table>";
echo "</div>";

// 3. فحص ملفات CSS و JavaScript
echo "<div class='section info'>";
echo "<h2>3. فحص ملفات CSS و JavaScript</h2>";

$css_js_files = [
    'assets/css/navigation-enhancements.css' => 'CSS التنقل',
    'assets/js/navigation-enhancements.js' => 'JavaScript التنقل',
    'assets/css/homepage-enhancements.css' => 'CSS الصفحة الرئيسية',
    'assets/js/homepage-enhancements.js' => 'JavaScript الصفحة الرئيسية'
];

echo "<table>";
echo "<tr><th>الملف</th><th>الحالة</th><th>الحجم</th><th>آخر تعديل</th></tr>";

foreach ($css_js_files as $file => $description) {
    if (file_exists($file)) {
        $size = round(filesize($file) / 1024, 2) . ' KB';
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "<tr class='success'>";
        echo "<td>$description</td>";
        echo "<td>✅ موجود</td>";
        echo "<td>$size</td>";
        echo "<td>$modified</td>";
        echo "</tr>";
    } else {
        echo "<tr class='error'>";
        echo "<td>$description</td>";
        echo "<td>❌ غير موجود</td>";
        echo "<td>-</td>";
        echo "<td>-</td>";
        echo "</tr>";
    }
}
echo "</table>";
echo "</div>";

// 4. فحص تضارب CSS
echo "<div class='section warning'>";
echo "<h2>4. فحص تضارب CSS</h2>";

if (file_exists('assets/css/navigation-enhancements.css')) {
    $nav_css = file_get_contents('assets/css/navigation-enhancements.css');
    
    // البحث عن قواعد CSS مكررة
    preg_match_all('/\.([a-zA-Z0-9_-]+)\s*\{/', $nav_css, $css_classes);
    $class_counts = array_count_values($css_classes[1]);
    $duplicate_classes = array_filter($class_counts, function($count) { return $count > 1; });
    
    if (!empty($duplicate_classes)) {
        echo "<p style='color: orange;'>⚠️ تم العثور على كلاسات CSS مكررة:</p>";
        echo "<ul>";
        foreach ($duplicate_classes as $class => $count) {
            echo "<li>.$class (مكرر $count مرات)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ لا توجد كلاسات CSS مكررة</p>";
    }
    
    // فحص تضارب الخصائص
    $conflicting_properties = [
        'position: fixed',
        'position: sticky',
        'z-index:',
        'top: 0',
        'background:'
    ];
    
    echo "<h3>فحص الخصائص المتضاربة:</h3>";
    echo "<table>";
    echo "<tr><th>الخاصية</th><th>عدد الاستخدامات</th><th>الحالة</th></tr>";
    
    foreach ($conflicting_properties as $property) {
        $count = substr_count($nav_css, $property);
        $status = $count > 3 ? '⚠️ كثير' : ($count > 0 ? '✅ طبيعي' : 'ℹ️ غير مستخدم');
        $class = $count > 3 ? 'warning' : ($count > 0 ? 'success' : 'info');
        
        echo "<tr class='$class'>";
        echo "<td>$property</td>";
        echo "<td>$count</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ ملف navigation-enhancements.css غير موجود</p>";
}
echo "</div>";

// 5. فحص JavaScript Event Listeners
echo "<div class='section warning'>";
echo "<h2>5. فحص JavaScript Event Listeners</h2>";

if (file_exists('assets/js/navigation-enhancements.js')) {
    $nav_js = file_get_contents('assets/js/navigation-enhancements.js');
    
    $event_listeners = [
        'addEventListener' => '/addEventListener/i',
        'scroll events' => '/scroll.*addEventListener/i',
        'click events' => '/click.*addEventListener/i',
        'DOMContentLoaded' => '/DOMContentLoaded/i',
        'window.onload' => '/window\.onload/i'
    ];
    
    echo "<table>";
    echo "<tr><th>نوع Event Listener</th><th>عدد الاستخدامات</th><th>الحالة</th></tr>";
    
    foreach ($event_listeners as $listener => $pattern) {
        preg_match_all($pattern, $nav_js, $matches);
        $count = count($matches[0]);
        
        $status = '';
        $class = '';
        if ($listener === 'scroll events' && $count > 2) {
            $status = '⚠️ كثير - قد يؤثر على الأداء';
            $class = 'warning';
        } elseif ($count > 0) {
            $status = '✅ طبيعي';
            $class = 'success';
        } else {
            $status = 'ℹ️ غير مستخدم';
            $class = 'info';
        }
        
        echo "<tr class='$class'>";
        echo "<td>$listener</td>";
        echo "<td>$count</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ ملف navigation-enhancements.js غير موجود</p>";
}
echo "</div>";

// 6. اختبار الأداء
echo "<div class='section info'>";
echo "<h2>6. اختبار الأداء</h2>";

$start_time = microtime(true);

// محاكاة تحميل الصفحة
ob_start();
try {
    include 'includes/header.php';
    $header_output = ob_get_contents();
} catch (Exception $e) {
    $header_output = '';
}
ob_end_clean();

$end_time = microtime(true);
$load_time = round(($end_time - $start_time) * 1000, 2);

echo "<table>";
echo "<tr><th>المقياس</th><th>القيمة</th><th>الحالة</th></tr>";
echo "<tr><td>وقت تحميل الهيدر</td><td>{$load_time} مللي ثانية</td><td>" . ($load_time < 100 ? '✅ سريع' : '⚠️ بطيء') . "</td></tr>";
echo "<tr><td>حجم HTML المولد</td><td>" . round(strlen($header_output) / 1024, 2) . " KB</td><td>" . (strlen($header_output) < 50000 ? '✅ مناسب' : '⚠️ كبير') . "</td></tr>";
echo "</table>";
echo "</div>";

// 7. توصيات الإصلاح
echo "<div class='section warning'>";
echo "<h2>7. توصيات الإصلاح</h2>";
echo "<ul>";
echo "<li>🔧 <strong>إزالة التكرارات:</strong> فحص وإزالة أي عناصر HTML مكررة</li>";
echo "<li>⚡ <strong>تحسين JavaScript:</strong> دمج event listeners وتحسين الأداء</li>";
echo "<li>🎨 <strong>تنظيف CSS:</strong> إزالة القواعد المكررة والمتضاربة</li>";
echo "<li>📱 <strong>اختبار التمرير:</strong> التأكد من سلاسة تأثيرات التمرير</li>";
echo "<li>🔍 <strong>فحص الاستجابة:</strong> اختبار على أجهزة مختلفة</li>";
echo "</ul>";
echo "</div>";

// 8. أدوات الإصلاح
echo "<div class='section success'>";
echo "<h2>8. أدوات الإصلاح</h2>";
echo "<a href='fix-header-issues.php' class='btn btn-danger'>إصلاح مشاكل الهيدر</a>";
echo "<a href='optimize-scroll-effects.php' class='btn btn-warning'>تحسين تأثيرات التمرير</a>";
echo "<a href='clean-css-js.php' class='btn btn-primary'>تنظيف CSS/JS</a>";
echo "<a href='test-header-performance.php' class='btn btn-success'>اختبار الأداء</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
