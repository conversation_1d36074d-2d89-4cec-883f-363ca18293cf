<?php
/**
 * HTTP 500 Error Debugging Tool
 * This file helps diagnose internal server errors
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any output before headers
ob_start();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 Error Debugging Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .status-ok { color: #10B981; }
        .status-warning { color: #F59E0B; }
        .status-error { color: #EF4444; }
        .code-block { background: #f8f9fa; border: 1px solid #e9ecef; padding: 10px; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-red-600 mb-6">🚨 HTTP 500 Error Debugging Tool</h1>
            
            <!-- Quick Status -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h2 class="text-lg font-semibold mb-2">Quick Status Check</h2>
                <p class="status-ok">✅ PHP is working (you can see this page)</p>
                <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            </div>

            <!-- PHP Configuration -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">PHP Configuration</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-gray-50 p-4 rounded">
                        <h3 class="font-semibold mb-2">Error Settings</h3>
                        <p><strong>Error Reporting:</strong> <?php echo error_reporting(); ?></p>
                        <p><strong>Display Errors:</strong> <?php echo ini_get('display_errors') ? 'On' : 'Off'; ?></p>
                        <p><strong>Log Errors:</strong> <?php echo ini_get('log_errors') ? 'On' : 'Off'; ?></p>
                        <p><strong>Error Log:</strong> <?php echo ini_get('error_log') ?: 'Default'; ?></p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded">
                        <h3 class="font-semibold mb-2">Memory & Limits</h3>
                        <p><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></p>
                        <p><strong>Max Execution Time:</strong> <?php echo ini_get('max_execution_time'); ?>s</p>
                        <p><strong>Upload Max Size:</strong> <?php echo ini_get('upload_max_filesize'); ?></p>
                        <p><strong>Post Max Size:</strong> <?php echo ini_get('post_max_size'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Required Extensions Check -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Required PHP Extensions</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <?php
                    $required_extensions = [
                        'pdo' => 'PDO',
                        'pdo_mysql' => 'PDO MySQL',
                        'simplexml' => 'SimpleXML',
                        'curl' => 'cURL',
                        'mbstring' => 'Multibyte String',
                        'json' => 'JSON',
                        'session' => 'Session',
                        'filter' => 'Filter'
                    ];

                    foreach ($required_extensions as $ext => $name) {
                        $loaded = extension_loaded($ext);
                        $status_class = $loaded ? 'status-ok' : 'status-error';
                        $status_text = $loaded ? '✅' : '❌';
                        echo "<div class='p-2 border rounded'>";
                        echo "<span class='$status_class'>$status_text $name</span>";
                        echo "</div>";
                    }
                    ?>
                </div>
            </div>

            <!-- File Existence Check -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Critical Files Check</h2>
                <div class="space-y-2">
                    <?php
                    $critical_files = [
                        'config/config.php',
                        'config/database.php',
                        'includes/functions.php',
                        'includes/header.php',
                        'includes/footer.php',
                        'classes/RSSParser.php',
                        'admin/includes/auth.php',
                        'admin/includes/header.php'
                    ];

                    foreach ($critical_files as $file) {
                        $exists = file_exists($file);
                        $readable = $exists ? is_readable($file) : false;
                        
                        if ($exists && $readable) {
                            $status = "<span class='status-ok'>✅ OK</span>";
                        } elseif ($exists) {
                            $status = "<span class='status-warning'>⚠️ Not Readable</span>";
                        } else {
                            $status = "<span class='status-error'>❌ Missing</span>";
                        }
                        
                        echo "<div class='flex justify-between items-center p-2 border rounded'>";
                        echo "<span>$file</span>";
                        echo $status;
                        echo "</div>";
                    }
                    ?>
                </div>
            </div>

            <!-- Syntax Check -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">PHP Syntax Check</h2>
                <div class="space-y-2">
                    <?php
                    $php_files_to_check = [
                        'index.php',
                        'config/config.php',
                        'config/database.php',
                        'includes/functions.php'
                    ];

                    foreach ($php_files_to_check as $file) {
                        if (file_exists($file)) {
                            $output = [];
                            $return_code = 0;
                            exec("php -l \"$file\" 2>&1", $output, $return_code);
                            
                            if ($return_code === 0) {
                                echo "<div class='p-2 bg-green-50 border border-green-200 rounded'>";
                                echo "<span class='status-ok'>✅ $file - Syntax OK</span>";
                                echo "</div>";
                            } else {
                                echo "<div class='p-2 bg-red-50 border border-red-200 rounded'>";
                                echo "<span class='status-error'>❌ $file - Syntax Error:</span>";
                                echo "<div class='code-block mt-2'>" . implode("\n", $output) . "</div>";
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='p-2 bg-yellow-50 border border-yellow-200 rounded'>";
                            echo "<span class='status-warning'>⚠️ $file - File not found</span>";
                            echo "</div>";
                        }
                    }
                    ?>
                </div>
            </div>

            <!-- Include Test -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Include/Require Test</h2>
                <div class="space-y-2">
                    <?php
                    $include_tests = [
                        'config/config.php' => 'Configuration',
                        'config/database.php' => 'Database Config',
                        'includes/functions.php' => 'Functions'
                    ];

                    foreach ($include_tests as $file => $description) {
                        echo "<div class='p-2 border rounded'>";
                        echo "<strong>Testing: $description ($file)</strong><br>";
                        
                        try {
                            if (file_exists($file)) {
                                ob_start();
                                $error_before = error_get_last();
                                include_once $file;
                                $error_after = error_get_last();
                                $output = ob_get_clean();
                                
                                if ($error_after && $error_after !== $error_before) {
                                    echo "<span class='status-error'>❌ Error: " . $error_after['message'] . "</span>";
                                } else {
                                    echo "<span class='status-ok'>✅ Included successfully</span>";
                                }
                                
                                if (!empty($output)) {
                                    echo "<div class='code-block mt-2'>Output: " . htmlspecialchars($output) . "</div>";
                                }
                            } else {
                                echo "<span class='status-error'>❌ File not found</span>";
                            }
                        } catch (Exception $e) {
                            echo "<span class='status-error'>❌ Exception: " . $e->getMessage() . "</span>";
                        } catch (Error $e) {
                            echo "<span class='status-error'>❌ Fatal Error: " . $e->getMessage() . "</span>";
                        }
                        echo "</div>";
                    }
                    ?>
                </div>
            </div>

            <!-- Database Connection Test -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Database Connection Test</h2>
                <div class="p-4 border rounded">
                    <?php
                    try {
                        if (file_exists('config/database.php')) {
                            include_once 'config/database.php';
                            
                            if (class_exists('Database')) {
                                $database = new Database();
                                $db = $database->connect();
                                
                                if ($db) {
                                    echo "<span class='status-ok'>✅ Database connection successful</span>";
                                    
                                    // Test a simple query
                                    $stmt = $db->query("SELECT 1 as test");
                                    if ($stmt) {
                                        echo "<br><span class='status-ok'>✅ Database query test successful</span>";
                                    }
                                } else {
                                    echo "<span class='status-error'>❌ Database connection failed</span>";
                                }
                            } else {
                                echo "<span class='status-error'>❌ Database class not found</span>";
                            }
                        } else {
                            echo "<span class='status-error'>❌ Database config file not found</span>";
                        }
                    } catch (Exception $e) {
                        echo "<span class='status-error'>❌ Database Error: " . $e->getMessage() . "</span>";
                    }
                    ?>
                </div>
            </div>

            <!-- Error Log Check -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Recent Error Logs</h2>
                <div class="p-4 border rounded">
                    <?php
                    $error_logs = [
                        ini_get('error_log'),
                        '/var/log/apache2/error.log',
                        '/var/log/httpd/error_log',
                        'C:/xampp/apache/logs/error.log',
                        'C:/wamp64/logs/apache_error.log',
                        '/Applications/XAMPP/logs/error_log'
                    ];

                    $found_logs = false;
                    foreach ($error_logs as $log_file) {
                        if ($log_file && file_exists($log_file) && is_readable($log_file)) {
                            $found_logs = true;
                            echo "<h4 class='font-semibold'>$log_file</h4>";
                            
                            $lines = file($log_file);
                            $recent_lines = array_slice($lines, -10); // Last 10 lines
                            
                            echo "<div class='code-block'>";
                            foreach ($recent_lines as $line) {
                                echo htmlspecialchars($line) . "<br>";
                            }
                            echo "</div>";
                            break; // Show only the first found log
                        }
                    }
                    
                    if (!$found_logs) {
                        echo "<span class='status-warning'>⚠️ No accessible error logs found</span>";
                        echo "<p class='mt-2'>Check these locations manually:</p>";
                        echo "<ul class='list-disc list-inside'>";
                        foreach ($error_logs as $log) {
                            if ($log) echo "<li class='text-sm'>$log</li>";
                        }
                        echo "</ul>";
                    }
                    ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Quick Actions</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <button onclick="testFile('index.php')" class="bg-blue-500 text-white p-2 rounded hover:bg-blue-600">
                        Test index.php
                    </button>
                    <button onclick="testFile('setup.php')" class="bg-green-500 text-white p-2 rounded hover:bg-green-600">
                        Test setup.php
                    </button>
                    <button onclick="testFile('admin/login.php')" class="bg-purple-500 text-white p-2 rounded hover:bg-purple-600">
                        Test admin login
                    </button>
                    <button onclick="location.reload()" class="bg-gray-500 text-white p-2 rounded hover:bg-gray-600">
                        Refresh Check
                    </button>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-yellow-50 p-4 rounded-lg">
                <h3 class="font-semibold text-yellow-800 mb-2">🔧 Troubleshooting Steps</h3>
                <ol class="list-decimal list-inside space-y-1 text-yellow-700">
                    <li>Check the syntax errors above (if any)</li>
                    <li>Verify all required files exist and are readable</li>
                    <li>Check database connection settings</li>
                    <li>Review Apache/PHP error logs</li>
                    <li>Test individual files using the buttons above</li>
                    <li>If all else fails, use the minimal test files</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function testFile(filename) {
            window.open(filename, '_blank');
        }
    </script>
</body>
</html>

<?php
// Flush output buffer
ob_end_flush();
?>
