# دليل شريط الأخبار العاجلة

## 🚨 نظرة عامة

تم إضافة شريط أخبار عاجلة متطور ومتجاوب إلى موقع الأخبار PHP. يظهر الشريط تحت الهيدر مباشرة ويعرض آخر الأخبار المميزة أو أحدث الأخبار.

## ✨ الميزات

### الميزات الأساسية
- 📱 **تصميم متجاوب** - يعمل على جميع الأجهزة
- 🎯 **حركة سلسة** - انتقال من اليمين إلى اليسار مناسب للعربية
- ⏯️ **تحكم كامل** - إيقاف/تشغيل وإغلاق
- 🖱️ **إيقاف عند Hover** - توقف الحركة عند تمرير الماوس
- 🔄 **تحديث تلقائي** - تحديث المحتوى كل 5 دقائق
- 💾 **حفظ التفضيلات** - يتذكر إعدادات المستخدم

### الميزات المتقدمة
- ⌨️ **تحكم بلوحة المفاتيح** - مسطرة المسافة للإيقاف/التشغيل، Escape للإغلاق
- 👆 **تحكم باللمس** - إيماءات السحب للتحكم
- 📊 **تتبع النقرات** - إحصائيات تفاعل المستخدمين
- 🎨 **تأثيرات بصرية** - أنيميشن متقدمة وتأثيرات ضوئية
- 🔧 **قابل للتخصيص** - سهولة تعديل الألوان والسرعة

## 🏗️ الهيكل التقني

### الملفات المضافة/المحدثة

```
includes/header.php          # الملف الرئيسي المحدث
assets/css/breaking-news.css # ملف CSS متقدم
assets/js/breaking-news.js   # ملف JavaScript متقدم
test-breaking-news.php       # صفحة اختبار الشريط
```

### الكود في header.php

```php
<!-- Breaking News Ticker -->
<?php
// جلب آخر 8 أخبار مميزة أو أحدث الأخبار
$breaking_news = getFeaturedArticles(8);
if (empty($breaking_news)) {
    $breaking_news = getLatestArticles(8);
}

if (!empty($breaking_news)):
?>
<div id="breaking-news-ticker" class="bg-gradient-to-r from-red-600 to-red-700 text-white shadow-lg relative overflow-hidden">
    <!-- محتوى الشريط -->
</div>
<?php endif; ?>
```

## 🎨 التصميم والألوان

### الألوان المستخدمة
- **الخلفية**: تدرج أحمر (`from-red-600 to-red-700`)
- **النص**: أبيض (`text-white`)
- **التسمية**: خلفية بيضاء مع نص أحمر
- **الروابط عند Hover**: أصفر ذهبي (`text-yellow-300`)

### التأثيرات البصرية
- تأثير الضوء المتحرك على الخلفية
- نبضة في تسمية "عاجل"
- وميض أيقونة البرق
- تأثيرات hover متقدمة

## 🔧 التخصيص

### تغيير السرعة
```javascript
// في ملف breaking-news.js
this.animationSpeed = 45; // بالثواني - قم بتغيير هذا الرقم
```

### تغيير الألوان
```css
/* في ملف breaking-news.css */
.breaking-news-ticker {
    background: linear-gradient(135deg, #your-color 0%, #your-color-2 100%);
}
```

### تغيير عدد الأخبار
```php
// في header.php
$breaking_news = getFeaturedArticles(8); // غير الرقم 8
```

## 📱 التجاوب

### نقاط التوقف
- **Desktop**: سرعة عادية (45 ثانية)
- **Tablet** (< 768px): سرعة أسرع (36 ثانية)
- **Mobile** (< 480px): سرعة أسرع (31.5 ثانية)

### التكيف مع الشاشات الصغيرة
- تصغير حجم الخط
- إخفاء النص الإضافي في التسمية
- تقليل المسافات والحشو

## 🎮 التحكم والتفاعل

### أزرار التحكم
- **⏸️ إيقاف/تشغيل**: يوقف أو يشغل حركة النص
- **❌ إغلاق**: يخفي الشريط لمدة ساعة

### تحكم لوحة المفاتيح
- **مسطرة المسافة**: إيقاف/تشغيل
- **Escape**: إغلاق الشريط

### تحكم اللمس (الأجهزة المحمولة)
- **سحب يمين**: إيقاف/تشغيل
- **سحب يسار**: إغلاق الشريط

## 🔄 التحديث التلقائي

### آلية العمل
- يتم تحديث المحتوى كل 5 دقائق
- يحدث فقط عندما تكون الصفحة مرئية
- لا يحدث أثناء hover المستخدم
- انتقال سلس عند التحديث

### تخصيص فترة التحديث
```javascript
// في breaking-news.js
setInterval(() => {
    // كود التحديث
}, 300000); // 300000 = 5 دقائق بالميلي ثانية
```

## 💾 حفظ التفضيلات

### البيانات المحفوظة
- حالة الإيقاف/التشغيل
- حالة الإخفاء ووقته
- إحصائيات النقرات

### مسح التفضيلات
```javascript
// مسح جميع تفضيلات شريط الأخبار
localStorage.removeItem('breakingNews_paused');
localStorage.removeItem('breakingNews_hidden');
localStorage.removeItem('breakingNews_hiddenTime');
```

## 🧪 الاختبار

### صفحة الاختبار
زر `test-breaking-news.php` لاختبار جميع وظائف الشريط:

```
http://yoursite.com/test-breaking-news.php
```

### اختبارات متاحة
- اختبار الإيقاف/التشغيل
- اختبار تأثير Hover
- اختبار التجاوب
- محاكاة تحديث المحتوى

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### الشريط لا يظهر
```php
// تأكد من وجود مقالات في قاعدة البيانات
$articles_count = $db->query("SELECT COUNT(*) FROM articles")->fetchColumn();
echo "عدد المقالات: " . $articles_count;
```

#### الحركة لا تعمل
```javascript
// تحقق من وجود العناصر
console.log(document.getElementById('breaking-news-ticker'));
console.log(document.getElementById('news-marquee'));
```

#### مشاكل التجاوب
```css
/* تأكد من تحميل CSS */
@media (max-width: 768px) {
    /* قواعد التجاوب */
}
```

## 📈 الأداء

### تحسينات مطبقة
- استخدام `transform` بدلاً من `left/right`
- `will-change: transform` للتحسين
- `backface-visibility: hidden`
- تحديث محدود بناءً على رؤية الصفحة

### مراقبة الأداء
```javascript
// مراقبة استخدام الذاكرة
console.log(performance.memory);

// مراقبة FPS
let lastTime = 0;
function measureFPS(currentTime) {
    const fps = 1000 / (currentTime - lastTime);
    console.log('FPS:', fps);
    lastTime = currentTime;
    requestAnimationFrame(measureFPS);
}
requestAnimationFrame(measureFPS);
```

## 🚀 التطوير المستقبلي

### ميزات مقترحة
- [ ] تكامل مع WebSocket للتحديث الفوري
- [ ] دعم الإشعارات المتقدمة
- [ ] تخصيص الألوان من لوحة التحكم
- [ ] إحصائيات مفصلة للتفاعل
- [ ] دعم أنواع مختلفة من التنبيهات

### API للتطوير
```javascript
// الوصول للكائن الرئيسي
const ticker = window.breakingNewsTicker;

// التحكم برمجياً
ticker.pause();
ticker.play();
ticker.closeTicker();
ticker.refreshContent();
```

---

## 📞 الدعم

للمساعدة أو الاستفسارات حول شريط الأخبار العاجلة، يرجى مراجعة:
- ملف `test-breaking-news.php` للاختبار
- كود المصدر في `includes/header.php`
- ملفات CSS و JavaScript في مجلد `assets/`
