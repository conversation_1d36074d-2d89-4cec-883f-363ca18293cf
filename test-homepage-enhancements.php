<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

session_start();

$page_title = 'اختبار تحسينات الصفحة الرئيسية';

// إضافة بعض المقالات التجريبية إذا لم توجد
$database = new Database();
$db = $database->connect();

// التحقق من وجود مقالات كافية
$stmt = $db->query("SELECT COUNT(*) FROM articles");
$articles_count = $stmt->fetchColumn();

if ($articles_count < 20) {
    // إضافة مقالات تجريبية إضافية لاختبار التحميل
    $additional_articles = [
        'أخبار تقنية: تطورات جديدة في عالم الذكاء الاصطناعي',
        'رياضة: نتائج مثيرة في البطولات المحلية والعالمية',
        'اقتصاد: تحليل الأسواق المالية والتطورات الاقتصادية',
        'صحة: نصائح طبية مهمة للحفاظ على الصحة العامة',
        'تعليم: مبادرات جديدة لتطوير التعليم الرقمي',
        'بيئة: جهود عالمية لمكافحة التغير المناخي',
        'ثقافة: فعاليات ثقافية متنوعة تعزز التراث',
        'علوم: اكتشافات علمية جديدة تفتح آفاق المستقبل',
        'سياسة: تطورات سياسية مهمة على الساحة المحلية',
        'مجتمع: قصص إنسانية ملهمة من المجتمع المحلي'
    ];
    
    foreach ($additional_articles as $index => $title) {
        $slug = createSlug($title);
        $content = "هذا مقال تجريبي لاختبار تحسينات الصفحة الرئيسية. يحتوي على محتوى تجريبي لعرض كيفية عمل النظام مع التحميل التدريجي والأنيميشن المتقدم.";
        $excerpt = "مقال تجريبي لاختبار التحسينات الجديدة";
        $is_featured = $index < 3 ? 1 : 0;
        
        $stmt = $db->prepare("
            INSERT IGNORE INTO articles (title, slug, content, excerpt, is_featured, published_at, author, views) 
            VALUES (?, ?, ?, ?, ?, NOW(), 'نظام الاختبار', ?)
        ");
        $stmt->execute([$title, $slug, $content, $excerpt, $is_featured, rand(100, 5000)]);
    }
}

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
            <i class="fas fa-magic ml-2 text-purple-600"></i>
            اختبار تحسينات الصفحة الرئيسية
        </h1>

        <!-- معلومات التحسينات -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-blue-800">
                <i class="fas fa-star ml-2"></i>التحسينات المضافة
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-mouse ml-2 text-blue-600"></i>Smooth Scrolling
                    </h3>
                    <p class="text-sm text-gray-600">تمرير سلس عند النقر على الروابط الداخلية</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-arrow-up ml-2 text-green-600"></i>Scroll to Top
                    </h3>
                    <p class="text-sm text-gray-600">زر العودة لأعلى يظهر عند التمرير</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-infinity ml-2 text-purple-600"></i>Infinite Scroll
                    </h3>
                    <p class="text-sm text-gray-600">تحميل المزيد من المقالات تلقائياً</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-magic ml-2 text-yellow-600"></i>Scroll Animations
                    </h3>
                    <p class="text-sm text-gray-600">تأثيرات حركية عند التمرير</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-thumbtack ml-2 text-red-600"></i>Sticky Navigation
                    </h3>
                    <p class="text-sm text-gray-600">شريط تنقل مثبت عند التمرير</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        <i class="fas fa-chart-line ml-2 text-indigo-600"></i>Progress Bar
                    </h3>
                    <p class="text-sm text-gray-600">شريط تقدم يوضح مدى التمرير</p>
                </div>
            </div>
        </div>

        <!-- اختبارات تفاعلية -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-green-800">
                <i class="fas fa-vial ml-2"></i>اختبارات تفاعلية
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-3">اختبار التمرير السلس</h3>
                    <div class="space-y-2">
                        <a href="#test-section-1" class="block bg-blue-600 text-white px-4 py-2 rounded text-center hover:bg-blue-700 transition-colors">
                            الانتقال للقسم الأول
                        </a>
                        <a href="#test-section-2" class="block bg-green-600 text-white px-4 py-2 rounded text-center hover:bg-green-700 transition-colors">
                            الانتقال للقسم الثاني
                        </a>
                        <a href="#test-section-3" class="block bg-purple-600 text-white px-4 py-2 rounded text-center hover:bg-purple-700 transition-colors">
                            الانتقال للقسم الثالث
                        </a>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-3">اختبار الوظائف</h3>
                    <div class="space-y-2">
                        <button onclick="testScrollToTop()" class="w-full bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors">
                            اختبار زر العودة لأعلى
                        </button>
                        <button onclick="testProgressBar()" class="w-full bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition-colors">
                            اختبار شريط التقدم
                        </button>
                        <button onclick="testStickyNav()" class="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors">
                            اختبار التنقل المثبت
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-yellow-800">
                <i class="fas fa-chart-bar ml-2"></i>إحصائيات المحتوى
            </h2>
            
            <?php
            $total_articles = $db->query("SELECT COUNT(*) FROM articles")->fetchColumn();
            $featured_articles = $db->query("SELECT COUNT(*) FROM articles WHERE is_featured = 1")->fetchColumn();
            $categories_count = $db->query("SELECT COUNT(*) FROM categories")->fetchColumn();
            ?>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-blue-600"><?php echo $total_articles; ?></div>
                    <div class="text-sm text-gray-600">إجمالي المقالات</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-green-600"><?php echo $featured_articles; ?></div>
                    <div class="text-sm text-gray-600">مقالات مميزة</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-purple-600"><?php echo $categories_count; ?></div>
                    <div class="text-sm text-gray-600">التصنيفات</div>
                </div>
            </div>
        </div>

        <!-- أقسام الاختبار -->
        <div class="space-y-12">
            <section id="test-section-1" class="bg-blue-50 border border-blue-200 rounded-lg p-8">
                <h2 class="text-2xl font-bold text-blue-800 mb-4">القسم الأول - اختبار الأنيميشن</h2>
                <p class="text-gray-700 mb-4">
                    هذا القسم مخصص لاختبار تأثيرات الأنيميشن عند التمرير. عندما تصل إلى هذا القسم، 
                    يجب أن تلاحظ تأثيرات حركية سلسة.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded-lg shadow fade-in">
                        <h3 class="font-semibold mb-2">Fade In Effect</h3>
                        <p class="text-sm text-gray-600">هذا العنصر يظهر بتأثير fade in</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow slide-up">
                        <h3 class="font-semibold mb-2">Slide Up Effect</h3>
                        <p class="text-sm text-gray-600">هذا العنصر يظهر بتأثير slide up</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow scale-in">
                        <h3 class="font-semibold mb-2">Scale In Effect</h3>
                        <p class="text-sm text-gray-600">هذا العنصر يظهر بتأثير scale in</p>
                    </div>
                </div>
            </section>

            <section id="test-section-2" class="bg-green-50 border border-green-200 rounded-lg p-8">
                <h2 class="text-2xl font-bold text-green-800 mb-4">القسم الثاني - اختبار التحميل</h2>
                <p class="text-gray-700 mb-4">
                    هذا القسم يحتوي على محتوى إضافي لاختبار وظيفة التحميل التدريجي. 
                    عند الوصول لنهاية الصفحة، سيتم تحميل المزيد من المحتوى تلقائياً.
                </p>
                <div class="space-y-4">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                    <div class="bg-white p-4 rounded-lg shadow slide-left">
                        <h3 class="font-semibold mb-2">عنصر تجريبي <?php echo $i; ?></h3>
                        <p class="text-sm text-gray-600">
                            هذا محتوى تجريبي لاختبار الأنيميشن والتحميل. يحتوي على نص وهمي 
                            لإظهار كيفية عمل التأثيرات المختلفة عند التمرير.
                        </p>
                    </div>
                    <?php endfor; ?>
                </div>
            </section>

            <section id="test-section-3" class="bg-purple-50 border border-purple-200 rounded-lg p-8">
                <h2 class="text-2xl font-bold text-purple-800 mb-4">القسم الثالث - اختبار الأداء</h2>
                <p class="text-gray-700 mb-4">
                    هذا القسم الأخير يحتوي على عناصر متعددة لاختبار أداء الأنيميشن 
                    والتأكد من عدم تأثيرها على سرعة الموقع.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <?php for ($i = 1; $i <= 8; $i++): ?>
                    <div class="bg-white p-4 rounded-lg shadow stagger-animation">
                        <div class="w-full h-20 bg-gradient-to-br from-purple-400 to-pink-400 rounded mb-3"></div>
                        <h4 class="font-semibold text-sm mb-1">عنصر <?php echo $i; ?></h4>
                        <p class="text-xs text-gray-600">محتوى تجريبي قصير</p>
                    </div>
                    <?php endfor; ?>
                </div>
            </section>
        </div>

        <!-- روابط سريعة -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-link ml-2"></i>روابط سريعة
            </h2>
            
            <div class="flex flex-wrap gap-4">
                <a href="index.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-home ml-2"></i>الصفحة الرئيسية
                </a>
                
                <a href="test-breaking-news.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-broadcast-tower ml-2"></i>اختبار شريط الأخبار
                </a>
                
                <a href="test-complete.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-vial ml-2"></i>اختبار شامل
                </a>
                
                <a href="admin/dashboard.php" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-tachometer-alt ml-2"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function testScrollToTop() {
    // التمرير لأسفل أولاً
    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    
    setTimeout(() => {
        alert('الآن اضغط على زر العودة لأعلى الذي ظهر في الزاوية السفلى');
    }, 2000);
}

function testProgressBar() {
    const progressBar = document.getElementById('scroll-progress');
    if (progressBar) {
        alert('شريط التقدم يعمل! لاحظ الشريط الأزرق في أعلى الصفحة يتحرك مع التمرير');
    } else {
        alert('شريط التقدم غير متاح في هذه الصفحة. جرب في الصفحة الرئيسية');
    }
}

function testStickyNav() {
    window.scrollTo({ top: 500, behavior: 'smooth' });
    
    setTimeout(() => {
        alert('لاحظ شريط التنقل المثبت في أعلى الصفحة عند التمرير');
    }, 1000);
}

// إضافة تأثيرات للعناصر في هذه الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة classes للأنيميشن
    const sections = document.querySelectorAll('section');
    sections.forEach((section, index) => {
        section.style.transitionDelay = `${index * 0.2}s`;
    });
    
    // مراقبة العناصر للأنيميشن
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, { threshold: 0.1 });
    
    // مراقبة جميع العناصر المتحركة
    const animatedElements = document.querySelectorAll('.fade-in, .slide-up, .slide-left, .scale-in, .stagger-animation');
    animatedElements.forEach(element => {
        observer.observe(element);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
