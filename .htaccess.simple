# Simple .htaccess for testing - موقع الأخبار

# Enable rewrite engine
RewriteEngine On

# Prevent directory browsing
Options -Indexes

# Protect sensitive files only
<Files "database.php">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

# Basic security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options SAMEORIGIN
    Header set X-XSS-Protection "1; mode=block"
</IfModule>

# PHP settings (if allowed)
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value memory_limit 256M
</IfModule>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/css text/javascript application/javascript
</IfModule>
