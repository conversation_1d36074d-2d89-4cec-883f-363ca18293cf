<?php
session_start();
require_once 'includes/functions.php';

// الحصول على كلمة البحث
$search_query = isset($_GET['q']) ? sanitizeInput($_GET['q']) : '';

// إعداد الصفحة
$page_title = $search_query ? "نتائج البحث عن: $search_query" : 'البحث';
$page_description = 'البحث في الأخبار والمقالات';

// الحصول على رقم الصفحة
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = (int)getSetting('articles_per_page', 12);

$articles = [];
$total_articles = 0;
$total_pages = 0;

if (!empty($search_query)) {
    // الحصول على نتائج البحث
    $articles = getArticles($page, $per_page, null, $search_query);
    $total_articles = getTotalArticles(null, $search_query);
    $total_pages = ceil($total_articles / $per_page);
}

include 'includes/header.php';
?>

<main class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-8">
        <ol class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
            <li><a href="index.php" class="hover:text-blue-600">الرئيسية</a></li>
            <li><i class="fas fa-chevron-left mx-2"></i></li>
            <li class="text-gray-700">البحث</li>
        </ol>
    </nav>

    <!-- Search Header -->
    <header class="mb-8">
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8">
            <h1 class="text-3xl md:text-4xl font-bold mb-4">
                <i class="fas fa-search ml-3"></i>
                البحث في الأخبار
            </h1>
            <p class="text-blue-100 text-lg mb-6">ابحث عن الأخبار والمقالات التي تهمك</p>
            
            <!-- Search Form -->
            <form action="search.php" method="GET" class="max-w-2xl">
                <div class="flex">
                    <input type="text" 
                           name="q" 
                           value="<?php echo htmlspecialchars($search_query); ?>"
                           placeholder="أدخل كلمة البحث..." 
                           class="flex-1 px-4 py-3 rounded-r-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-300"
                           required>
                    <button type="submit" 
                            class="px-6 py-3 bg-white text-blue-600 rounded-l-lg hover:bg-gray-100 transition-colors font-medium">
                        <i class="fas fa-search ml-1"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-3">
            <?php if (!empty($search_query)): ?>
                <!-- Search Results Header -->
                <div class="mb-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-2">
                        نتائج البحث عن: "<span class="text-blue-600"><?php echo htmlspecialchars($search_query); ?></span>"
                    </h2>
                    <p class="text-gray-600">
                        تم العثور على <?php echo number_format($total_articles); ?> نتيجة
                    </p>
                </div>

                <?php if (!empty($articles)): ?>
                    <!-- Search Results -->
                    <div class="space-y-6 mb-8">
                        <?php foreach ($articles as $article): ?>
                        <article class="bg-white rounded-lg shadow-md overflow-hidden news-card">
                            <div class="md:flex">
                                <div class="md:w-48 md:flex-shrink-0">
                                    <?php if ($article['image_url']): ?>
                                        <img src="<?php echo $article['image_url']; ?>" 
                                             alt="<?php echo htmlspecialchars($article['title']); ?>"
                                             class="w-full h-48 md:h-full object-cover">
                                    <?php else: ?>
                                        <div class="w-full h-48 md:h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                            <i class="fas fa-newspaper text-4xl text-gray-400"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="p-6 flex-1">
                                    <div class="flex items-center justify-between mb-2">
                                        <?php if ($article['category_name']): ?>
                                            <a href="category.php?slug=<?php echo $article['category_slug']; ?>" 
                                               class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium hover:bg-blue-200 transition-colors">
                                                <?php echo $article['category_name']; ?>
                                            </a>
                                        <?php endif; ?>
                                        <span class="text-xs text-gray-500">
                                            <i class="fas fa-clock ml-1"></i>
                                            <?php echo formatArabicDate($article['published_at']); ?>
                                        </span>
                                    </div>
                                    <h3 class="font-bold text-xl mb-3 leading-tight">
                                        <a href="article.php?slug=<?php echo $article['slug']; ?>" 
                                           class="hover:text-blue-600 transition-colors">
                                            <?php 
                                            // تمييز كلمة البحث في العنوان
                                            $highlighted_title = str_ireplace($search_query, "<mark class='bg-yellow-200'>$search_query</mark>", $article['title']);
                                            echo $highlighted_title;
                                            ?>
                                        </a>
                                    </h3>
                                    <p class="text-gray-600 mb-4 leading-relaxed">
                                        <?php 
                                        // تمييز كلمة البحث في المقتطف
                                        $highlighted_excerpt = str_ireplace($search_query, "<mark class='bg-yellow-200'>$search_query</mark>", $article['excerpt']);
                                        echo $highlighted_excerpt;
                                        ?>
                                    </p>
                                    <div class="flex items-center justify-between text-sm text-gray-500">
                                        <span>
                                            <i class="fas fa-eye ml-1"></i>
                                            <?php echo number_format($article['views']); ?> مشاهدة
                                        </span>
                                        <?php if ($article['author']): ?>
                                        <span>
                                            <i class="fas fa-user ml-1"></i>
                                            <?php echo $article['author']; ?>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </article>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination -->
                    <?php echo generatePagination($page, $total_pages, "search.php?q=" . urlencode($search_query)); ?>
                    
                <?php else: ?>
                    <div class="text-center py-16">
                        <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">لم يتم العثور على نتائج</h3>
                        <p class="text-gray-500 mb-8">جرب استخدام كلمات مختلفة أو أكثر عمومية</p>
                        
                        <!-- Search Suggestions -->
                        <div class="bg-gray-50 rounded-lg p-6 max-w-md mx-auto">
                            <h4 class="font-semibold text-gray-800 mb-3">نصائح للبحث:</h4>
                            <ul class="text-sm text-gray-600 space-y-1 text-right">
                                <li>• تأكد من صحة الإملاء</li>
                                <li>• استخدم كلمات أكثر عمومية</li>
                                <li>• جرب كلمات مرادفة</li>
                                <li>• قلل من عدد الكلمات</li>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <!-- No Search Query -->
                <div class="text-center py-16">
                    <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">ابدأ البحث</h3>
                    <p class="text-gray-500 mb-8">أدخل كلمة البحث في الحقل أعلاه للعثور على الأخبار</p>
                    
                    <!-- Popular Categories -->
                    <div class="max-w-md mx-auto">
                        <h4 class="font-semibold text-gray-800 mb-4">تصنيفات شائعة:</h4>
                        <div class="flex flex-wrap gap-2 justify-center">
                            <?php foreach (getCategories() as $category): ?>
                            <a href="category.php?slug=<?php echo $category['slug']; ?>" 
                               class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm hover:bg-blue-200 transition-colors">
                                <?php echo $category['name']; ?>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Popular Searches -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-fire ml-2 text-red-500"></i>
                    عمليات بحث شائعة
                </h3>
                <div class="space-y-2">
                    <a href="search.php?q=تقنية" class="block text-gray-700 hover:text-blue-600 transition-colors">تقنية</a>
                    <a href="search.php?q=رياضة" class="block text-gray-700 hover:text-blue-600 transition-colors">رياضة</a>
                    <a href="search.php?q=اقتصاد" class="block text-gray-700 hover:text-blue-600 transition-colors">اقتصاد</a>
                    <a href="search.php?q=سياسة" class="block text-gray-700 hover:text-blue-600 transition-colors">سياسة</a>
                    <a href="search.php?q=صحة" class="block text-gray-700 hover:text-blue-600 transition-colors">صحة</a>
                </div>
            </div>

            <!-- Latest News -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-clock ml-2 text-blue-600"></i>
                    أحدث الأخبار
                </h3>
                <div class="space-y-4">
                    <?php 
                    $latest_articles = getLatestArticles(6);
                    foreach ($latest_articles as $latest): 
                    ?>
                    <article class="flex space-x-3 space-x-reverse">
                        <div class="w-16 h-12 flex-shrink-0">
                            <?php if ($latest['image_url']): ?>
                                <img src="<?php echo $latest['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($latest['title']); ?>"
                                     class="w-full h-full object-cover rounded">
                            <?php else: ?>
                                <div class="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sm leading-tight mb-1">
                                <a href="article.php?slug=<?php echo $latest['slug']; ?>" 
                                   class="hover:text-blue-600 transition-colors">
                                    <?php echo mb_substr($latest['title'], 0, 60) . (mb_strlen($latest['title']) > 60 ? '...' : ''); ?>
                                </a>
                            </h4>
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-clock ml-1"></i>
                                <?php echo formatDate($latest['published_at'], 'H:i'); ?>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Categories -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-list ml-2 text-blue-600"></i>
                    التصنيفات
                </h3>
                <div class="space-y-2">
                    <?php foreach (getCategories() as $category): ?>
                    <a href="category.php?slug=<?php echo $category['slug']; ?>" 
                       class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-700"><?php echo $category['name']; ?></span>
                        <i class="fas fa-chevron-left text-gray-400"></i>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<?php include 'includes/footer.php'; ?>
