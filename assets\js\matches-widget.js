/**
 * Football Matches Widget JavaScript
 * سكريبت جدول المباريات
 */

class MatchesWidget {
    constructor() {
        this.slider = null;
        this.sliderContainer = null;
        this.prevBtn = null;
        this.nextBtn = null;
        this.dots = [];
        this.currentSlide = 0;
        this.totalSlides = 0;
        this.autoPlayInterval = null;
        this.isAutoPlaying = true;
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.isDragging = false;
        this.isInitialized = false;
        
        // Responsive breakpoints
        this.breakpoints = {
            mobile: 768,
            tablet: 1024
        };
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeWidget());
        } else {
            this.initializeWidget();
        }
    }
    
    initializeWidget() {
        try {
            this.findElements();
            
            if (!this.validateElements()) {
                console.warn('Matches widget elements not found or incomplete');
                return;
            }
            
            this.setupSlider();
            this.bindEvents();
            this.setupAccessibility();
            this.startAutoPlay();
            this.handleResize();
            
            this.isInitialized = true;
            console.log('Football matches widget initialized successfully');
            
        } catch (error) {
            console.error('Error initializing matches widget:', error);
            this.showError('حدث خطأ في تحميل جدول المباريات');
        }
    }
    
    findElements() {
        this.slider = document.getElementById('matchesSlider');
        this.sliderContainer = document.getElementById('sliderContainer');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.dots = document.querySelectorAll('.slider-dot');
        
        // Get total slides from data attribute or calculate
        if (this.sliderContainer) {
            const slides = this.sliderContainer.querySelectorAll('.matches-slide');
            this.totalSlides = slides.length;
        }
    }
    
    validateElements() {
        return this.slider && 
               this.sliderContainer && 
               this.prevBtn && 
               this.nextBtn && 
               this.totalSlides > 0;
    }
    
    setupSlider() {
        // Set initial position
        this.updateSlider();
        
        // Set initial button states
        this.updateNavigationButtons();
        
        // Set initial dot states
        this.updateDots();
    }
    
    bindEvents() {
        // Navigation buttons
        this.prevBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.prevSlide();
        });
        
        this.nextBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.nextSlide();
        });
        
        // Dots navigation
        this.dots.forEach((dot, index) => {
            dot.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToSlide(index);
            });
        });
        
        // Touch/swipe support
        this.bindTouchEvents();
        
        // Mouse drag support for desktop
        this.bindMouseEvents();
        
        // Keyboard navigation
        this.bindKeyboardEvents();
        
        // Pause on hover
        this.bindHoverEvents();
        
        // Window resize
        window.addEventListener('resize', () => this.handleResize());
        
        // Visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
    }
    
    bindTouchEvents() {
        this.slider.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
        this.slider.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: true });
        this.slider.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });
    }
    
    bindMouseEvents() {
        this.slider.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.slider.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.slider.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.slider.addEventListener('mouseleave', (e) => this.handleMouseUp(e));
    }
    
    bindKeyboardEvents() {
        this.slider.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.nextSlide(); // RTL: left arrow goes to next
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.prevSlide(); // RTL: right arrow goes to previous
                    break;
                case 'Home':
                    e.preventDefault();
                    this.goToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    this.goToSlide(this.totalSlides - 1);
                    break;
            }
        });
    }
    
    bindHoverEvents() {
        this.slider.addEventListener('mouseenter', () => this.pauseAutoPlay());
        this.slider.addEventListener('mouseleave', () => this.resumeAutoPlay());
    }
    
    setupAccessibility() {
        // Set ARIA attributes
        this.slider.setAttribute('role', 'region');
        this.slider.setAttribute('aria-label', 'عارض شرائح المباريات');
        this.slider.setAttribute('tabindex', '0');
        
        // Navigation buttons
        this.prevBtn.setAttribute('aria-label', 'المباراة السابقة');
        this.nextBtn.setAttribute('aria-label', 'المباراة التالية');
        
        // Dots
        this.dots.forEach((dot, index) => {
            dot.setAttribute('aria-label', `الذهاب إلى الشريحة ${index + 1}`);
            dot.setAttribute('role', 'button');
            dot.setAttribute('tabindex', '0');
        });
        
        // Slides
        const slides = this.sliderContainer.querySelectorAll('.matches-slide');
        slides.forEach((slide, index) => {
            slide.setAttribute('aria-hidden', index !== this.currentSlide);
        });
    }
    
    updateSlider() {
        if (!this.sliderContainer) return;
        
        const translateX = -this.currentSlide * 100;
        this.sliderContainer.style.transform = `translateX(${translateX}%)`;
        
        // Update ARIA attributes
        const slides = this.sliderContainer.querySelectorAll('.matches-slide');
        slides.forEach((slide, index) => {
            slide.setAttribute('aria-hidden', index !== this.currentSlide);
        });
    }
    
    updateNavigationButtons() {
        if (!this.prevBtn || !this.nextBtn) return;
        
        this.prevBtn.disabled = this.currentSlide === 0;
        this.nextBtn.disabled = this.currentSlide === this.totalSlides - 1;
        
        // Update ARIA attributes
        this.prevBtn.setAttribute('aria-disabled', this.prevBtn.disabled);
        this.nextBtn.setAttribute('aria-disabled', this.nextBtn.disabled);
    }
    
    updateDots() {
        this.dots.forEach((dot, index) => {
            const isActive = index === this.currentSlide;
            dot.classList.toggle('active', isActive);
            dot.setAttribute('aria-pressed', isActive);
        });
    }
    
    prevSlide() {
        if (this.currentSlide > 0) {
            this.currentSlide--;
            this.updateSlider();
            this.updateNavigationButtons();
            this.updateDots();
            this.resetAutoPlay();
        }
    }
    
    nextSlide() {
        if (this.currentSlide < this.totalSlides - 1) {
            this.currentSlide++;
            this.updateSlider();
            this.updateNavigationButtons();
            this.updateDots();
            this.resetAutoPlay();
        }
    }
    
    goToSlide(index) {
        if (index >= 0 && index < this.totalSlides && index !== this.currentSlide) {
            this.currentSlide = index;
            this.updateSlider();
            this.updateNavigationButtons();
            this.updateDots();
            this.resetAutoPlay();
        }
    }
    
    startAutoPlay() {
        if (this.totalSlides <= 1) return;
        
        this.autoPlayInterval = setInterval(() => {
            if (this.isAutoPlaying) {
                if (this.currentSlide < this.totalSlides - 1) {
                    this.nextSlide();
                } else {
                    this.goToSlide(0); // Loop back to first slide
                }
            }
        }, 5000); // 5 seconds
    }
    
    pauseAutoPlay() {
        this.isAutoPlaying = false;
    }
    
    resumeAutoPlay() {
        this.isAutoPlaying = true;
    }
    
    resetAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
        }
        this.startAutoPlay();
    }
    
    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
        this.isAutoPlaying = false;
    }
    
    handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
        this.pauseAutoPlay();
    }
    
    handleTouchMove(e) {
        if (!this.touchStartX) return;
        this.touchEndX = e.touches[0].clientX;
    }
    
    handleTouchEnd(e) {
        if (!this.touchStartX || !this.touchEndX) {
            this.resumeAutoPlay();
            return;
        }
        
        const diffX = this.touchStartX - this.touchEndX;
        const threshold = 50;
        
        if (Math.abs(diffX) > threshold) {
            if (diffX > 0) {
                // Swipe left (next slide in RTL)
                this.nextSlide();
            } else {
                // Swipe right (previous slide in RTL)
                this.prevSlide();
            }
        }
        
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.resumeAutoPlay();
    }
    
    handleMouseDown(e) {
        e.preventDefault();
        this.isDragging = true;
        this.touchStartX = e.clientX;
        this.pauseAutoPlay();
        this.slider.style.cursor = 'grabbing';
    }
    
    handleMouseMove(e) {
        if (!this.isDragging) return;
        e.preventDefault();
        this.touchEndX = e.clientX;
    }
    
    handleMouseUp(e) {
        if (!this.isDragging) return;
        
        this.isDragging = false;
        this.slider.style.cursor = 'grab';
        
        if (this.touchStartX && this.touchEndX) {
            const diffX = this.touchStartX - this.touchEndX;
            const threshold = 50;
            
            if (Math.abs(diffX) > threshold) {
                if (diffX > 0) {
                    this.nextSlide();
                } else {
                    this.prevSlide();
                }
            }
        }
        
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.resumeAutoPlay();
    }
    
    handleResize() {
        // Recalculate slides if needed based on responsive breakpoints
        const width = window.innerWidth;
        
        // Update slides per view based on screen size
        if (width <= this.breakpoints.mobile) {
            // Mobile: 1 slide per view
            this.updateSlidesPerView(1);
        } else if (width <= this.breakpoints.tablet) {
            // Tablet: 2 slides per view
            this.updateSlidesPerView(2);
        } else {
            // Desktop: 3 slides per view
            this.updateSlidesPerView(3);
        }
    }
    
    updateSlidesPerView(slidesPerView) {
        // This would require restructuring the slides
        // For now, we'll just ensure the current slide is still valid
        if (this.currentSlide >= this.totalSlides) {
            this.currentSlide = this.totalSlides - 1;
            this.updateSlider();
            this.updateNavigationButtons();
            this.updateDots();
        }
    }
    
    handleVisibilityChange() {
        if (document.hidden) {
            this.pauseAutoPlay();
        } else {
            this.resumeAutoPlay();
        }
    }
    
    showError(message) {
        if (!this.slider) return;
        
        this.slider.innerHTML = `
            <div class="matches-error">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>${message}</h3>
                <p>يرجى المحاولة مرة أخرى لاحقاً</p>
            </div>
        `;
        this.slider.classList.add('error');
    }
    
    destroy() {
        this.stopAutoPlay();
        
        // Remove event listeners
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        
        // Clear references
        this.slider = null;
        this.sliderContainer = null;
        this.prevBtn = null;
        this.nextBtn = null;
        this.dots = [];
        
        this.isInitialized = false;
    }
}

// Initialize the widget when the script loads
let matchesWidget = null;

// Auto-initialize if elements exist
document.addEventListener('DOMContentLoaded', function() {
    const matchesSlider = document.getElementById('matchesSlider');
    if (matchesSlider) {
        matchesWidget = new MatchesWidget();
    }
});

// Export for manual initialization if needed
window.MatchesWidget = MatchesWidget;
