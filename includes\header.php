<?php
/**
 * Header Template - محسن ومنظم
 * يحتوي فقط على عناصر الهيدر بدون HTML الأساسي
 */

// التأكد من تحميل الدوال المطلوبة
if (!function_exists('getSetting')) {
    require_once 'functions.php';
}

// الحصول على التصنيفات للتنقل
try {
    $categories = getCategories();
} catch (Exception $e) {
    $categories = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo getSetting('site_name', 'موقع الأخبار'); ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : getSetting('site_description', 'موقع إخباري شامل'); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Navigation Enhancements CSS -->
    <link rel="stylesheet" href="assets/css/navigation-enhancements.css">
    
    <!-- Homepage Enhancements CSS -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'index.php'): ?>
    <link rel="stylesheet" href="assets/css/homepage-enhancements.css">
    <link rel="stylesheet" href="assets/css/matches-widget.css">
    <?php endif; ?>
    
    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
        }
        
        .sticky-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .sticky-header.scrolled {
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="sticky-header sticky top-0 z-50 shadow-lg">
        <div class="container mx-auto px-4">
            <!-- Top Bar -->
            <div class="border-b border-gray-200 py-2">
                <div class="flex justify-between items-center text-sm text-gray-600">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <span class="flex items-center">
                            <i class="fas fa-calendar-alt ml-1"></i>
                            <?php echo date('Y/m/d'); ?>
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-clock ml-1"></i>
                            <span id="current-time"><?php echo date('H:i'); ?></span>
                        </span>
                    </div>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <!-- Social Media Links -->
                        <a href="#" class="text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-blue-400 hover:text-blue-600 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-red-600 hover:text-red-800 transition-colors">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="text-pink-600 hover:text-pink-800 transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Header -->
            <div class="py-4">
                <div class="flex justify-between items-center">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="index.php" class="flex items-center">
                            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3 rounded-lg ml-3">
                                <i class="fas fa-newspaper text-2xl"></i>
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-800">
                                    <?php echo getSetting('site_name', 'موقع الأخبار'); ?>
                                </h1>
                                <p class="text-sm text-gray-600">
                                    <?php echo getSetting('site_description', 'موقع إخباري شامل'); ?>
                                </p>
                            </div>
                        </a>
                    </div>
                    
                    <!-- Search Box -->
                    <div class="hidden md:flex flex-1 max-w-md mx-8">
                        <form action="search.php" method="GET" class="w-full">
                            <div class="relative">
                                <input type="text" 
                                       name="q" 
                                       placeholder="البحث في الأخبار..." 
                                       class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button type="submit" 
                                        class="absolute left-0 top-0 h-full px-3 text-gray-400 hover:text-blue-600 transition-colors">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button class="mobile-nav-toggle md:hidden text-gray-600 hover:text-gray-800">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="main-navigation border-t border-gray-200">
                <div class="py-3">
                    <ul class="nav-menu hidden md:flex space-x-8 space-x-reverse">
                        <li class="nav-item">
                            <a href="index.php" class="nav-link">
                                <i class="fas fa-home ml-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="matches.php" class="nav-link">
                                <i class="fas fa-futbol ml-1"></i>مواعيد المباريات
                            </a>
                        </li>
                        <?php if (!empty($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 5) as $category): ?>
                            <li class="nav-item">
                                <a href="category.php?slug=<?php echo $category['slug']; ?>" class="nav-link">
                                    <i class="fas fa-tag ml-1"></i><?php echo $category['name']; ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a href="search.php" class="nav-link">
                                <i class="fas fa-search ml-1"></i>البحث
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content Container -->
    <main class="min-h-screen">
