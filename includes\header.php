<?php
/**
 * Header Template - محسن ومنظم
 * يحتوي فقط على عناصر الهيدر بدون HTML الأساسي
 */

// التأكد من تحميل الدوال المطلوبة
if (!function_exists('getSetting')) {
    require_once 'functions.php';
}

// الحصول على التصنيفات للتنقل
try {
    $categories = getCategories();
} catch (Exception $e) {
    $categories = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo getSetting('site_name', 'موقع الأخبار'); ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : getSetting('site_description', 'موقع إخباري شامل'); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Tailwind CSS - Removed for better performance and consistency -->
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Unified Design System CSS -->
    <link rel="stylesheet" href="assets/css/unified-design-system.css">

    <!-- Design Quality Fixes -->
    <link rel="stylesheet" href="assets/css/urgent-fixes.css">
    <link rel="stylesheet" href="assets/css/accessibility-fixes.css">

    <!-- Page Specific CSS -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'index.php'): ?>
    <link rel="stylesheet" href="assets/css/homepage-enhancements.css">
    <link rel="stylesheet" href="assets/css/matches-widget.css">
    <?php endif; ?>
</head>
<body class="page-wrapper">
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">تخطي إلى المحتوى الرئيسي</a>

    <!-- Header -->
    <header class="site-header" role="banner">
        <div class="container">
            <!-- Top Bar -->
            <div class="header-top">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <span class="flex items-center">
                            <i class="fas fa-calendar-alt ml-1"></i>
                            <?php echo date('Y/m/d'); ?>
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-clock ml-1"></i>
                            <span id="current-time"><?php echo date('H:i'); ?></span>
                        </span>
                    </div>
                    
                    <div class="header-social-links">
                        <!-- Social Media Links -->
                        <a href="#" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link youtube">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="social-link instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Header -->
            <div class="header-main">
                <div class="header-container">
                    <!-- Logo -->
                    <div class="header-brand">
                        <a href="index.php" class="site-logo">
                            <div class="logo-icon">
                                <i class="fas fa-newspaper"></i>
                            </div>
                            <div class="logo-text">
                                <h1 class="site-title">
                                    <?php echo getSetting('site_name', 'موقع الأخبار'); ?>
                                </h1>
                                <p class="site-description">
                                    <?php echo getSetting('site_description', 'موقع إخباري شامل'); ?>
                                </p>
                            </div>
                        </a>
                    </div>
                    
                    <!-- Search Box -->
                    <div class="hidden md:flex flex-1 max-w-md mx-8">
                        <form action="search.php" method="GET" class="w-full">
                            <div class="relative">
                                <input type="text" 
                                       name="q" 
                                       placeholder="البحث في الأخبار..." 
                                       class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button type="submit" 
                                        class="absolute left-0 top-0 h-full px-3 text-gray-400 hover:text-blue-600 transition-colors">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button class="mobile-nav-toggle md:hidden text-gray-600 hover:text-gray-800">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="main-navigation border-t border-gray-200" aria-label="التنقل الرئيسي" role="navigation">
                <div class="py-3">
                    <ul class="nav-menu hidden md:flex space-x-8 space-x-reverse" role="menubar">
                        <li class="nav-item">
                            <a href="index.php" class="nav-link">
                                <i class="fas fa-home ml-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="matches.php" class="nav-link">
                                <i class="fas fa-futbol ml-1"></i>مواعيد المباريات
                            </a>
                        </li>
                        <?php if (!empty($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 5) as $category): ?>
                            <li class="nav-item">
                                <a href="category.php?slug=<?php echo $category['slug']; ?>" class="nav-link">
                                    <i class="fas fa-tag ml-1"></i><?php echo $category['name']; ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a href="search.php" class="nav-link">
                                <i class="fas fa-search ml-1"></i>البحث
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content Container -->
    <main id="main-content" class="min-h-screen" role="main">
