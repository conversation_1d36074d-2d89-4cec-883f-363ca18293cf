-- جدول المباريات
CREATE TABLE IF NOT EXISTS matches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    home_team VARCHAR(100) NOT NULL COMMENT 'الفريق المضيف',
    away_team VARCHAR(100) NOT NULL COMMENT 'الفريق الضيف',
    home_team_logo VARCHAR(255) DEFAULT NULL COMMENT 'شعار الفريق المضيف',
    away_team_logo VARCHAR(255) DEFAULT NULL COMMENT 'شعار الفريق الضيف',
    match_date DATETIME NOT NULL COMMENT 'تاريخ ووقت المباراة',
    competition VARCHAR(100) NOT NULL COMMENT 'البطولة أو المسابقة',
    venue VARCHAR(100) DEFAULT NULL COMMENT 'الملعب',
    status ENUM('scheduled', 'live', 'finished', 'postponed', 'cancelled') DEFAULT 'scheduled' COMMENT 'حالة المباراة',
    home_score INT DEFAULT NULL COMMENT 'نتيجة الفريق المضيف',
    away_score INT DEFAULT NULL COMMENT 'نتيجة الفريق الضيف',
    match_time VARCHAR(10) DEFAULT NULL COMMENT 'وقت المباراة (مثل: 90+2)',
    description TEXT DEFAULT NULL COMMENT 'وصف أو ملاحظات إضافية',
    is_featured BOOLEAN DEFAULT FALSE COMMENT 'مباراة مميزة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول البطولات
CREATE TABLE IF NOT EXISTS competitions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'اسم البطولة',
    slug VARCHAR(100) NOT NULL UNIQUE COMMENT 'الرابط المختصر',
    logo VARCHAR(255) DEFAULT NULL COMMENT 'شعار البطولة',
    country VARCHAR(50) DEFAULT NULL COMMENT 'البلد',
    season VARCHAR(20) DEFAULT NULL COMMENT 'الموسم',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشطة أم لا',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الفرق
CREATE TABLE IF NOT EXISTS teams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'اسم الفريق',
    slug VARCHAR(100) NOT NULL UNIQUE COMMENT 'الرابط المختصر',
    logo VARCHAR(255) DEFAULT NULL COMMENT 'شعار الفريق',
    country VARCHAR(50) DEFAULT NULL COMMENT 'البلد',
    founded_year INT DEFAULT NULL COMMENT 'سنة التأسيس',
    stadium VARCHAR(100) DEFAULT NULL COMMENT 'الملعب الرئيسي',
    description TEXT DEFAULT NULL COMMENT 'وصف الفريق',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط أم لا',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية للبطولات
INSERT INTO competitions (name, slug, country, season) VALUES
('الدوري السعودي للمحترفين', 'saudi-pro-league', 'السعودية', '2024-2025'),
('دوري أبطال آسيا', 'afc-champions-league', 'آسيا', '2024'),
('كأس الملك', 'kings-cup', 'السعودية', '2024'),
('الدوري المصري', 'egyptian-league', 'مصر', '2024-2025'),
('دوري أبطال أوروبا', 'uefa-champions-league', 'أوروبا', '2024-2025'),
('الدوري الإنجليزي الممتاز', 'premier-league', 'إنجلترا', '2024-2025');

-- إدراج بيانات تجريبية للفرق
INSERT INTO teams (name, slug, country, stadium) VALUES
('الهلال', 'al-hilal', 'السعودية', 'ملعب الملك فهد الدولي'),
('النصر', 'al-nassr', 'السعودية', 'ملعب مرسول بارك'),
('الأهلي السعودي', 'al-ahli-saudi', 'السعودية', 'ملعب الملك عبدالله الرياضي'),
('الاتحاد', 'al-ittihad', 'السعودية', 'ملعب الأمير عبدالله الفيصل'),
('الأهلي المصري', 'al-ahly-egypt', 'مصر', 'ستاد القاهرة الدولي'),
('الزمالك', 'zamalek', 'مصر', 'ستاد القاهرة الدولي'),
('ريال مدريد', 'real-madrid', 'إسبانيا', 'سانتياغو برنابيو'),
('برشلونة', 'barcelona', 'إسبانيا', 'كامب نو'),
('مانشستر سيتي', 'manchester-city', 'إنجلترا', 'ملعب الاتحاد'),
('ليفربول', 'liverpool', 'إنجلترا', 'أنفيلد');

-- إدراج مباريات تجريبية
INSERT INTO matches (home_team, away_team, match_date, competition, venue, status, home_score, away_score, is_featured) VALUES
-- مباريات منتهية
('الهلال', 'النصر', '2024-01-15 20:00:00', 'الدوري السعودي للمحترفين', 'ملعب الملك فهد الدولي', 'finished', 2, 1, TRUE),
('الأهلي السعودي', 'الاتحاد', '2024-01-16 18:00:00', 'الدوري السعودي للمحترفين', 'ملعب الملك عبدالله الرياضي', 'finished', 1, 0, FALSE),
('الأهلي المصري', 'الزمالك', '2024-01-17 19:00:00', 'الدوري المصري', 'ستاد القاهرة الدولي', 'finished', 3, 2, TRUE),
('ريال مدريد', 'برشلونة', '2024-01-18 21:00:00', 'دوري أبطال أوروبا', 'سانتياغو برنابيو', 'finished', 2, 0, TRUE),

-- مباريات قادمة
('النصر', 'الأهلي السعودي', DATE_ADD(NOW(), INTERVAL 2 DAY), 'الدوري السعودي للمحترفين', 'ملعب مرسول بارك', 'scheduled', NULL, NULL, TRUE),
('الهلال', 'الاتحاد', DATE_ADD(NOW(), INTERVAL 3 DAY), 'كأس الملك', 'ملعب الملك فهد الدولي', 'scheduled', NULL, NULL, FALSE),
('الزمالك', 'الأهلي المصري', DATE_ADD(NOW(), INTERVAL 5 DAY), 'الدوري المصري', 'ستاد القاهرة الدولي', 'scheduled', NULL, NULL, TRUE),
('مانشستر سيتي', 'ليفربول', DATE_ADD(NOW(), INTERVAL 7 DAY), 'الدوري الإنجليزي الممتاز', 'ملعب الاتحاد', 'scheduled', NULL, NULL, TRUE),
('برشلونة', 'ريال مدريد', DATE_ADD(NOW(), INTERVAL 10 DAY), 'دوري أبطال أوروبا', 'كامب نو', 'scheduled', NULL, NULL, TRUE),

-- مباراة جارية (للاختبار)
('الهلال', 'الأهلي السعودي', NOW(), 'الدوري السعودي للمحترفين', 'ملعب الملك فهد الدولي', 'live', 1, 1, TRUE);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_matches_date ON matches(match_date);
CREATE INDEX idx_matches_status ON matches(status);
CREATE INDEX idx_matches_competition ON matches(competition);
CREATE INDEX idx_matches_featured ON matches(is_featured);
CREATE INDEX idx_competitions_active ON competitions(is_active);
CREATE INDEX idx_teams_active ON teams(is_active);
