<?php
/**
 * إضافة مقالات تجريبية لاختبار شريط الأخبار العاجلة
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

session_start();

$page_title = 'إضافة مقالات تجريبية';
$database = new Database();
$db = $database->connect();

$success_message = '';
$error_message = '';

// التحقق من وجود مقالات
$stmt = $db->query("SELECT COUNT(*) FROM articles");
$existing_articles = $stmt->fetchColumn();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_samples'])) {
    try {
        // مقالات تجريبية للأخبار العاجلة
        $sample_articles = [
            [
                'title' => 'عاجل: تطورات مهمة في الأحداث الاقتصادية العالمية',
                'content' => 'شهدت الأسواق العالمية تطورات مهمة اليوم مع إعلان البنوك المركزية عن قرارات جديدة تؤثر على الاقتصاد العالمي. هذه التطورات تأتي في إطار الجهود المستمرة لتحقيق الاستقرار الاقتصادي.',
                'excerpt' => 'تطورات مهمة في الأسواق العالمية مع قرارات البنوك المركزية الجديدة',
                'is_featured' => 1,
                'author' => 'فريق التحرير الاقتصادي'
            ],
            [
                'title' => 'أخبار عاجلة: إنجازات تقنية جديدة في مجال الذكاء الاصطناعي',
                'content' => 'أعلنت شركات التقنية الرائدة عن اختراقات جديدة في مجال الذكاء الاصطناعي قد تغير مستقبل التكنولوجيا. هذه الإنجازات تفتح آفاقاً جديدة للتطبيقات العملية.',
                'excerpt' => 'اختراقات جديدة في الذكاء الاصطناعي تفتح آفاقاً مستقبلية',
                'is_featured' => 1,
                'author' => 'فريق التقنية'
            ],
            [
                'title' => 'تحديث فوري: أحداث رياضية مثيرة في البطولات العالمية',
                'content' => 'شهدت البطولات الرياضية العالمية أحداثاً مثيرة ونتائج مفاجئة غيرت من ترتيب المنافسات. الجماهير تتابع بحماس هذه التطورات الرياضية المهمة.',
                'excerpt' => 'نتائج مفاجئة في البطولات العالمية تثير حماس الجماهير',
                'is_featured' => 1,
                'author' => 'فريق الرياضة'
            ],
            [
                'title' => 'عاجل: تطورات علمية مهمة في مجال الطب والصحة',
                'content' => 'أعلن الباحثون عن اكتشافات طبية جديدة قد تساهم في علاج أمراض مستعصية. هذه الاكتشافات تمثل أملاً جديداً للمرضى حول العالم.',
                'excerpt' => 'اكتشافات طبية جديدة تفتح آمالاً في علاج الأمراض المستعصية',
                'is_featured' => 1,
                'author' => 'فريق الصحة'
            ],
            [
                'title' => 'أخبار عاجلة: تطورات بيئية مهمة في مكافحة التغير المناخي',
                'content' => 'اتخذت الحكومات العالمية خطوات جديدة لمكافحة التغير المناخي من خلال مبادرات بيئية طموحة. هذه الجهود تهدف إلى حماية كوكب الأرض للأجيال القادمة.',
                'excerpt' => 'مبادرات عالمية جديدة لمكافحة التغير المناخي وحماية البيئة',
                'is_featured' => 1,
                'author' => 'فريق البيئة'
            ],
            [
                'title' => 'تحديث عاجل: إنجازات فضائية جديدة في استكشاف الكون',
                'content' => 'حققت وكالات الفضاء العالمية إنجازات جديدة في مهام استكشاف الفضاء. هذه المهام تكشف أسرار جديدة عن الكون وتفتح آفاقاً للاستكشاف المستقبلي.',
                'excerpt' => 'إنجازات فضائية جديدة تكشف أسرار الكون وتفتح آفاق الاستكشاف',
                'is_featured' => 1,
                'author' => 'فريق العلوم'
            ],
            [
                'title' => 'أخبار عاجلة: تطورات تعليمية مهمة في التعلم الرقمي',
                'content' => 'شهد قطاع التعليم تطورات مهمة مع إدخال تقنيات جديدة للتعلم الرقمي. هذه التطورات تحسن من جودة التعليم وتوفر فرصاً أفضل للطلاب.',
                'excerpt' => 'تقنيات جديدة في التعلم الرقمي تحسن جودة التعليم',
                'is_featured' => 1,
                'author' => 'فريق التعليم'
            ],
            [
                'title' => 'عاجل: أحداث ثقافية مهمة تعزز التبادل الحضاري',
                'content' => 'انطلقت فعاليات ثقافية عالمية تهدف إلى تعزيز التبادل الحضاري بين الشعوب. هذه الفعاليات تبرز أهمية التنوع الثقافي والحوار بين الحضارات.',
                'excerpt' => 'فعاليات ثقافية عالمية تعزز التبادل الحضاري والحوار',
                'is_featured' => 1,
                'author' => 'فريق الثقافة'
            ]
        ];
        
        $added_count = 0;
        
        foreach ($sample_articles as $article) {
            $slug = createSlug($article['title']);
            
            // التحقق من عدم وجود مقال بنفس العنوان
            $stmt = $db->prepare("SELECT COUNT(*) FROM articles WHERE title = ?");
            $stmt->execute([$article['title']]);
            
            if ($stmt->fetchColumn() == 0) {
                $stmt = $db->prepare("
                    INSERT INTO articles (title, slug, content, excerpt, is_featured, published_at, author, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW(), ?, NOW())
                ");
                
                if ($stmt->execute([
                    $article['title'], 
                    $slug, 
                    $article['content'], 
                    $article['excerpt'], 
                    $article['is_featured'],
                    $article['author']
                ])) {
                    $added_count++;
                }
            }
        }
        
        if ($added_count > 0) {
            $success_message = "تم إضافة $added_count مقال تجريبي بنجاح!";
        } else {
            $error_message = 'جميع المقالات التجريبية موجودة بالفعل.';
        }
        
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء إضافة المقالات: ' . $e->getMessage();
    }
}

// إحصائيات حالية
$stmt = $db->query("SELECT COUNT(*) FROM articles WHERE is_featured = 1");
$featured_count = $stmt->fetchColumn();

$stmt = $db->query("SELECT COUNT(*) FROM articles");
$total_count = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>body { font-family: 'Tajawal', Arial, sans-serif; }</style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
                <i class="fas fa-newspaper ml-2 text-blue-600"></i>
                إضافة مقالات تجريبية لشريط الأخبار العاجلة
            </h1>

            <!-- Messages -->
            <?php if ($success_message): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle ml-2"></i><?php echo $success_message; ?>
            </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-triangle ml-2"></i><?php echo $error_message; ?>
            </div>
            <?php endif; ?>

            <!-- Current Statistics -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4 text-blue-800">
                    <i class="fas fa-chart-bar ml-2"></i>الإحصائيات الحالية
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600"><?php echo $total_count; ?></div>
                        <div class="text-sm text-gray-600">إجمالي المقالات</div>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600"><?php echo $featured_count; ?></div>
                        <div class="text-sm text-gray-600">مقالات مميزة</div>
                    </div>
                    
                    <div class="bg-white p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-purple-600"><?php echo max(0, 8 - $featured_count); ?></div>
                        <div class="text-sm text-gray-600">مقالات مطلوبة للشريط</div>
                    </div>
                </div>
            </div>

            <!-- Add Sample Articles -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4 text-yellow-800">
                    <i class="fas fa-plus-circle ml-2"></i>إضافة مقالات تجريبية
                </h2>
                
                <p class="text-gray-700 mb-4">
                    سيتم إضافة 8 مقالات تجريبية مميزة لاختبار شريط الأخبار العاجلة. هذه المقالات تغطي مواضيع متنوعة وستظهر في الشريط المتحرك.
                </p>
                
                <div class="bg-white p-4 rounded-lg mb-4">
                    <h3 class="font-semibold text-gray-800 mb-2">المقالات التي سيتم إضافتها:</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li><i class="fas fa-check text-green-600 ml-2"></i>تطورات اقتصادية عالمية</li>
                        <li><i class="fas fa-check text-green-600 ml-2"></i>إنجازات تقنية في الذكاء الاصطناعي</li>
                        <li><i class="fas fa-check text-green-600 ml-2"></i>أحداث رياضية مثيرة</li>
                        <li><i class="fas fa-check text-green-600 ml-2"></i>اكتشافات طبية جديدة</li>
                        <li><i class="fas fa-check text-green-600 ml-2"></i>مبادرات بيئية</li>
                        <li><i class="fas fa-check text-green-600 ml-2"></i>إنجازات فضائية</li>
                        <li><i class="fas fa-check text-green-600 ml-2"></i>تطورات تعليمية</li>
                        <li><i class="fas fa-check text-green-600 ml-2"></i>فعاليات ثقافية</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <button type="submit" name="add_samples" value="1" 
                            class="bg-yellow-600 text-white px-6 py-3 rounded-lg hover:bg-yellow-700 transition-colors">
                        <i class="fas fa-plus ml-2"></i>إضافة المقالات التجريبية
                    </button>
                </form>
            </div>

            <!-- Next Steps -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-800">
                    <i class="fas fa-arrow-right ml-2"></i>الخطوات التالية
                </h2>
                
                <div class="space-y-3">
                    <div class="flex items-center p-3 bg-white rounded-lg">
                        <i class="fas fa-eye text-blue-600 ml-3"></i>
                        <span>اذهب إلى <a href="index.php" class="text-blue-600 hover:underline">الصفحة الرئيسية</a> لرؤية شريط الأخبار العاجلة</span>
                    </div>
                    
                    <div class="flex items-center p-3 bg-white rounded-lg">
                        <i class="fas fa-vial text-green-600 ml-3"></i>
                        <span>اختبر الشريط في <a href="test-breaking-news.php" class="text-green-600 hover:underline">صفحة الاختبار</a></span>
                    </div>
                    
                    <div class="flex items-center p-3 bg-white rounded-lg">
                        <i class="fas fa-edit text-purple-600 ml-3"></i>
                        <span>أضف مقالات حقيقية من <a href="admin/articles.php" class="text-purple-600 hover:underline">لوحة التحكم</a></span>
                    </div>
                    
                    <div class="flex items-center p-3 bg-white rounded-lg">
                        <i class="fas fa-book text-orange-600 ml-3"></i>
                        <span>اقرأ <a href="BREAKING_NEWS_GUIDE.md" class="text-orange-600 hover:underline">دليل شريط الأخبار</a> للمزيد من المعلومات</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
