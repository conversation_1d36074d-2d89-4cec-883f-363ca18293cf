# موقع الأخبار - إعدادات Apache

# تفعيل إعادة الكتابة
RewriteEngine On

# منع الوصول إلى ملفات النظام الحساسة (مسارات نسبية)
<Files "database.php">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>

<Files "*.log">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>

# منع الوصول إلى مجلدات النظام (مسارات نسبية للمجلد الحالي)
<IfModule mod_rewrite.c>
    RewriteRule ^logs/ - [F,L]
    RewriteRule ^config/database\.php$ - [F,L]
    RewriteRule ^cron/ - [F,L]
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # تفعيل XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# منع عرض قوائم المجلدات
Options -Indexes

# إعدادات PHP (إذا كان مسموحاً)
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
</IfModule>

# إعادة توجيه الأخطاء (اختياري)
# ErrorDocument 404 /404.php
# ErrorDocument 500 /500.php

# روابط صديقة لمحركات البحث (اختياري)
# RewriteRule ^article/([^/]+)/?$ article.php?slug=$1 [L,QSA]
# RewriteRule ^category/([^/]+)/?$ category.php?slug=$1 [L,QSA]
# RewriteRule ^search/([^/]+)/?$ search.php?q=$1 [L,QSA]

# منع الوصول المباشر لملفات PHP في مجلد cron
<IfModule mod_rewrite.c>
    RewriteRule ^cron/.*\.php$ - [F,L]
</IfModule>

# حماية أساسية من الهجمات الشائعة (مبسطة)
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (union|select|insert|delete|drop|create|alter) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>
