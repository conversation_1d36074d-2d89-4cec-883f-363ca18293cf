<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>لوحة التحكم الإدارية</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Tajawal', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
        }
        
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        .admin-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .sidebar-active {
            background-color: rgba(59, 130, 246, 0.1);
            border-left: 3px solid #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 bg-white shadow-lg sidebar-transition">
            <div class="p-6">
                <!-- Logo -->
                <div class="flex items-center mb-8">
                    <div class="bg-blue-600 w-10 h-10 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-newspaper text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-800">لوحة التحكم</h1>
                        <p class="text-sm text-gray-600">إدارة الموقع</p>
                    </div>
                </div>
                
                <!-- User Info -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <div class="bg-blue-600 w-10 h-10 rounded-full flex items-center justify-center ml-3">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800"><?php echo $_SESSION['full_name'] ?? $_SESSION['username']; ?></p>
                            <p class="text-sm text-gray-600">
                                <?php echo $_SESSION['user_role'] === 'admin' ? 'مدير' : 'محرر'; ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Navigation -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'sidebar-active' : ''; ?>">
                        <i class="fas fa-tachometer-alt ml-3 w-5"></i>
                        لوحة التحكم
                    </a>
                    
                    <a href="articles.php" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'articles.php' ? 'sidebar-active' : ''; ?>">
                        <i class="fas fa-newspaper ml-3 w-5"></i>
                        المقالات
                    </a>
                    
                    <a href="categories.php" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'categories.php' ? 'sidebar-active' : ''; ?>">
                        <i class="fas fa-folder ml-3 w-5"></i>
                        التصنيفات
                    </a>

                    <a href="matches.php" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'matches.php' ? 'sidebar-active' : ''; ?>">
                        <i class="fas fa-futbol ml-3 w-5"></i>
                        المباريات
                    </a>

                    <a href="rss-sources.php" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'rss-sources.php' ? 'sidebar-active' : ''; ?>">
                        <i class="fas fa-rss ml-3 w-5"></i>
                        مصادر RSS
                    </a>
                    
                    <?php if ($_SESSION['user_role'] === 'admin'): ?>
                    <a href="users.php" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'users.php' ? 'sidebar-active' : ''; ?>">
                        <i class="fas fa-users ml-3 w-5"></i>
                        المستخدمين
                    </a>
                    
                    <a href="settings.php" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors <?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'sidebar-active' : ''; ?>">
                        <i class="fas fa-cog ml-3 w-5"></i>
                        الإعدادات
                    </a>
                    <?php endif; ?>
                    
                    <div class="border-t border-gray-200 my-4"></div>
                    
                    <a href="../index.php" target="_blank" class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-external-link-alt ml-3 w-5"></i>
                        عرض الموقع
                    </a>
                    
                    <a href="logout.php" class="flex items-center px-4 py-3 text-red-600 rounded-lg hover:bg-red-50 transition-colors">
                        <i class="fas fa-sign-out-alt ml-3 w-5"></i>
                        تسجيل الخروج
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 focus:outline-none">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="text-xl font-semibold text-gray-800 mr-4">
                            <?php echo isset($page_title) ? $page_title : 'لوحة التحكم'; ?>
                        </h2>
                    </div>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <!-- Notifications -->
                        <div class="relative">
                            <button class="text-gray-600 hover:text-gray-800 focus:outline-none">
                                <i class="fas fa-bell text-xl"></i>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                            </button>
                        </div>
                        
                        <!-- RSS Fetch Button -->
                        <button onclick="fetchRSS()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                            <i class="fas fa-sync-alt ml-1"></i>
                            جلب RSS
                        </button>
                        
                        <!-- User Menu -->
                        <div class="relative">
                            <button onclick="toggleUserMenu()" class="flex items-center text-gray-600 hover:text-gray-800 focus:outline-none">
                                <div class="bg-blue-600 w-8 h-8 rounded-full flex items-center justify-center ml-2">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <span class="hidden md:block"><?php echo $_SESSION['username']; ?></span>
                                <i class="fas fa-chevron-down mr-2"></i>
                            </button>
                            
                            <div id="user-menu" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <a href="profile.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-t-lg">
                                    <i class="fas fa-user ml-2"></i>
                                    الملف الشخصي
                                </a>
                                <a href="settings.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-cog ml-2"></i>
                                    الإعدادات
                                </a>
                                <div class="border-t border-gray-200"></div>
                                <a href="logout.php" class="block px-4 py-2 text-red-600 hover:bg-red-50 rounded-b-lg">
                                    <i class="fas fa-sign-out-alt ml-2"></i>
                                    تسجيل الخروج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Messages -->
                <?php if (isset($_SESSION['message'])): ?>
                <div class="mb-6">
                    <?php
                    $message_type = $_SESSION['message_type'] ?? 'info';
                    $bg_color = $message_type === 'success' ? 'bg-green-100 border-green-500 text-green-700' : 
                               ($message_type === 'error' ? 'bg-red-100 border-red-500 text-red-700' : 
                                'bg-blue-100 border-blue-500 text-blue-700');
                    ?>
                    <div class="<?php echo $bg_color; ?> border-l-4 p-4 rounded">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle ml-2"></i>
                            <span><?php echo $_SESSION['message']; ?></span>
                        </div>
                    </div>
                    <?php 
                    unset($_SESSION['message'], $_SESSION['message_type']); 
                    ?>
                </div>
                <?php endif; ?>

<script>
// Sidebar toggle for mobile
document.getElementById('sidebar-toggle').addEventListener('click', function() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('-translate-x-full');
});

// User menu toggle
function toggleUserMenu() {
    const userMenu = document.getElementById('user-menu');
    userMenu.classList.toggle('hidden');
}

// Close user menu when clicking outside
document.addEventListener('click', function(event) {
    const userMenu = document.getElementById('user-menu');
    const userButton = event.target.closest('button[onclick="toggleUserMenu()"]');
    
    if (!userButton && !userMenu.contains(event.target)) {
        userMenu.classList.add('hidden');
    }
});

// RSS Fetch function
function fetchRSS() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري الجلب...';
    button.disabled = true;
    
    fetch('fetch-rss.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم جلب ${data.articles_count} مقال جديد بنجاح`);
                location.reload();
            } else {
                alert('حدث خطأ أثناء جلب RSS: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
            console.error('Error:', error);
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
}

// Auto-hide messages after 5 seconds
setTimeout(function() {
    const messages = document.querySelectorAll('[class*="bg-green-100"], [class*="bg-red-100"], [class*="bg-blue-100"]');
    messages.forEach(message => {
        message.style.transition = 'opacity 0.5s ease-out';
        message.style.opacity = '0';
        setTimeout(() => {
            message.remove();
        }, 500);
    });
}, 5000);
</script>
