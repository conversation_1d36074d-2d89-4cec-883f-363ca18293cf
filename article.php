<?php
// تحديد المسار الجذر
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}

// تحميل ملفات التكوين أولاً
require_once 'config/config.php';
require_once 'includes/functions.php';

// بدء الجلسة بعد تحميل التكوين
session_start();

// الحصول على slug المقال
$slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';

if (empty($slug)) {
    header("Location: index.php");
    exit();
}

// الحصول على المقال
$article = getArticle($slug);

if (!$article) {
    header("HTTP/1.0 404 Not Found");
    $page_title = 'المقال غير موجود';
    include 'includes/header.php';
    echo '<div class="container mx-auto px-4 py-16 text-center">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">404 - المقال غير موجود</h1>
            <p class="text-gray-600 mb-8">عذراً، المقال المطلوب غير موجود.</p>
            <a href="index.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                العودة إلى الرئيسية
            </a>
          </div>';
    include 'includes/footer.php';
    exit();
}

// زيادة عدد المشاهدات
incrementViews($article['id']);

// إعداد الصفحة
$page_title = $article['title'];
$page_description = $article['excerpt'];

// الحصول على مقالات ذات صلة
$related_articles = getLatestArticles(4, $article['id']);

include 'includes/header.php';
?>

<main class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Content -->
        <article class="lg:col-span-3">
            <!-- Breadcrumb -->
            <nav class="mb-6">
                <ol class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                    <li><a href="index.php" class="hover:text-blue-600">الرئيسية</a></li>
                    <li><i class="fas fa-chevron-left mx-2"></i></li>
                    <?php if ($article['category_name']): ?>
                    <li>
                        <a href="category.php?slug=<?php echo $article['category_slug']; ?>" 
                           class="hover:text-blue-600"><?php echo $article['category_name']; ?></a>
                    </li>
                    <li><i class="fas fa-chevron-left mx-2"></i></li>
                    <?php endif; ?>
                    <li class="text-gray-700"><?php echo mb_substr($article['title'], 0, 50) . '...'; ?></li>
                </ol>
            </nav>

            <!-- Article Header -->
            <header class="mb-8">
                <div class="mb-4">
                    <?php if ($article['category_name']): ?>
                        <a href="category.php?slug=<?php echo $article['category_slug']; ?>" 
                           class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors">
                            <?php echo $article['category_name']; ?>
                        </a>
                    <?php endif; ?>
                    <?php if ($article['is_featured']): ?>
                        <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium mr-2">
                            مميز
                        </span>
                    <?php endif; ?>
                </div>
                
                <h1 class="text-3xl md:text-4xl font-bold text-gray-800 leading-tight mb-4">
                    <?php echo $article['title']; ?>
                </h1>
                
                <div class="flex flex-wrap items-center text-gray-600 text-sm mb-6">
                    <div class="flex items-center ml-6 mb-2">
                        <i class="fas fa-calendar-alt ml-2 text-blue-600"></i>
                        <span><?php echo formatArabicDate($article['published_at']); ?></span>
                    </div>
                    
                    <div class="flex items-center ml-6 mb-2">
                        <i class="fas fa-eye ml-2 text-blue-600"></i>
                        <span><?php echo number_format($article['views']); ?> مشاهدة</span>
                    </div>
                    
                    <?php if ($article['author']): ?>
                    <div class="flex items-center ml-6 mb-2">
                        <i class="fas fa-user ml-2 text-blue-600"></i>
                        <span><?php echo $article['author']; ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($article['source_name']): ?>
                    <div class="flex items-center mb-2">
                        <i class="fas fa-rss ml-2 text-blue-600"></i>
                        <span><?php echo $article['source_name']; ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Social Share -->
                <div class="flex items-center space-x-4 space-x-reverse border-t border-b border-gray-200 py-4">
                    <span class="text-gray-600 font-medium">مشاركة:</span>
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>" 
                       target="_blank" 
                       class="flex items-center px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                        <i class="fab fa-facebook ml-1"></i>
                        فيسبوك
                    </a>
                    <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>&text=<?php echo urlencode($article['title']); ?>" 
                       target="_blank" 
                       class="flex items-center px-3 py-2 bg-blue-400 text-white rounded hover:bg-blue-500 transition-colors">
                        <i class="fab fa-twitter ml-1"></i>
                        تويتر
                    </a>
                    <a href="https://wa.me/?text=<?php echo urlencode($article['title'] . ' - ' . 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>" 
                       target="_blank" 
                       class="flex items-center px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        <i class="fab fa-whatsapp ml-1"></i>
                        واتساب
                    </a>
                    <button onclick="copyToClipboard()" 
                            class="flex items-center px-3 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                        <i class="fas fa-copy ml-1"></i>
                        نسخ الرابط
                    </button>
                </div>
            </header>

            <!-- Article Image -->
            <?php if ($article['image_url']): ?>
            <div class="mb-8">
                <img src="<?php echo $article['image_url']; ?>" 
                     alt="<?php echo htmlspecialchars($article['title']); ?>"
                     class="w-full h-auto rounded-lg shadow-lg">
            </div>
            <?php endif; ?>

            <!-- Article Content -->
            <div class="prose prose-lg max-w-none mb-8">
                <div class="text-gray-800 leading-relaxed text-lg">
                    <?php echo nl2br($article['content']); ?>
                </div>
            </div>

            <!-- Source Link -->
            <?php if ($article['source_url']): ?>
            <div class="bg-gray-50 p-4 rounded-lg mb-8">
                <p class="text-gray-600 mb-2">
                    <i class="fas fa-external-link-alt ml-2"></i>
                    المصدر الأصلي:
                </p>
                <a href="<?php echo $article['source_url']; ?>" 
                   target="_blank" 
                   class="text-blue-600 hover:text-blue-800 underline break-all">
                    <?php echo $article['source_url']; ?>
                </a>
            </div>
            <?php endif; ?>

            <!-- Tags -->
            <div class="flex flex-wrap gap-2 mb-8">
                <span class="text-gray-600 font-medium ml-2">الكلمات المفتاحية:</span>
                <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"><?php echo $article['category_name']; ?></span>
                <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">أخبار</span>
                <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">عاجل</span>
            </div>

            <!-- Related Articles -->
            <?php if (!empty($related_articles)): ?>
            <section class="border-t border-gray-200 pt-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-newspaper ml-2 text-blue-600"></i>
                    أخبار ذات صلة
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php foreach ($related_articles as $related): ?>
                    <article class="bg-white rounded-lg shadow-md overflow-hidden news-card">
                        <div class="flex">
                            <div class="w-24 h-20 flex-shrink-0">
                                <?php if ($related['image_url']): ?>
                                    <img src="<?php echo $related['image_url']; ?>" 
                                         alt="<?php echo htmlspecialchars($related['title']); ?>"
                                         class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 p-3">
                                <h4 class="font-semibold text-sm leading-tight mb-1">
                                    <a href="article.php?slug=<?php echo $related['slug']; ?>" 
                                       class="hover:text-blue-600 transition-colors">
                                        <?php echo mb_substr($related['title'], 0, 80) . (mb_strlen($related['title']) > 80 ? '...' : ''); ?>
                                    </a>
                                </h4>
                                <div class="text-xs text-gray-500">
                                    <i class="fas fa-clock ml-1"></i>
                                    <?php echo formatArabicDate($related['published_at']); ?>
                                </div>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; ?>
        </article>

        <!-- Sidebar -->
        <aside class="lg:col-span-1">
            <!-- Latest News -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-clock ml-2 text-blue-600"></i>
                    أحدث الأخبار
                </h3>
                <div class="space-y-4">
                    <?php 
                    $sidebar_articles = getLatestArticles(6, $article['id']);
                    foreach ($sidebar_articles as $sidebar_article): 
                    ?>
                    <article class="flex space-x-3 space-x-reverse">
                        <div class="w-16 h-12 flex-shrink-0">
                            <?php if ($sidebar_article['image_url']): ?>
                                <img src="<?php echo $sidebar_article['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($sidebar_article['title']); ?>"
                                     class="w-full h-full object-cover rounded">
                            <?php else: ?>
                                <div class="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sm leading-tight mb-1">
                                <a href="article.php?slug=<?php echo $sidebar_article['slug']; ?>" 
                                   class="hover:text-blue-600 transition-colors">
                                    <?php echo mb_substr($sidebar_article['title'], 0, 60) . (mb_strlen($sidebar_article['title']) > 60 ? '...' : ''); ?>
                                </a>
                            </h4>
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-clock ml-1"></i>
                                <?php echo formatDate($sidebar_article['published_at'], 'H:i'); ?>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Categories -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-list ml-2 text-blue-600"></i>
                    التصنيفات
                </h3>
                <div class="space-y-2">
                    <?php foreach (getCategories() as $category): ?>
                    <a href="category.php?slug=<?php echo $category['slug']; ?>" 
                       class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-700"><?php echo $category['name']; ?></span>
                        <i class="fas fa-chevron-left text-gray-400"></i>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </aside>
    </div>
</main>

<script>
function copyToClipboard() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
        alert('تم نسخ الرابط بنجاح!');
    }, function(err) {
        console.error('خطأ في نسخ الرابط: ', err);
    });
}

// Add smooth scrolling
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
