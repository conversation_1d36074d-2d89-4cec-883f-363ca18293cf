<?php
/**
 * تشخيص متقدم لمشاكل التكرار والعرض
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تشخيص متقدم للمشاكل</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo "th, td { padding: 12px; text-align: right; border: 1px solid #ddd; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo "h3 { color: #2980b9; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo ".duplicate { background: #ffebee; border-left: 4px solid #f44336; }";
echo ".issue { background: #fff3e0; border-left: 4px solid #ff9800; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 تشخيص متقدم لمشاكل التكرار والعرض</h1>";

// 1. فحص مشاكل التكرار في الصفحة الرئيسية
echo "<div class='section error'>";
echo "<h2>1. فحص مشاكل التكرار في الصفحة الرئيسية</h2>";

// قراءة محتوى index.php
$index_content = file_get_contents('index.php');

// البحث عن التكرارات المحتملة
$potential_duplicates = [
    'المباريات القادمة' => [
        'pattern' => '/المباريات القادمة/u',
        'description' => 'عنوان المباريات القادمة'
    ],
    'سلايدر المباريات' => [
        'pattern' => '/matches-slider/i',
        'description' => 'عنصر سلايدر المباريات'
    ],
    'أحدث الأخبار' => [
        'pattern' => '/أحدث الأخبار/u',
        'description' => 'عنوان أحدث الأخبار'
    ],
    'foreach.*articles' => [
        'pattern' => '/foreach.*articles.*as/i',
        'description' => 'حلقات عرض المقالات'
    ],
    'foreach.*matches' => [
        'pattern' => '/foreach.*matches.*as/i',
        'description' => 'حلقات عرض المباريات'
    ]
];

echo "<h3>نتائج فحص التكرار:</h3>";
echo "<table>";
echo "<tr><th>العنصر</th><th>عدد التكرارات</th><th>الحالة</th><th>التفاصيل</th></tr>";

foreach ($potential_duplicates as $name => $config) {
    preg_match_all($config['pattern'], $index_content, $matches);
    $count = count($matches[0]);
    
    $status = '';
    $class = '';
    if ($count > 2) {
        $status = '❌ تكرار مشكوك فيه';
        $class = 'duplicate';
    } elseif ($count == 2) {
        $status = '⚠️ تكرار محتمل';
        $class = 'issue';
    } else {
        $status = '✅ طبيعي';
        $class = '';
    }
    
    echo "<tr class='$class'>";
    echo "<td>$name</td>";
    echo "<td>$count</td>";
    echo "<td>$status</td>";
    echo "<td>" . $config['description'] . "</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 2. فحص بنية HTML
echo "<div class='section info'>";
echo "<h2>2. فحص بنية HTML</h2>";

// فحص العناصر المكررة في HTML
$html_elements = [
    'header' => '/\<header/i',
    'footer' => '/\<footer/i',
    'main' => '/\<main/i',
    'nav' => '/\<nav/i',
    'section.*matches' => '/\<section.*matches/i'
];

echo "<h3>فحص العناصر الأساسية:</h3>";
echo "<table>";
echo "<tr><th>العنصر</th><th>عدد التكرارات</th><th>الحالة</th></tr>";

foreach ($html_elements as $element => $pattern) {
    preg_match_all($pattern, $index_content, $matches);
    $count = count($matches[0]);
    
    $expected = ($element === 'section.*matches') ? 1 : 1;
    
    if ($count > $expected) {
        $status = '❌ تكرار غير مرغوب';
        $class = 'duplicate';
    } elseif ($count == $expected) {
        $status = '✅ طبيعي';
        $class = '';
    } else {
        $status = '⚠️ مفقود';
        $class = 'issue';
    }
    
    echo "<tr class='$class'>";
    echo "<td>$element</td>";
    echo "<td>$count</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 3. فحص استدعاءات قاعدة البيانات
echo "<div class='section warning'>";
echo "<h2>3. فحص استدعاءات قاعدة البيانات</h2>";

$db_calls = [
    'getArticles' => '/getArticles\s*\(/i',
    'getUpcomingMatches' => '/getUpcomingMatches\s*\(/i',
    'getFeaturedArticles' => '/getFeaturedArticles\s*\(/i',
    'getLatestArticles' => '/getLatestArticles\s*\(/i',
    'getCategories' => '/getCategories\s*\(/i'
];

echo "<h3>استدعاءات الدوال:</h3>";
echo "<table>";
echo "<tr><th>الدالة</th><th>عدد الاستدعاءات</th><th>الحالة</th></tr>";

foreach ($db_calls as $function => $pattern) {
    preg_match_all($pattern, $index_content, $matches);
    $count = count($matches[0]);
    
    if ($count > 2) {
        $status = '❌ استدعاءات مفرطة';
        $class = 'duplicate';
    } elseif ($count <= 2) {
        $status = '✅ طبيعي';
        $class = '';
    } else {
        $status = '⚠️ لا استدعاءات';
        $class = 'issue';
    }
    
    echo "<tr class='$class'>";
    echo "<td>$function</td>";
    echo "<td>$count</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 4. فحص ملفات CSS و JavaScript
echo "<div class='section info'>";
echo "<h2>4. فحص ملفات CSS و JavaScript</h2>";

$css_files = [
    'assets/css/homepage-enhancements.css',
    'assets/css/style.css',
    'assets/css/matches-slider.css'
];

$js_files = [
    'assets/js/homepage-enhancements.js',
    'assets/js/matches-slider.js',
    'assets/js/main.js'
];

echo "<h3>ملفات CSS:</h3>";
echo "<table>";
echo "<tr><th>الملف</th><th>الحالة</th><th>الحجم</th></tr>";

foreach ($css_files as $file) {
    if (file_exists($file)) {
        $size = round(filesize($file) / 1024, 2) . ' KB';
        echo "<tr><td>$file</td><td style='color: green;'>✅ موجود</td><td>$size</td></tr>";
    } else {
        echo "<tr><td>$file</td><td style='color: red;'>❌ غير موجود</td><td>-</td></tr>";
    }
}
echo "</table>";

echo "<h3>ملفات JavaScript:</h3>";
echo "<table>";
echo "<tr><th>الملف</th><th>الحالة</th><th>الحجم</th></tr>";

foreach ($js_files as $file) {
    if (file_exists($file)) {
        $size = round(filesize($file) / 1024, 2) . ' KB';
        echo "<tr><td>$file</td><td style='color: green;'>✅ موجود</td><td>$size</td></tr>";
    } else {
        echo "<tr><td>$file</td><td style='color: red;'>❌ غير موجود</td><td>-</td></tr>";
    }
}
echo "</table>";
echo "</div>";

// 5. فحص مشاكل العرض المحتملة
echo "<div class='section warning'>";
echo "<h2>5. فحص مشاكل العرض المحتملة</h2>";

// فحص استخدام Tailwind CSS
$tailwind_classes = [
    'container mx-auto',
    'grid grid-cols',
    'flex items-center',
    'bg-gradient-to',
    'rounded-lg',
    'shadow-lg'
];

echo "<h3>استخدام Tailwind CSS:</h3>";
echo "<table>";
echo "<tr><th>الكلاس</th><th>عدد الاستخدامات</th><th>الحالة</th></tr>";

foreach ($tailwind_classes as $class) {
    $count = substr_count($index_content, $class);
    
    if ($count > 0) {
        $status = '✅ مستخدم';
        $color = 'green';
    } else {
        $status = '❌ غير مستخدم';
        $color = 'red';
    }
    
    echo "<tr><td>$class</td><td>$count</td><td style='color: $color;'>$status</td></tr>";
}
echo "</table>";
echo "</div>";

// 6. اختبار الأداء
echo "<div class='section success'>";
echo "<h2>6. اختبار الأداء</h2>";

$start_time = microtime(true);

// محاكاة تحميل الصفحة الرئيسية
try {
    if (file_exists('includes/matches_functions.php')) {
        require_once 'includes/matches_functions.php';
        $upcoming_matches = getUpcomingMatches(5);
    }
    
    $articles = getLatestArticles(12);
    $featured_articles = getFeaturedArticles(4);
    $categories = getCategories();
    
    $end_time = microtime(true);
    $execution_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "<p>✅ وقت تحميل البيانات: <strong>{$execution_time} مللي ثانية</strong></p>";
    
    if ($execution_time > 1000) {
        echo "<p style='color: red;'>⚠️ الأداء بطيء - يحتاج تحسين</p>";
    } elseif ($execution_time > 500) {
        echo "<p style='color: orange;'>⚠️ الأداء متوسط</p>";
    } else {
        echo "<p style='color: green;'>✅ الأداء ممتاز</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل البيانات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 7. توصيات الإصلاح
echo "<div class='section info'>";
echo "<h2>7. توصيات الإصلاح</h2>";
echo "<ul>";
echo "<li>🔧 <strong>إزالة التكرارات:</strong> فحص الكود لإزالة أي عناصر مكررة</li>";
echo "<li>🎨 <strong>تحسين CSS:</strong> دمج ملفات CSS وتقليل الحجم</li>";
echo "<li>⚡ <strong>تحسين الأداء:</strong> تحسين استدعاءات قاعدة البيانات</li>";
echo "<li>📱 <strong>التصميم المتجاوب:</strong> اختبار العرض على أجهزة مختلفة</li>";
echo "<li>🔍 <strong>اختبار المتصفحات:</strong> اختبار التوافق مع متصفحات مختلفة</li>";
echo "</ul>";
echo "</div>";

// 8. روابط الإصلاح السريع
echo "<div class='section success'>";
echo "<h2>8. أدوات الإصلاح السريع</h2>";
echo "<a href='fix-duplicates.php' class='btn btn-danger'>إصلاح التكرارات</a>";
echo "<a href='optimize-performance.php' class='btn btn-warning'>تحسين الأداء</a>";
echo "<a href='test-responsive.php' class='btn btn-primary'>اختبار التصميم المتجاوب</a>";
echo "<a href='validate-html.php' class='btn btn-success'>فحص HTML</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
