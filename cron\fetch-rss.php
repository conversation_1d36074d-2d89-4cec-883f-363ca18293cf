<?php
/**
 * Cron Job لجلب مصادر RSS تلقائياً
 * يتم تشغيله كل ساعة أو حسب الحاجة
 * 
 * لإضافة هذا الملف إلى cron job:
 * 0 * * * * /usr/bin/php /path/to/your/website/cron/fetch-rss.php
 */

// تعيين مسار المشروع
$project_root = dirname(__DIR__);
require_once $project_root . '/config/database.php';
require_once $project_root . '/classes/RSSParser.php';
require_once $project_root . '/includes/functions.php';

// تسجيل بداية العملية
$log_message = "[" . date('Y-m-d H:i:s') . "] بدء جلب مصادر RSS التلقائي\n";
file_put_contents($project_root . '/logs/rss-cron.log', $log_message, FILE_APPEND | LOCK_EX);

try {
    // التحقق من إعداد الجلب التلقائي
    $auto_fetch = getSetting('auto_fetch_rss', 'true');
    
    if ($auto_fetch !== 'true') {
        $log_message = "[" . date('Y-m-d H:i:s') . "] الجلب التلقائي معطل\n";
        file_put_contents($project_root . '/logs/rss-cron.log', $log_message, FILE_APPEND | LOCK_EX);
        exit();
    }
    
    $rssParser = new RSSParser();
    $database = new Database();
    $db = $database->connect();
    
    // الحصول على مصادر RSS التي تحتاج إلى تحديث
    $fetch_interval = (int) getSetting('rss_fetch_interval', 3600); // افتراضي: ساعة واحدة
    
    $stmt = $db->prepare("
        SELECT id, name, url 
        FROM rss_sources 
        WHERE is_active = 1 
        AND (last_fetched IS NULL OR last_fetched < DATE_SUB(NOW(), INTERVAL ? SECOND))
    ");
    $stmt->execute([$fetch_interval]);
    $sources = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total_articles = 0;
    $successful_sources = 0;
    $failed_sources = 0;
    
    foreach ($sources as $source) {
        try {
            $log_message = "[" . date('Y-m-d H:i:s') . "] جلب من مصدر: {$source['name']} ({$source['url']})\n";
            file_put_contents($project_root . '/logs/rss-cron.log', $log_message, FILE_APPEND | LOCK_EX);
            
            $articles_count = $rssParser->fetchRSSFeed($source['id']);
            
            if ($articles_count !== false) {
                $total_articles += $articles_count;
                $successful_sources++;
                
                $log_message = "[" . date('Y-m-d H:i:s') . "] نجح جلب $articles_count مقال من {$source['name']}\n";
                file_put_contents($project_root . '/logs/rss-cron.log', $log_message, FILE_APPEND | LOCK_EX);
            } else {
                $failed_sources++;
                
                $log_message = "[" . date('Y-m-d H:i:s') . "] فشل جلب من {$source['name']}\n";
                file_put_contents($project_root . '/logs/rss-cron.log', $log_message, FILE_APPEND | LOCK_EX);
            }
            
            // تأخير قصير بين المصادر لتجنب الحمل الزائد
            sleep(2);
            
        } catch (Exception $e) {
            $failed_sources++;
            $log_message = "[" . date('Y-m-d H:i:s') . "] خطأ في جلب من {$source['name']}: " . $e->getMessage() . "\n";
            file_put_contents($project_root . '/logs/rss-cron.log', $log_message, FILE_APPEND | LOCK_EX);
        }
    }
    
    // تسجيل النتائج النهائية
    $log_message = "[" . date('Y-m-d H:i:s') . "] انتهاء الجلب التلقائي - إجمالي المقالات: $total_articles، مصادر ناجحة: $successful_sources، مصادر فاشلة: $failed_sources\n";
    file_put_contents($project_root . '/logs/rss-cron.log', $log_message, FILE_APPEND | LOCK_EX);
    
    // تنظيف ملف السجل إذا أصبح كبيراً جداً (أكثر من 1MB)
    $log_file = $project_root . '/logs/rss-cron.log';
    if (file_exists($log_file) && filesize($log_file) > 1048576) {
        $lines = file($log_file);
        $recent_lines = array_slice($lines, -1000); // الاحتفاظ بآخر 1000 سطر
        file_put_contents($log_file, implode('', $recent_lines));
    }
    
    echo "RSS Fetch completed successfully. Articles: $total_articles, Successful sources: $successful_sources, Failed sources: $failed_sources\n";
    
} catch (Exception $e) {
    $log_message = "[" . date('Y-m-d H:i:s') . "] خطأ عام في الجلب التلقائي: " . $e->getMessage() . "\n";
    file_put_contents($project_root . '/logs/rss-cron.log', $log_message, FILE_APPEND | LOCK_EX);
    
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
