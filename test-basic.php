<?php
/**
 * Basic PHP Test - Minimal functionality check
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic PHP Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Basic PHP Test</h1>
    
    <h2>PHP Information</h2>
    <p class="success">✅ PHP is working!</p>
    <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
    <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
    <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <h2>File System Check</h2>
    <?php
    $files_to_check = [
        'index.php',
        'config/database.php',
        'includes/functions.php',
        'admin/login.php'
    ];
    
    foreach ($files_to_check as $file) {
        if (file_exists($file)) {
            echo "<p class='success'>✅ $file - Found</p>";
        } else {
            echo "<p class='error'>❌ $file - Missing</p>";
        }
    }
    ?>
    
    <h2>Directory Check</h2>
    <?php
    $dirs_to_check = ['admin', 'config', 'includes', 'classes', 'logs'];
    
    foreach ($dirs_to_check as $dir) {
        if (is_dir($dir)) {
            echo "<p class='success'>✅ $dir/ - Found</p>";
        } else {
            echo "<p class='error'>❌ $dir/ - Missing</p>";
        }
    }
    ?>
    
    <h2>Quick Actions</h2>
    <p><a href="index.php">🏠 Go to Homepage</a></p>
    <p><a href="setup.php">⚙️ Database Setup</a></p>
    <p><a href="admin/login.php">🔐 Admin Login</a></p>
    <p><a href="troubleshoot-403.php">🔧 Troubleshooting Tool</a></p>
    
    <h2>Current Directory Contents</h2>
    <ul>
    <?php
    $files = scandir('.');
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $type = is_dir($file) ? '[DIR]' : '[FILE]';
            echo "<li>$type $file</li>";
        }
    }
    ?>
    </ul>
    
    <hr>
    <p><small>If you can see this page, PHP is working correctly. The 403 error is likely due to .htaccess or permission issues.</small></p>
</body>
</html>
