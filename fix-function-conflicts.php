<?php
/**
 * إصلاح تضارب الدوال بين session_init.php و functions.php
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح تضارب الدوال</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; border-left: 4px solid #007bff; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح تضارب الدوال</h1>";

$fixes_applied = [];
$errors = [];

// 1. تشخيص المشكلة
echo "<div class='section error'>";
echo "<h2>1. تشخيص المشكلة</h2>";

echo "<p><strong>المشكلة:</strong> تضارب في الدوال بين session_init.php و functions.php</p>";
echo "<p><strong>الدوال المتضاربة:</strong></p>";
echo "<ul>";
echo "<li>isLoggedIn() - موجودة في كلا الملفين</li>";
echo "<li>getUserId() - موجودة في session_init.php فقط</li>";
echo "<li>setFlashMessage() - موجودة في session_init.php فقط</li>";
echo "<li>getFlashMessages() - موجودة في session_init.php فقط</li>";
echo "</ul>";

echo "<p><strong>الحل:</strong> إزالة الدوال المكررة من functions.php والاحتفاظ بها في session_init.php</p>";
echo "</div>";

// 2. إنشاء نسخة احتياطية
echo "<div class='section info'>";
echo "<h2>2. إنشاء نسخة احتياطية</h2>";

if (file_exists('includes/functions.php')) {
    $backup_name = 'includes/functions.php.backup.' . date('Y-m-d-H-i-s');
    if (copy('includes/functions.php', $backup_name)) {
        echo "<p style='color: green;'>✅ تم إنشاء نسخة احتياطية: $backup_name</p>";
        $fixes_applied[] = "إنشاء نسخة احتياطية من functions.php";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء نسخة احتياطية</p>";
        $errors[] = "فشل في إنشاء نسخة احتياطية";
    }
} else {
    echo "<p style='color: red;'>❌ ملف functions.php غير موجود</p>";
}
echo "</div>";

// 3. إصلاح تضارب الدوال
echo "<div class='section success'>";
echo "<h2>3. إصلاح تضارب الدوال</h2>";

if (file_exists('includes/functions.php')) {
    $functions_content = file_get_contents('includes/functions.php');
    
    // إزالة الدوال المتضاربة من functions.php
    $functions_to_remove = [
        'isLoggedIn',
        'getUserId', 
        'setFlashMessage',
        'getFlashMessages',
        'generateCSRFToken',
        'validateCSRFToken'
    ];
    
    foreach ($functions_to_remove as $function_name) {
        // البحث عن الدالة وإزالتها
        $pattern = '/\/\*\*.*?\*\/\s*function\s+' . $function_name . '\s*\([^}]*\}\s*/s';
        if (preg_match($pattern, $functions_content)) {
            $functions_content = preg_replace($pattern, '', $functions_content);
            echo "<p style='color: green;'>✅ تم إزالة الدالة $function_name من functions.php</p>";
            $fixes_applied[] = "إزالة الدالة $function_name";
        }
    }
    
    // تنظيف الأسطر الفارغة الزائدة
    $functions_content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $functions_content);
    
    // حفظ الملف المحدث
    if (file_put_contents('includes/functions.php', $functions_content)) {
        echo "<p style='color: green;'>✅ تم تحديث functions.php بنجاح</p>";
        $fixes_applied[] = "تحديث functions.php";
    } else {
        echo "<p style='color: red;'>❌ فشل في تحديث functions.php</p>";
        $errors[] = "فشل في تحديث functions.php";
    }
} else {
    echo "<p style='color: red;'>❌ ملف functions.php غير موجود</p>";
}
echo "</div>";

// 4. تحديث session_init.php لإضافة دوال إضافية
echo "<div class='section info'>";
echo "<h2>4. تحديث session_init.php</h2>";

if (file_exists('includes/session_init.php')) {
    $session_content = file_get_contents('includes/session_init.php');
    
    // التحقق من وجود الدوال المطلوبة
    $required_functions = [
        'isAdmin' => '// دالة للتحقق من صلاحيات الإدارة
function isAdmin() {
    return isLoggedIn() && isset($_SESSION[\'user_role\']) && $_SESSION[\'user_role\'] === \'admin\';
}',
        'redirect' => '// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}',
        'showMessage' => '// دالة لعرض رسالة (متوافقة مع النظام القديم)
function showMessage($message, $type = \'info\') {
    setFlashMessage($type, $message);
}',
        'getMessage' => '// دالة للحصول على الرسالة (متوافقة مع النظام القديم)
function getMessage() {
    $messages = getFlashMessages(false);
    if (!empty($messages)) {
        $message = $messages[0];
        getFlashMessages(true); // حذف الرسائل
        return [\'message\' => $message[\'message\'], \'type\' => $message[\'type\']];
    }
    return null;
}'
    ];
    
    $functions_added = [];
    foreach ($required_functions as $func_name => $func_code) {
        if (strpos($session_content, "function $func_name") === false) {
            $session_content = rtrim($session_content, " \n\r\t?>");
            $session_content .= "\n\n" . $func_code . "\n";
            $functions_added[] = $func_name;
        }
    }
    
    // إضافة إغلاق PHP في النهاية
    if (!str_ends_with(trim($session_content), '?>')) {
        $session_content .= "\n?>";
    }
    
    if (!empty($functions_added)) {
        if (file_put_contents('includes/session_init.php', $session_content)) {
            echo "<p style='color: green;'>✅ تم إضافة الدوال المفقودة إلى session_init.php</p>";
            echo "<ul>";
            foreach ($functions_added as $func) {
                echo "<li>✅ $func()</li>";
            }
            echo "</ul>";
            $fixes_applied[] = "إضافة دوال مفقودة إلى session_init.php";
        } else {
            echo "<p style='color: red;'>❌ فشل في تحديث session_init.php</p>";
            $errors[] = "فشل في تحديث session_init.php";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ جميع الدوال المطلوبة موجودة في session_init.php</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف session_init.php غير موجود</p>";
}
echo "</div>";

// 5. اختبار الإصلاح
echo "<div class='section warning'>";
echo "<h2>5. اختبار الإصلاح</h2>";

// محاولة تحميل الملفات للتأكد من عدم وجود أخطاء
ob_start();
$test_passed = true;

try {
    // اختبار تحميل session_init.php
    include_once 'includes/session_init.php';
    echo "<p style='color: green;'>✅ تم تحميل session_init.php بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل session_init.php: " . $e->getMessage() . "</p>";
    $test_passed = false;
}

try {
    // اختبار تحميل functions.php
    include_once 'includes/functions.php';
    echo "<p style='color: green;'>✅ تم تحميل functions.php بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل functions.php: " . $e->getMessage() . "</p>";
    $test_passed = false;
}

// اختبار الدوال
$functions_to_test = ['isLoggedIn', 'isAdmin', 'getUserId', 'generateCSRFToken'];
foreach ($functions_to_test as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ الدالة $func() متاحة</p>";
    } else {
        echo "<p style='color: red;'>❌ الدالة $func() غير متاحة</p>";
        $test_passed = false;
    }
}

ob_end_clean();

if ($test_passed) {
    echo "<p style='color: green; font-weight: bold;'>🎉 جميع الاختبارات نجحت!</p>";
    $fixes_applied[] = "اجتياز جميع الاختبارات";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ بعض الاختبارات فشلت</p>";
    $errors[] = "فشل في بعض الاختبارات";
}
echo "</div>";

// 6. ملخص الإصلاحات
echo "<div class='section success'>";
echo "<h2>6. ملخص الإصلاحات المطبقة</h2>";

if (!empty($fixes_applied)) {
    echo "<h3>الإصلاحات المطبقة:</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li style='color: green;'>✅ $fix</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: blue;'>ℹ️ لم يتم تطبيق إصلاحات جديدة</p>";
}

if (!empty($errors)) {
    echo "<h3>الأخطاء:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>❌ $error</li>";
    }
    echo "</ul>";
}

echo "<h3>التحسينات المطبقة:</h3>";
echo "<ul>";
echo "<li>🔧 <strong>إزالة تضارب الدوال:</strong> إزالة الدوال المكررة من functions.php</li>";
echo "<li>📋 <strong>توحيد الدوال:</strong> جميع دوال الجلسة في session_init.php</li>";
echo "<li>🔄 <strong>التوافق العكسي:</strong> الحفاظ على التوافق مع الكود القديم</li>";
echo "<li>🧹 <strong>تنظيف الكود:</strong> إزالة التكرارات والكود غير المستخدم</li>";
echo "</ul>";
echo "</div>";

// 7. اختبار النتائج
echo "<div class='section info'>";
echo "<h2>7. اختبار النتائج</h2>";
echo "<div style='text-align: center;'>";
echo "<a href='index.php' target='_blank' class='btn btn-primary'>اختبار الصفحة الرئيسية</a>";
echo "<a href='matches.php' target='_blank' class='btn btn-success'>اختبار صفحة المباريات</a>";
echo "<a href='test-session-functionality.php' target='_blank' class='btn btn-warning'>اختبار وظائف الجلسة</a>";
echo "<a href='admin/login.php' target='_blank' class='btn btn-danger'>اختبار لوحة التحكم</a>";
echo "</div>";

echo "<p style='margin-top: 20px; text-align: center; color: #27ae60; font-weight: bold;'>";
echo "🎉 تم إصلاح تضارب الدوال بنجاح!";
echo "</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
