# 🚨 HTTP 500 Internal Server Error - Complete Troubleshooting Guide

## 🎯 **IMMEDIATE QUICK FIXES** (Try these first!)

### **Fix 1: Run Minimal Test**
```
http://localhost/amr/minimal-test.php
```
This will test each component step by step.

### **Fix 2: Use Automatic Fix Tool**
```
http://localhost/amr/fix-500-errors.php
```
This provides one-click fixes for common issues.

### **Fix 3: Run Full Diagnostic**
```
http://localhost/amr/debug-500.php
```
This shows detailed error information.

---

## 🔍 **ROOT CAUSE ANALYSIS**

HTTP 500 errors in PHP are typically caused by:

1. **PHP Syntax Errors** (90% of cases)
2. **Missing/Incorrect Include Files** (80% of cases)
3. **Database Connection Issues** (70% of cases)
4. **Missing PHP Extensions** (60% of cases)
5. **File Permission Issues** (50% of cases)
6. **Memory/Resource Limits** (30% of cases)

---

## 📋 **STEP-BY-STEP DEBUGGING**

### **Step 1: Check PHP Error Logs**

#### **XAMPP (Windows):**
```
C:\xampp\apache\logs\error.log
C:\xampp\php\logs\php_error_log
```

#### **WAMP (Windows):**
```
C:\wamp64\logs\apache_error.log
C:\wamp64\logs\php_error.log
```

#### **Linux/Mac:**
```bash
tail -f /var/log/apache2/error.log
tail -f /var/log/php_errors.log
```

### **Step 2: Enable PHP Error Display**
Add this to the top of `index.php`:
```php
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
?>
```

### **Step 3: Test Individual Components**

#### **Test 1: Basic PHP**
Create `test.php`:
```php
<?php
echo "PHP is working - Version: " . PHP_VERSION;
phpinfo();
?>
```

#### **Test 2: Database Connection**
```php
<?php
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "Database connection successful";
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
}
?>
```

#### **Test 3: Include Files**
```php
<?php
if (file_exists('config/config.php')) {
    include_once 'config/config.php';
    echo "Config loaded successfully";
} else {
    echo "Config file missing";
}
?>
```

---

## 🛠️ **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Syntax Errors**

**Symptoms:** 500 error immediately when accessing any PHP file

**Solution:**
```bash
# Check syntax of all PHP files
php -l index.php
php -l config/config.php
php -l config/database.php
php -l includes/functions.php
```

**Common syntax errors:**
- Missing semicolons `;`
- Unmatched brackets `{` `}`
- Incorrect quotes `"` `'`
- Missing `?>` tags

### **Issue 2: Missing Include Files**

**Symptoms:** 500 error with "failed to open stream" in logs

**Solution:**
```php
// Fix include paths in index.php
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}

require_once ROOT_PATH . '/config/config.php';
require_once ROOT_PATH . '/includes/functions.php';
```

### **Issue 3: Database Connection Errors**

**Symptoms:** 500 error when database is accessed

**Solution:**
1. **Check MySQL is running**
2. **Verify database credentials in `config/database.php`**
3. **Create database if it doesn't exist**

```sql
CREATE DATABASE news_website CHARACTER SET utf8 COLLATE utf8_general_ci;
```

### **Issue 4: Missing PHP Extensions**

**Symptoms:** 500 error with "Call to undefined function" in logs

**Required extensions:**
- `pdo`
- `pdo_mysql`
- `simplexml`
- `curl`
- `mbstring`

**Check extensions:**
```php
<?php
$required = ['pdo', 'pdo_mysql', 'simplexml', 'curl', 'mbstring'];
foreach ($required as $ext) {
    echo $ext . ': ' . (extension_loaded($ext) ? 'OK' : 'MISSING') . "\n";
}
?>
```

### **Issue 5: Memory/Resource Limits**

**Symptoms:** 500 error with "Fatal error: Allowed memory size" in logs

**Solution:**
```php
// Add to config/config.php
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
```

### **Issue 6: File Permissions (Linux/Mac)**

**Symptoms:** 500 error with "Permission denied" in logs

**Solution:**
```bash
# Fix permissions
chmod 644 *.php
chmod 755 admin/ config/ includes/ classes/
chmod 755 .
```

---

## 🔧 **AUTOMATED FIXES**

### **Use the Fix Tool:**
```
http://localhost/amr/fix-500-errors.php
```

**Available fixes:**
1. **Create Simple Working Index** - Replaces complex index.php with basic version
2. **Fix Config File** - Creates safe configuration
3. **Fix Database Class** - Creates minimal database connection
4. **Fix Functions File** - Creates safe functions
5. **Fix Include Paths** - Corrects all include statements
6. **Fix File Permissions** - Sets correct permissions (Linux/Mac)

---

## 🎯 **ENVIRONMENT-SPECIFIC SOLUTIONS**

### **XAMPP (Windows)**

1. **Start Apache and MySQL** in XAMPP Control Panel
2. **Check Apache error log:** `C:\xampp\apache\logs\error.log`
3. **Enable mod_rewrite:** Usually enabled by default
4. **PHP extensions:** Check `C:\xampp\php\php.ini`

**Common XAMPP fixes:**
```ini
; In C:\xampp\php\php.ini
extension=pdo_mysql
extension=simplexml
extension=curl
extension=mbstring

; Increase limits
memory_limit = 256M
max_execution_time = 300
```

### **WAMP (Windows)**

1. **All services green** in WAMP tray
2. **PHP extensions:** Left-click WAMP → PHP → PHP Extensions
3. **Error logs:** `C:\wamp64\logs\`

### **Linux/Mac**

1. **Check Apache status:**
```bash
sudo systemctl status apache2
```

2. **Check PHP-FPM (if used):**
```bash
sudo systemctl status php7.4-fpm
```

3. **Install missing extensions:**
```bash
# Ubuntu/Debian
sudo apt-get install php-mysql php-xml php-curl php-mbstring

# CentOS/RHEL
sudo yum install php-mysql php-xml php-curl php-mbstring
```

---

## 🚀 **EMERGENCY SOLUTIONS**

### **Solution 1: Use PHP Built-in Server**
```bash
cd /path/to/your/project
php -S localhost:8000
```
Access: `http://localhost:8000`

### **Solution 2: Create Minimal Working Site**
Run the fix tool and select "Create Simple Working Index"

### **Solution 3: Disable Complex Features**
Temporarily comment out complex includes in `index.php`:
```php
<?php
// require_once 'config/config.php';
// require_once 'includes/functions.php';

echo "Basic site working";
?>
```

---

## ✅ **TESTING CHECKLIST**

After applying fixes, test these URLs:
- ✅ `http://localhost/amr/minimal-test.php`
- ✅ `http://localhost/amr/debug-500.php`
- ✅ `http://localhost/amr/index.php`
- ✅ `http://localhost/amr/setup.php`
- ✅ `http://localhost/amr/admin/login.php`

---

## 📞 **GETTING HELP**

### **Check Error Logs First:**
1. **Apache error log** - Shows server-level errors
2. **PHP error log** - Shows PHP-specific errors
3. **Browser console** - Shows JavaScript errors

### **Common Error Messages:**

**"Parse error: syntax error"**
→ Fix PHP syntax in the mentioned file

**"Fatal error: require_once(): Failed opening"**
→ Fix include paths or create missing files

**"Fatal error: Class 'Database' not found"**
→ Include database.php or fix the Database class

**"Fatal error: Call to undefined function"**
→ Install missing PHP extension or include functions.php

**"Fatal error: Allowed memory size exhausted"**
→ Increase memory_limit in PHP configuration

---

## 🎯 **SUCCESS INDICATORS**

You've fixed the 500 error when:
1. ✅ `minimal-test.php` shows all green checkmarks
2. ✅ `debug-500.php` shows no critical errors
3. ✅ `index.php` loads without errors
4. ✅ Error logs show no new PHP errors

The 500 error is almost always due to PHP syntax errors or missing files. Use the diagnostic tools provided to identify the exact cause, then apply the appropriate fix.
