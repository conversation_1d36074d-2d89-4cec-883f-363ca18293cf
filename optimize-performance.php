<?php
/**
 * تحسين أداء الموقع
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تحسين الأداء</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo "table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo "th, td { padding: 12px; text-align: right; border: 1px solid #ddd; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".progress-bar { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }";
echo ".progress-fill { height: 100%; background: linear-gradient(90deg, #27ae60, #2ecc71); transition: width 0.3s ease; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>⚡ تحسين أداء الموقع</h1>";

$optimizations = [];
$performance_score = 0;
$total_tests = 8;

// 1. قياس أداء قاعدة البيانات
echo "<div class='section info'>";
echo "<h2>1. قياس أداء قاعدة البيانات</h2>";

$start_time = microtime(true);
try {
    $database = new Database();
    $db = $database->connect();
    
    // اختبار استعلامات مختلفة
    $queries = [
        'المقالات' => "SELECT COUNT(*) FROM articles",
        'التصنيفات' => "SELECT COUNT(*) FROM categories",
        'المستخدمين' => "SELECT COUNT(*) FROM users"
    ];
    
    if (file_exists('includes/matches_functions.php')) {
        $queries['المباريات'] = "SELECT COUNT(*) FROM matches";
    }
    
    echo "<table>";
    echo "<tr><th>الاستعلام</th><th>الوقت (مللي ثانية)</th><th>الحالة</th></tr>";
    
    $total_db_time = 0;
    foreach ($queries as $name => $query) {
        $query_start = microtime(true);
        $stmt = $db->query($query);
        $result = $stmt->fetchColumn();
        $query_end = microtime(true);
        
        $query_time = round(($query_end - $query_start) * 1000, 2);
        $total_db_time += $query_time;
        
        $status = $query_time < 50 ? '✅ سريع' : ($query_time < 200 ? '⚠️ متوسط' : '❌ بطيء');
        $class = $query_time < 50 ? '' : ($query_time < 200 ? 'warning' : 'error');
        
        echo "<tr class='$class'>";
        echo "<td>$name ($result سجل)</td>";
        echo "<td>$query_time</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $avg_db_time = $total_db_time / count($queries);
    echo "<p><strong>متوسط وقت الاستعلام:</strong> " . round($avg_db_time, 2) . " مللي ثانية</p>";
    
    if ($avg_db_time < 50) {
        $performance_score += 2;
        echo "<p style='color: green;'>✅ أداء قاعدة البيانات ممتاز</p>";
    } elseif ($avg_db_time < 200) {
        $performance_score += 1;
        echo "<p style='color: orange;'>⚠️ أداء قاعدة البيانات متوسط</p>";
        $optimizations[] = "تحسين استعلامات قاعدة البيانات";
    } else {
        echo "<p style='color: red;'>❌ أداء قاعدة البيانات بطيء</p>";
        $optimizations[] = "تحسين استعلامات قاعدة البيانات بشكل عاجل";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

$end_time = microtime(true);
$total_time = round(($end_time - $start_time) * 1000, 2);
echo "<p><strong>إجمالي وقت اختبار قاعدة البيانات:</strong> $total_time مللي ثانية</p>";
echo "</div>";

// 2. فحص حجم الملفات
echo "<div class='section info'>";
echo "<h2>2. فحص حجم الملفات</h2>";

$files_to_check = [
    'index.php' => 'الصفحة الرئيسية',
    'matches.php' => 'صفحة المباريات',
    'includes/functions.php' => 'دوال النظام',
    'includes/matches_functions.php' => 'دوال المباريات',
    'assets/css/homepage-enhancements.css' => 'ملف CSS المحسن'
];

echo "<table>";
echo "<tr><th>الملف</th><th>الحجم</th><th>الحالة</th><th>التوصية</th></tr>";

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        $size_kb = round($size / 1024, 2);
        
        $status = '';
        $recommendation = '';
        $class = '';
        
        if (strpos($file, '.php') !== false) {
            if ($size_kb < 50) {
                $status = '✅ مناسب';
                $performance_score += 0.25;
            } elseif ($size_kb < 100) {
                $status = '⚠️ كبير';
                $recommendation = 'تحسين الكود';
                $class = 'warning';
            } else {
                $status = '❌ كبير جداً';
                $recommendation = 'تقسيم الملف';
                $class = 'error';
                $optimizations[] = "تحسين حجم ملف $description";
            }
        } elseif (strpos($file, '.css') !== false) {
            if ($size_kb < 20) {
                $status = '✅ مناسب';
                $performance_score += 0.25;
            } elseif ($size_kb < 50) {
                $status = '⚠️ كبير';
                $recommendation = 'ضغط CSS';
                $class = 'warning';
            } else {
                $status = '❌ كبير جداً';
                $recommendation = 'تحسين وضغط';
                $class = 'error';
                $optimizations[] = "ضغط ملف CSS";
            }
        }
        
        echo "<tr class='$class'>";
        echo "<td>$description</td>";
        echo "<td>{$size_kb} KB</td>";
        echo "<td>$status</td>";
        echo "<td>$recommendation</td>";
        echo "</tr>";
    } else {
        echo "<tr>";
        echo "<td>$description</td>";
        echo "<td>غير موجود</td>";
        echo "<td>❌ مفقود</td>";
        echo "<td>إنشاء الملف</td>";
        echo "</tr>";
    }
}
echo "</table>";
echo "</div>";

// 3. فحص استخدام الذاكرة
echo "<div class='section info'>";
echo "<h2>3. فحص استخدام الذاكرة</h2>";

$memory_usage = memory_get_usage(true);
$memory_peak = memory_get_peak_usage(true);
$memory_limit = ini_get('memory_limit');

$memory_mb = round($memory_usage / 1024 / 1024, 2);
$peak_mb = round($memory_peak / 1024 / 1024, 2);

echo "<table>";
echo "<tr><th>المقياس</th><th>القيمة</th><th>الحالة</th></tr>";
echo "<tr><td>الاستخدام الحالي</td><td>{$memory_mb} MB</td><td>" . ($memory_mb < 32 ? '✅ جيد' : '⚠️ مرتفع') . "</td></tr>";
echo "<tr><td>الذروة</td><td>{$peak_mb} MB</td><td>" . ($peak_mb < 64 ? '✅ جيد' : '⚠️ مرتفع') . "</td></tr>";
echo "<tr><td>الحد الأقصى</td><td>$memory_limit</td><td>ℹ️ إعداد الخادم</td></tr>";
echo "</table>";

if ($memory_mb < 32) {
    $performance_score += 1;
    echo "<p style='color: green;'>✅ استخدام الذاكرة ممتاز</p>";
} else {
    echo "<p style='color: orange;'>⚠️ استخدام الذاكرة مرتفع</p>";
    $optimizations[] = "تحسين استخدام الذاكرة";
}
echo "</div>";

// 4. فحص سرعة تحميل الصفحات
echo "<div class='section info'>";
echo "<h2>4. فحص سرعة تحميل الصفحات</h2>";

$pages_to_test = [
    'index.php' => 'الصفحة الرئيسية',
    'matches.php' => 'صفحة المباريات'
];

echo "<table>";
echo "<tr><th>الصفحة</th><th>وقت التحميل (مللي ثانية)</th><th>الحالة</th></tr>";

foreach ($pages_to_test as $page => $name) {
    $start = microtime(true);
    
    // محاكاة تحميل الصفحة
    ob_start();
    try {
        include $page;
        $content = ob_get_contents();
    } catch (Exception $e) {
        $content = '';
    }
    ob_end_clean();
    
    $end = microtime(true);
    $load_time = round(($end - $start) * 1000, 2);
    
    $status = '';
    $class = '';
    if ($load_time < 500) {
        $status = '✅ سريع';
        $performance_score += 1;
    } elseif ($load_time < 1000) {
        $status = '⚠️ متوسط';
        $class = 'warning';
        $performance_score += 0.5;
    } else {
        $status = '❌ بطيء';
        $class = 'error';
        $optimizations[] = "تحسين سرعة تحميل $name";
    }
    
    echo "<tr class='$class'>";
    echo "<td>$name</td>";
    echo "<td>$load_time</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 5. فحص التخزين المؤقت
echo "<div class='section info'>";
echo "<h2>5. فحص التخزين المؤقت</h2>";

$cache_headers = [
    'Cache-Control' => 'تحكم التخزين المؤقت',
    'Expires' => 'تاريخ انتهاء الصلاحية',
    'ETag' => 'علامة الكيان',
    'Last-Modified' => 'آخر تعديل'
];

echo "<p>فحص رؤوس التخزين المؤقت:</p>";
echo "<table>";
echo "<tr><th>الرأس</th><th>الحالة</th><th>التوصية</th></tr>";

$cache_score = 0;
foreach ($cache_headers as $header => $description) {
    $exists = false; // محاكاة فحص الرؤوس
    
    if ($exists) {
        echo "<tr><td>$description</td><td style='color: green;'>✅ موجود</td><td>-</td></tr>";
        $cache_score++;
    } else {
        echo "<tr><td>$description</td><td style='color: red;'>❌ غير موجود</td><td>إضافة الرأس</td></tr>";
    }
}
echo "</table>";

if ($cache_score > 2) {
    $performance_score += 1;
    echo "<p style='color: green;'>✅ التخزين المؤقت محسن</p>";
} else {
    echo "<p style='color: orange;'>⚠️ التخزين المؤقت يحتاج تحسين</p>";
    $optimizations[] = "تحسين إعدادات التخزين المؤقت";
}
echo "</div>";

// 6. حساب النتيجة النهائية
$final_score = round(($performance_score / $total_tests) * 100);

echo "<div class='section " . ($final_score > 80 ? 'success' : ($final_score > 60 ? 'warning' : 'error')) . "'>";
echo "<h2>📊 النتيجة النهائية</h2>";

echo "<div class='progress-bar'>";
echo "<div class='progress-fill' style='width: {$final_score}%'></div>";
echo "</div>";
echo "<p style='text-align: center; font-size: 24px; font-weight: bold; margin: 20px 0;'>";
echo "$final_score / 100";
echo "</p>";

if ($final_score > 80) {
    echo "<p style='color: green; font-weight: bold;'>🎉 أداء ممتاز! الموقع محسن بشكل جيد</p>";
} elseif ($final_score > 60) {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ أداء جيد ولكن يحتاج بعض التحسينات</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ الأداء يحتاج تحسين عاجل</p>";
}
echo "</div>";

// 7. توصيات التحسين
if (!empty($optimizations)) {
    echo "<div class='section warning'>";
    echo "<h2>🔧 توصيات التحسين</h2>";
    echo "<ol>";
    foreach ($optimizations as $optimization) {
        echo "<li>$optimization</li>";
    }
    echo "</ol>";
    echo "</div>";
}

// 8. خطة التحسين
echo "<div class='section info'>";
echo "<h2>📋 خطة التحسين</h2>";
echo "<h3>تحسينات فورية:</h3>";
echo "<ul>";
echo "<li>ضغط ملفات CSS و JavaScript</li>";
echo "<li>تحسين الصور (WebP, ضغط)</li>";
echo "<li>إضافة رؤوس التخزين المؤقت</li>";
echo "<li>تحسين استعلامات قاعدة البيانات</li>";
echo "</ul>";

echo "<h3>تحسينات متقدمة:</h3>";
echo "<ul>";
echo "<li>استخدام CDN للملفات الثابتة</li>";
echo "<li>تطبيق Lazy Loading للصور</li>";
echo "<li>تحسين خادم قاعدة البيانات</li>";
echo "<li>استخدام Redis للتخزين المؤقت</li>";
echo "</ul>";
echo "</div>";

// 9. أدوات التحسين
echo "<div class='section success'>";
echo "<h2>🛠️ أدوات التحسين</h2>";
echo "<a href='compress-assets.php' class='btn btn-primary'>ضغط الملفات</a>";
echo "<a href='optimize-database.php' class='btn btn-warning'>تحسين قاعدة البيانات</a>";
echo "<a href='cache-setup.php' class='btn btn-success'>إعداد التخزين المؤقت</a>";
echo "<a href='test-responsive.php' class='btn btn-danger'>اختبار الأداء مرة أخرى</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
