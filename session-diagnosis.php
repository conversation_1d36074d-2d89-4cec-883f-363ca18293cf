<?php
/**
 * تشخيص شامل لمشاكل PHP Session والـ Headers
 */

// تعطيل عرض الأخطاء مؤقتاً لتجنب تداخل الإخراج
error_reporting(0);
ini_set('display_errors', 0);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تشخيص مشاكل PHP Session</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo "table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo "th, td { padding: 12px; text-align: right; border: 1px solid #ddd; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; border-left: 4px solid #007bff; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 تشخيص شامل لمشاكل PHP Session والـ Headers</h1>";

// 1. فحص حالة الـ Headers
echo "<div class='section info'>";
echo "<h2>1. فحص حالة الـ Headers</h2>";

$headers_sent = headers_sent($file, $line);
if ($headers_sent) {
    echo "<p style='color: red;'><strong>❌ تم إرسال الـ Headers بالفعل!</strong></p>";
    echo "<p><strong>الملف:</strong> $file</p>";
    echo "<p><strong>السطر:</strong> $line</p>";
} else {
    echo "<p style='color: green;'><strong>✅ لم يتم إرسال الـ Headers بعد</strong></p>";
}

echo "<p><strong>معلومات الـ Headers:</strong></p>";
echo "<ul>";
echo "<li>headers_sent(): " . ($headers_sent ? 'true' : 'false') . "</li>";
echo "<li>ob_get_level(): " . ob_get_level() . "</li>";
echo "<li>ob_get_length(): " . (ob_get_length() ?: '0') . "</li>";
echo "</ul>";
echo "</div>";

// 2. فحص حالة الجلسة
echo "<div class='section info'>";
echo "<h2>2. فحص حالة الجلسة</h2>";

echo "<p><strong>معلومات الجلسة:</strong></p>";
echo "<ul>";
echo "<li>session_status(): " . session_status() . " (" . 
    (session_status() == PHP_SESSION_NONE ? 'لم تبدأ' : 
    (session_status() == PHP_SESSION_ACTIVE ? 'نشطة' : 'معطلة')) . ")</li>";
echo "<li>session_id(): " . (session_id() ?: 'غير محدد') . "</li>";
echo "<li>session_name(): " . session_name() . "</li>";
echo "<li>session_save_path(): " . session_save_path() . "</li>";
echo "</ul>";

echo "<p><strong>إعدادات الجلسة:</strong></p>";
echo "<ul>";
echo "<li>session.auto_start: " . ini_get('session.auto_start') . "</li>";
echo "<li>session.use_cookies: " . ini_get('session.use_cookies') . "</li>";
echo "<li>session.cookie_lifetime: " . ini_get('session.cookie_lifetime') . "</li>";
echo "<li>session.gc_maxlifetime: " . ini_get('session.gc_maxlifetime') . "</li>";
echo "</ul>";
echo "</div>";

// 3. فحص الملفات للبحث عن مصادر الإخراج المبكر
echo "<div class='section warning'>";
echo "<h2>3. فحص الملفات للبحث عن مصادر الإخراج المبكر</h2>";

$files_to_check = [
    'config/config.php' => 'ملف التكوين الرئيسي',
    'includes/header.php' => 'ملف الهيدر',
    'includes/footer.php' => 'ملف الفوتر',
    'includes/functions.php' => 'ملف الدوال',
    'index.php' => 'الصفحة الرئيسية'
];

echo "<table>";
echo "<tr><th>الملف</th><th>حجم الملف</th><th>يبدأ بـ PHP</th><th>BOM</th><th>مسافات قبل PHP</th><th>الحالة</th></tr>";

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $size = strlen($content);
        
        // فحص BOM
        $has_bom = (substr($content, 0, 3) === "\xEF\xBB\xBF");
        
        // فحص إذا كان يبدأ بـ PHP
        $starts_with_php = (substr(ltrim($content), 0, 5) === '<?php');
        
        // فحص المسافات قبل PHP
        $whitespace_before = '';
        if (preg_match('/^(\s*)(<\?php)/s', $content, $matches)) {
            $whitespace_before = $matches[1];
        }
        
        $status = '';
        $class = '';
        
        if ($has_bom) {
            $status = '❌ يحتوي على BOM';
            $class = 'error';
        } elseif (!empty($whitespace_before)) {
            $status = '⚠️ مسافات قبل PHP';
            $class = 'warning';
        } elseif (!$starts_with_php && $file !== 'includes/header.php') {
            $status = '⚠️ لا يبدأ بـ PHP';
            $class = 'warning';
        } else {
            $status = '✅ سليم';
            $class = 'success';
        }
        
        echo "<tr class='$class'>";
        echo "<td>$description</td>";
        echo "<td>" . round($size / 1024, 2) . " KB</td>";
        echo "<td>" . ($starts_with_php ? 'نعم' : 'لا') . "</td>";
        echo "<td>" . ($has_bom ? 'نعم' : 'لا') . "</td>";
        echo "<td>" . (empty($whitespace_before) ? 'لا' : 'نعم (' . strlen($whitespace_before) . ' حرف)') . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    } else {
        echo "<tr>";
        echo "<td>$description</td>";
        echo "<td colspan='5'>❌ الملف غير موجود</td>";
        echo "</tr>";
    }
}
echo "</table>";
echo "</div>";

// 4. فحص تفصيلي لملف config.php
echo "<div class='section error'>";
echo "<h2>4. فحص تفصيلي لملف config.php</h2>";

if (file_exists('config/config.php')) {
    $config_content = file_get_contents('config/config.php');
    $config_lines = explode("\n", $config_content);
    
    echo "<p><strong>فحص الأسطر 95-98 (إعدادات الجلسة):</strong></p>";
    
    // عرض الأسطر المحددة
    echo "<div class='code'>";
    for ($i = 94; $i < 98 && $i < count($config_lines); $i++) {
        $line_num = $i + 1;
        $line_content = htmlspecialchars($config_lines[$i]);
        echo sprintf("%3d: %s\n", $line_num, $line_content);
    }
    echo "</div>";
    
    // البحث عن استدعاءات ini_set للجلسة
    $session_ini_calls = [];
    foreach ($config_lines as $line_num => $line) {
        if (strpos($line, 'ini_set') !== false && strpos($line, 'session') !== false) {
            $session_ini_calls[] = ($line_num + 1) . ': ' . trim($line);
        }
    }
    
    if (!empty($session_ini_calls)) {
        echo "<p><strong>استدعاءات ini_set للجلسة:</strong></p>";
        echo "<div class='code'>";
        foreach ($session_ini_calls as $call) {
            echo htmlspecialchars($call) . "\n";
        }
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>❌ ملف config.php غير موجود</p>";
}
echo "</div>";

// 5. فحص تفصيلي لملف index.php
echo "<div class='section error'>";
echo "<h2>5. فحص تفصيلي لملف index.php</h2>";

if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    $index_lines = explode("\n", $index_content);
    
    echo "<p><strong>فحص السطر 16 (session_start):</strong></p>";
    
    // عرض الأسطر حول السطر 16
    echo "<div class='code'>";
    for ($i = 13; $i < 20 && $i < count($index_lines); $i++) {
        $line_num = $i + 1;
        $line_content = htmlspecialchars($index_lines[$i]);
        $highlight = ($line_num == 16) ? ' <-- السطر المشكوك فيه' : '';
        echo sprintf("%3d: %s%s\n", $line_num, $line_content, $highlight);
    }
    echo "</div>";
    
    // البحث عن استدعاءات session_start
    $session_start_calls = [];
    foreach ($index_lines as $line_num => $line) {
        if (strpos($line, 'session_start') !== false) {
            $session_start_calls[] = ($line_num + 1) . ': ' . trim($line);
        }
    }
    
    if (!empty($session_start_calls)) {
        echo "<p><strong>استدعاءات session_start:</strong></p>";
        echo "<div class='code'>";
        foreach ($session_start_calls as $call) {
            echo htmlspecialchars($call) . "\n";
        }
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>❌ ملف index.php غير موجود</p>";
}
echo "</div>";

// 6. فحص ترتيب تحميل الملفات
echo "<div class='section warning'>";
echo "<h2>6. فحص ترتيب تحميل الملفات</h2>";

echo "<p><strong>الترتيب المتوقع لتحميل الملفات:</strong></p>";
echo "<ol>";
echo "<li>config/config.php (إعدادات الجلسة)</li>";
echo "<li>session_start()</li>";
echo "<li>includes/functions.php</li>";
echo "<li>includes/header.php (HTML output)</li>";
echo "</ol>";

echo "<p><strong>فحص الترتيب الحالي في index.php:</strong></p>";
if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    $includes = [];
    
    // البحث عن includes/requires
    if (preg_match_all('/(include|require)(_once)?\s*[\'"]([^\'"]+)[\'"]/', $index_content, $matches, PREG_OFFSET_CAPTURE)) {
        foreach ($matches[0] as $i => $match) {
            $line_num = substr_count(substr($index_content, 0, $match[1]), "\n") + 1;
            $includes[] = "السطر $line_num: " . trim($match[0]);
        }
    }
    
    // البحث عن session_start
    if (preg_match('/session_start\s*\(\s*\)/', $index_content, $match, PREG_OFFSET_CAPTURE)) {
        $line_num = substr_count(substr($index_content, 0, $match[0][1]), "\n") + 1;
        $includes[] = "السطر $line_num: session_start()";
    }
    
    if (!empty($includes)) {
        echo "<div class='code'>";
        foreach ($includes as $include) {
            echo htmlspecialchars($include) . "\n";
        }
        echo "</div>";
    }
}
echo "</div>";

// 7. توصيات الإصلاح
echo "<div class='section success'>";
echo "<h2>7. توصيات الإصلاح</h2>";
echo "<ul>";
echo "<li>🔧 <strong>إزالة BOM:</strong> إزالة Byte Order Mark من بداية الملفات</li>";
echo "<li>🧹 <strong>إزالة المسافات:</strong> إزالة أي مسافات أو أسطر فارغة قبل &lt;?php</li>";
echo "<li>📋 <strong>إعادة ترتيب الكود:</strong> وضع إعدادات الجلسة قبل أي إخراج</li>";
echo "<li>🔄 <strong>استخدام Output Buffering:</strong> تفعيل ob_start() لتجنب مشاكل الـ headers</li>";
echo "<li>⚡ <strong>تحسين الأداء:</strong> تحسين ترتيب تحميل الملفات</li>";
echo "</ul>";
echo "</div>";

// 8. أدوات الإصلاح
echo "<div class='section info'>";
echo "<h2>8. أدوات الإصلاح</h2>";
echo "<a href='fix-session-headers.php' class='btn btn-danger'>إصلاح مشاكل Session والـ Headers</a>";
echo "<a href='clean-bom-whitespace.php' class='btn btn-warning'>تنظيف BOM والمسافات</a>";
echo "<a href='reorganize-session-code.php' class='btn btn-primary'>إعادة تنظيم كود الجلسة</a>";
echo "<a href='test-session-functionality.php' class='btn btn-success'>اختبار وظائف الجلسة</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
