<?php
class Database {
    private $host = 'localhost';
    private $db_name = 'news_website';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function connect() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8")
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }
        
        return $this->conn;
    }
}

// إنشاء قاعدة البيانات والجداول
function createDatabase() {
    try {
        $pdo = new PDO("mysql:host=localhost", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS news_website CHARACTER SET utf8 COLLATE utf8_general_ci");
        $pdo->exec("USE news_website");
        
        // جدول التصنيفات
        $pdo->exec("CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // جدول مصادر RSS
        $pdo->exec("CREATE TABLE IF NOT EXISTS rss_sources (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            url VARCHAR(500) NOT NULL,
            category_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            last_fetched TIMESTAMP NULL,
            fetch_interval INT DEFAULT 3600,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        )");
        
        // جدول المقالات
        $pdo->exec("CREATE TABLE IF NOT EXISTS articles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(500) NOT NULL,
            slug VARCHAR(500) NOT NULL,
            content LONGTEXT NOT NULL,
            excerpt TEXT,
            image_url VARCHAR(500),
            source_url VARCHAR(500),
            author VARCHAR(200),
            category_id INT,
            rss_source_id INT,
            published_at TIMESTAMP,
            is_featured BOOLEAN DEFAULT FALSE,
            views INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
            FOREIGN KEY (rss_source_id) REFERENCES rss_sources(id) ON DELETE SET NULL,
            INDEX idx_published (published_at),
            INDEX idx_category (category_id),
            INDEX idx_featured (is_featured)
        )");
        
        // جدول المستخدمين الإداريين
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100),
            role ENUM('admin', 'editor') DEFAULT 'editor',
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // جدول الإعدادات
        $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إدراج البيانات الأولية
        insertInitialData($pdo);
        
        return true;
    } catch(PDOException $e) {
        echo "Database Error: " . $e->getMessage();
        return false;
    }
}

function insertInitialData($pdo) {
    // إدراج التصنيفات الأولية
    $categories = [
        ['name' => 'أخبار عامة', 'slug' => 'general-news', 'description' => 'الأخبار العامة والمتنوعة'],
        ['name' => 'تقنية', 'slug' => 'technology', 'description' => 'أخبار التقنية والتكنولوجيا'],
        ['name' => 'رياضة', 'slug' => 'sports', 'description' => 'الأخبار الرياضية'],
        ['name' => 'اقتصاد', 'slug' => 'economy', 'description' => 'الأخبار الاقتصادية'],
        ['name' => 'سياسة', 'slug' => 'politics', 'description' => 'الأخبار السياسية']
    ];
    
    foreach($categories as $category) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO categories (name, slug, description) VALUES (?, ?, ?)");
        $stmt->execute([$category['name'], $category['slug'], $category['description']]);
    }
    
    // إدراج مستخدم إداري افتراضي
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['admin', '<EMAIL>', $admin_password, 'مدير الموقع', 'admin']);
    
    // إدراج الإعدادات الأولية
    $settings = [
        ['setting_key' => 'site_name', 'setting_value' => 'موقع الأخبار', 'setting_type' => 'text', 'description' => 'اسم الموقع'],
        ['setting_key' => 'site_description', 'setting_value' => 'موقع إخباري شامل', 'setting_type' => 'text', 'description' => 'وصف الموقع'],
        ['setting_key' => 'articles_per_page', 'setting_value' => '12', 'setting_type' => 'number', 'description' => 'عدد المقالات في الصفحة'],
        ['setting_key' => 'auto_fetch_rss', 'setting_value' => 'true', 'setting_type' => 'boolean', 'description' => 'جلب RSS تلقائياً'],
        ['setting_key' => 'rss_fetch_interval', 'setting_value' => '3600', 'setting_type' => 'number', 'description' => 'فترة جلب RSS بالثواني']
    ];
    
    foreach($settings as $setting) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
        $stmt->execute([$setting['setting_key'], $setting['setting_value'], $setting['setting_type'], $setting['description']]);
    }
}
?>
