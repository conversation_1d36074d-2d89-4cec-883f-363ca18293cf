<?php
/**
 * تطبيق تحديثات التنقل على الملفات الموجودة
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تطبيق تحديثات التنقل</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo ".code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; border-left: 4px solid #007bff; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔄 تطبيق تحديثات التنقل</h1>";

$updates_applied = [];
$errors = [];

// 1. تحديث header.php لإضافة ملفات CSS و JS الجديدة
echo "<div class='section info'>";
echo "<h2>1. تحديث header.php</h2>";

if (file_exists('includes/header.php')) {
    $header_content = file_get_contents('includes/header.php');
    
    // التحقق من وجود الروابط
    $has_nav_css = strpos($header_content, 'navigation-enhancements.css') !== false;
    $has_nav_js = strpos($header_content, 'navigation-enhancements.js') !== false;
    
    if (!$has_nav_css || !$has_nav_js) {
        // إنشاء نسخة احتياطية
        copy('includes/header.php', 'includes/header.php.backup.' . date('Y-m-d-H-i-s'));
        
        // إضافة رابط CSS
        if (!$has_nav_css) {
            $css_link = "\n    <!-- Navigation Enhancements CSS -->\n    <link rel=\"stylesheet\" href=\"assets/css/navigation-enhancements.css\">";
            
            // البحث عن مكان إدراج CSS
            if (strpos($header_content, '</head>') !== false) {
                $header_content = str_replace('</head>', $css_link . "\n</head>", $header_content);
                $updates_applied[] = "إضافة رابط CSS للتنقل";
            }
        }
        
        // إضافة رابط JavaScript
        if (!$has_nav_js) {
            $js_link = "\n    <!-- Navigation Enhancements JavaScript -->\n    <script src=\"assets/js/navigation-enhancements.js\"></script>";
            
            // البحث عن مكان إدراج JS
            if (strpos($header_content, '</body>') !== false) {
                $header_content = str_replace('</body>', $js_link . "\n</body>", $header_content);
                $updates_applied[] = "إضافة رابط JavaScript للتنقل";
            } elseif (strpos($header_content, '</html>') !== false) {
                $header_content = str_replace('</html>', $js_link . "\n</html>", $header_content);
                $updates_applied[] = "إضافة رابط JavaScript للتنقل";
            }
        }
        
        // حفظ الملف المحدث
        if (file_put_contents('includes/header.php', $header_content)) {
            echo "<p style='color: green;'>✅ تم تحديث header.php بنجاح</p>";
        } else {
            $errors[] = "فشل في تحديث header.php";
            echo "<p style='color: red;'>❌ فشل في تحديث header.php</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ الروابط موجودة بالفعل في header.php</p>";
    }
} else {
    $errors[] = "ملف header.php غير موجود";
    echo "<p style='color: red;'>❌ ملف header.php غير موجود</p>";
}
echo "</div>";

// 2. تحديث التنقل الرئيسي
echo "<div class='section info'>";
echo "<h2>2. تحديث التنقل الرئيسي</h2>";

if (file_exists('includes/header.php')) {
    $header_content = file_get_contents('includes/header.php');
    
    // البحث عن التنقل الحالي وتحديثه
    $old_nav_pattern = '/<nav class="border-t border-gray-200">/';
    $new_nav = '<nav class="main-navigation border-t border-gray-200">';
    
    if (preg_match($old_nav_pattern, $header_content)) {
        $header_content = preg_replace($old_nav_pattern, $new_nav, $header_content);
        
        // تحديث قائمة التنقل
        $old_ul_pattern = '/<ul id="main-nav" class="hidden md:flex space-x-8 space-x-reverse">/';
        $new_ul = '<ul class="nav-menu hidden md:flex space-x-8 space-x-reverse">';
        
        $header_content = preg_replace($old_ul_pattern, $new_ul, $header_content);
        
        // تحديث روابط التنقل
        $header_content = str_replace('class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-blue-600"', 'class="nav-link"', $header_content);
        
        // إضافة زر التنقل للموبايل
        $mobile_btn_pattern = '/<button id="mobile-menu-btn" class="md:hidden text-gray-600 hover:text-gray-800">/';
        $new_mobile_btn = '<button class="mobile-nav-toggle md:hidden">';
        
        $header_content = preg_replace($mobile_btn_pattern, $new_mobile_btn, $header_content);
        
        // حفظ التحديثات
        if (file_put_contents('includes/header.php', $header_content)) {
            $updates_applied[] = "تحديث كلاسات التنقل";
            echo "<p style='color: green;'>✅ تم تحديث التنقل الرئيسي</p>";
        } else {
            $errors[] = "فشل في تحديث التنقل";
            echo "<p style='color: red;'>❌ فشل في تحديث التنقل</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لم يتم العثور على التنقل للتحديث</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف header.php غير موجود</p>";
}
echo "</div>";

// 3. إضافة زر العودة للأعلى
echo "<div class='section info'>";
echo "<h2>3. إضافة زر العودة للأعلى</h2>";

// إضافة زر العودة للأعلى في index.php
if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    
    // التحقق من وجود الزر
    $has_back_to_top = strpos($index_content, 'back-to-top') !== false;
    
    if (!$has_back_to_top) {
        // إنشاء نسخة احتياطية
        copy('index.php', 'index.php.backup.' . date('Y-m-d-H-i-s'));
        
        // إضافة زر العودة للأعلى قبل إغلاق body
        $back_to_top_html = "\n<!-- Back to Top Button -->\n<button class=\"back-to-top\" aria-label=\"العودة للأعلى\">\n    <i class=\"fas fa-chevron-up\"></i>\n</button>\n";
        
        if (strpos($index_content, '</body>') !== false) {
            $index_content = str_replace('</body>', $back_to_top_html . '</body>', $index_content);
            
            if (file_put_contents('index.php', $index_content)) {
                $updates_applied[] = "إضافة زر العودة للأعلى";
                echo "<p style='color: green;'>✅ تم إضافة زر العودة للأعلى في index.php</p>";
            } else {
                $errors[] = "فشل في إضافة زر العودة للأعلى";
                echo "<p style='color: red;'>❌ فشل في إضافة زر العودة للأعلى</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لم يتم العثور على علامة </body> في index.php</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ زر العودة للأعلى موجود بالفعل</p>";
    }
} else {
    $errors[] = "ملف index.php غير موجود";
    echo "<p style='color: red;'>❌ ملف index.php غير موجود</p>";
}

// إضافة الزر في صفحات أخرى
$pages_to_update = ['matches.php', 'article.php', 'category.php', 'search.php'];

foreach ($pages_to_update as $page) {
    if (file_exists($page)) {
        $page_content = file_get_contents($page);
        $has_back_to_top = strpos($page_content, 'back-to-top') !== false;
        
        if (!$has_back_to_top && strpos($page_content, '</body>') !== false) {
            copy($page, $page . '.backup.' . date('Y-m-d-H-i-s'));
            
            $back_to_top_html = "\n<!-- Back to Top Button -->\n<button class=\"back-to-top\" aria-label=\"العودة للأعلى\">\n    <i class=\"fas fa-chevron-up\"></i>\n</button>\n";
            
            $page_content = str_replace('</body>', $back_to_top_html . '</body>', $page_content);
            
            if (file_put_contents($page, $page_content)) {
                $updates_applied[] = "إضافة زر العودة للأعلى في $page";
                echo "<p style='color: green;'>✅ تم إضافة زر العودة للأعلى في $page</p>";
            }
        }
    }
}
echo "</div>";

// 4. إنشاء ملف اختبار للتنقل
echo "<div class='section info'>";
echo "<h2>4. إنشاء ملف اختبار للتنقل</h2>";

$test_navigation_content = '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التنقل المحسن</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/navigation-enhancements.css">
    <style>
        body { font-family: \'Segoe UI\', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { min-height: 100vh; padding: 50px 20px; }
        .test-content { max-width: 800px; margin: 0 auto; text-align: center; }
    </style>
</head>
<body>

<!-- Test Navigation -->
<nav class="main-navigation">
    <div class="nav-container">
        <div class="flex justify-between items-center py-4">
            <div class="flex items-center">
                <h1 class="text-xl font-bold text-white">موقع الأخبار</h1>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link active">
                        <i class="fas fa-home"></i>الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#news" class="nav-link">
                        <i class="fas fa-newspaper"></i>الأخبار
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#matches" class="nav-link">
                        <i class="fas fa-futbol"></i>المباريات
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#sports" class="nav-link">
                        <i class="fas fa-trophy"></i>الرياضة
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">
                        <i class="fas fa-envelope"></i>اتصل بنا
                    </a>
                </li>
            </ul>
            
            <button class="mobile-nav-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </div>
</nav>

<!-- Test Sections -->
<section id="home" class="test-section bg-blue-50">
    <div class="test-content">
        <h2 class="text-4xl font-bold mb-6">🏠 الصفحة الرئيسية</h2>
        <p class="text-lg text-gray-600 mb-8">اختبار التنقل المحسن مع تأثيرات التمرير</p>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <i class="fas fa-rocket text-3xl text-blue-500 mb-4"></i>
                <h3 class="text-xl font-bold mb-2">سرعة عالية</h3>
                <p>تحميل سريع وأداء محسن</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <i class="fas fa-mobile-alt text-3xl text-green-500 mb-4"></i>
                <h3 class="text-xl font-bold mb-2">تصميم متجاوب</h3>
                <p>يعمل على جميع الأجهزة</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <i class="fas fa-shield-alt text-3xl text-purple-500 mb-4"></i>
                <h3 class="text-xl font-bold mb-2">آمان عالي</h3>
                <p>حماية متقدمة للبيانات</p>
            </div>
        </div>
    </div>
</section>

<section id="news" class="test-section bg-green-50">
    <div class="test-content">
        <h2 class="text-4xl font-bold mb-6">📰 الأخبار</h2>
        <p class="text-lg text-gray-600 mb-8">أحدث الأخبار والمستجدات</p>
        <div class="space-y-4">
            <div class="bg-white p-6 rounded-lg shadow-lg text-right">
                <h3 class="text-xl font-bold mb-2">خبر مهم جداً</h3>
                <p class="text-gray-600">تفاصيل الخبر هنا...</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg text-right">
                <h3 class="text-xl font-bold mb-2">خبر آخر</h3>
                <p class="text-gray-600">تفاصيل الخبر هنا...</p>
            </div>
        </div>
    </div>
</section>

<section id="matches" class="test-section bg-yellow-50">
    <div class="test-content">
        <h2 class="text-4xl font-bold mb-6">⚽ المباريات</h2>
        <p class="text-lg text-gray-600 mb-8">مواعيد المباريات والنتائج</p>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-bold mb-4">المباريات القادمة</h3>
            <div class="space-y-3">
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span>الأهلي vs الزمالك</span>
                    <span class="text-blue-600 font-bold">20:00</span>
                </div>
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span>برشلونة vs ريال مدريد</span>
                    <span class="text-blue-600 font-bold">22:00</span>
                </div>
            </div>
        </div>
    </div>
</section>

<section id="sports" class="test-section bg-purple-50">
    <div class="test-content">
        <h2 class="text-4xl font-bold mb-6">🏆 الرياضة</h2>
        <p class="text-lg text-gray-600 mb-8">أخبار رياضية متنوعة</p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <i class="fas fa-futbol text-3xl text-green-500 mb-4"></i>
                <h3 class="text-xl font-bold mb-2">كرة القدم</h3>
                <p>أحدث أخبار كرة القدم</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <i class="fas fa-basketball-ball text-3xl text-orange-500 mb-4"></i>
                <h3 class="text-xl font-bold mb-2">كرة السلة</h3>
                <p>أحدث أخبار كرة السلة</p>
            </div>
        </div>
    </div>
</section>

<section id="contact" class="test-section bg-red-50">
    <div class="test-content">
        <h2 class="text-4xl font-bold mb-6">📧 اتصل بنا</h2>
        <p class="text-lg text-gray-600 mb-8">نحن هنا للمساعدة</p>
        <div class="bg-white p-8 rounded-lg shadow-lg max-w-md mx-auto">
            <form class="space-y-4">
                <input type="text" placeholder="الاسم" class="w-full p-3 border rounded-lg">
                <input type="email" placeholder="البريد الإلكتروني" class="w-full p-3 border rounded-lg">
                <textarea placeholder="الرسالة" rows="4" class="w-full p-3 border rounded-lg"></textarea>
                <button type="submit" class="w-full bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700">
                    إرسال الرسالة
                </button>
            </form>
        </div>
    </div>
</section>

<!-- Back to Top Button -->
<button class="back-to-top" aria-label="العودة للأعلى">
    <i class="fas fa-chevron-up"></i>
</button>

<script src="assets/js/navigation-enhancements.js"></script>
</body>
</html>';

if (file_put_contents('test-navigation.php', $test_navigation_content)) {
    $updates_applied[] = "إنشاء ملف اختبار التنقل";
    echo "<p style='color: green;'>✅ تم إنشاء ملف test-navigation.php</p>";
} else {
    $errors[] = "فشل في إنشاء ملف اختبار التنقل";
    echo "<p style='color: red;'>❌ فشل في إنشاء ملف اختبار التنقل</p>";
}
echo "</div>";

// 5. ملخص التحديثات
echo "<div class='section success'>";
echo "<h2>5. ملخص التحديثات</h2>";

if (!empty($updates_applied)) {
    echo "<h3>التحديثات المطبقة:</h3>";
    echo "<ul>";
    foreach ($updates_applied as $update) {
        echo "<li style='color: green;'>✅ $update</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: blue;'>ℹ️ لم يتم تطبيق تحديثات جديدة</p>";
}

if (!empty($errors)) {
    echo "<h3>الأخطاء:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>❌ $error</li>";
    }
    echo "</ul>";
}

echo "<h3>الملفات المحدثة:</h3>";
echo "<ul>";
echo "<li>📁 <strong>assets/css/navigation-enhancements.css</strong> - ملف CSS للتحسينات</li>";
echo "<li>📁 <strong>assets/js/navigation-enhancements.js</strong> - ملف JavaScript للتحسينات</li>";
echo "<li>📁 <strong>includes/header.php</strong> - تحديث الروابط والكلاسات</li>";
echo "<li>📁 <strong>index.php</strong> - إضافة زر العودة للأعلى</li>";
echo "<li>📁 <strong>test-navigation.php</strong> - ملف اختبار التنقل</li>";
echo "</ul>";
echo "</div>";

// 6. روابط الاختبار
echo "<div class='section info'>";
echo "<h2>6. اختبار النتائج</h2>";
echo "<div style='text-align: center;'>";
echo "<a href='test-navigation.php' target='_blank' class='btn btn-primary'>🧭 اختبار التنقل المحسن</a>";
echo "<a href='index.php' target='_blank' class='btn btn-success'>🏠 الصفحة الرئيسية</a>";
echo "<a href='matches.php' target='_blank' class='btn btn-warning'>⚽ صفحة المباريات</a>";
echo "<a href='final-report.php' target='_blank' class='btn btn-danger'>📊 التقرير النهائي</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
