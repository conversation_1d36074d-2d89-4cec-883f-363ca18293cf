<?php
/**
 * Session and Function Conflict Fix Tool
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

$action = $_GET['action'] ?? '';
$results = [];

function addResult($message, $success = true) {
    global $results;
    $results[] = [
        'message' => $message,
        'success' => $success
    ];
}

switch ($action) {
    case 'fix_all':
        // Fix all session and function conflicts
        
        // 1. Fix index.php session order
        if (file_exists('index.php')) {
            $content = file_get_contents('index.php');
            
            // Check if session_start is before config include
            if (strpos($content, 'session_start();') < strpos($content, 'config/config.php')) {
                $content = preg_replace(
                    '/session_start\(\);\s*\n\s*\/\/ تحديد المسار الجذر/',
                    '// تحديد المسار الجذر',
                    $content
                );
                
                $content = preg_replace(
                    '/(require_once \'includes\/functions\.php\';)\s*\n/',
                    "$1\n\n// بدء الجلسة بعد تحميل التكوين\nsession_start();\n",
                    $content
                );
                
                file_put_contents('index.php', $content);
                addResult("Fixed session order in index.php");
            } else {
                addResult("index.php session order already correct");
            }
        }
        
        // 2. Fix other main files
        $main_files = ['article.php', 'category.php', 'search.php'];
        foreach ($main_files as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                
                if (strpos($content, 'session_start();') !== false && 
                    strpos($content, 'session_start();') < strpos($content, 'config/config.php')) {
                    
                    $content = preg_replace(
                        '/session_start\(\);\s*\n/',
                        '',
                        $content
                    );
                    
                    $content = preg_replace(
                        '/(require_once \'includes\/functions\.php\';)\s*\n/',
                        "$1\n\n// بدء الجلسة بعد تحميل التكوين\nsession_start();\n",
                        $content
                    );
                    
                    file_put_contents($file, $content);
                    addResult("Fixed session order in $file");
                } else {
                    addResult("$file session order already correct or file doesn't use sessions");
                }
            }
        }
        
        // 3. Fix config.php session settings
        if (file_exists('config/config.php')) {
            $content = file_get_contents('config/config.php');
            
            if (strpos($content, 'ini_set(\'session.') !== false && 
                strpos($content, 'if (session_status() === PHP_SESSION_NONE)') === false) {
                
                $content = preg_replace(
                    '/\/\/ إعدادات الجلسة\s*\nini_set\(\'session\./',
                    "// إعدادات الجلسة (فقط إذا لم تبدأ الجلسة بعد)\nif (session_status() === PHP_SESSION_NONE) {\n    ini_set('session.",
                    $content
                );
                
                $content = preg_replace(
                    '/ini_set\(\'session\.cookie_secure\', isset\(\$_SERVER\[\'HTTPS\'\]\)\);/',
                    "ini_set('session.cookie_secure', isset(\$_SERVER['HTTPS']));\n}",
                    $content
                );
                
                file_put_contents('config/config.php', $content);
                addResult("Fixed session settings in config.php");
            } else {
                addResult("config.php session settings already correct");
            }
        }
        
        // 4. Remove duplicate functions from config.php
        if (file_exists('config/config.php')) {
            $content = file_get_contents('config/config.php');
            
            if (strpos($content, 'function sanitizeInput(') !== false) {
                // Remove all function definitions from config.php
                $content = preg_replace(
                    '/\/\/ دالة تنظيف المدخلات.*?(?=\/\/ تحميل ملف التكوين المخصص|$)/s',
                    "// ملاحظة: الدوال المساعدة موجودة في includes/functions.php\n// تم إزالة الدوال المكررة لتجنب تضارب التعريفات\n\n",
                    $content
                );
                
                file_put_contents('config/config.php', $content);
                addResult("Removed duplicate functions from config.php");
            } else {
                addResult("No duplicate functions found in config.php");
            }
        }
        
        addResult("All fixes applied successfully!", true);
        break;
        
    case 'check_issues':
        // Check for common issues
        
        // Check session order in main files
        $files_to_check = ['index.php', 'article.php', 'category.php', 'search.php'];
        foreach ($files_to_check as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                $session_pos = strpos($content, 'session_start()');
                $config_pos = strpos($content, 'config/config.php');
                
                if ($session_pos !== false && $config_pos !== false && $session_pos < $config_pos) {
                    addResult("❌ $file: session_start() called before config include", false);
                } else {
                    addResult("✅ $file: session order correct");
                }
            }
        }
        
        // Check for duplicate functions
        if (file_exists('config/config.php')) {
            $content = file_get_contents('config/config.php');
            if (strpos($content, 'function sanitizeInput(') !== false) {
                addResult("❌ config.php contains duplicate function definitions", false);
            } else {
                addResult("✅ config.php: no duplicate functions");
            }
        }
        
        // Check session settings
        if (file_exists('config/config.php')) {
            $content = file_get_contents('config/config.php');
            if (strpos($content, 'ini_set(\'session.') !== false && 
                strpos($content, 'session_status()') === false) {
                addResult("❌ config.php: session settings not protected", false);
            } else {
                addResult("✅ config.php: session settings protected");
            }
        }
        break;
        
    default:
        addResult("No action specified", false);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session & Function Conflict Fix</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold mb-4">🔧 Session & Function Conflict Fix Tool</h1>
            
            <?php if (!empty($results)): ?>
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-3">Results:</h2>
                    <div class="space-y-2">
                        <?php foreach ($results as $result): ?>
                            <div class="p-3 rounded <?php echo $result['success'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                <?php echo htmlspecialchars($result['message']); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <a href="?action=check_issues" class="bg-blue-600 text-white p-4 rounded-lg text-center hover:bg-blue-700 transition-colors">
                    🔍 Check for Issues
                </a>
                <a href="?action=fix_all" class="bg-green-600 text-white p-4 rounded-lg text-center hover:bg-green-700 transition-colors">
                    🔧 Fix All Issues
                </a>
            </div>
            
            <div class="bg-blue-50 p-4 rounded-lg mb-6">
                <h3 class="font-semibold text-blue-800 mb-2">Common Issues Fixed:</h3>
                <ul class="list-disc list-inside text-blue-700 space-y-1">
                    <li>Session configuration warnings</li>
                    <li>Function redeclaration errors</li>
                    <li>Include order problems</li>
                    <li>Duplicate function definitions</li>
                </ul>
            </div>
            
            <div class="flex space-x-4">
                <a href="minimal-test.php" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Run Minimal Test
                </a>
                <a href="debug-500.php" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    Full Diagnostic
                </a>
                <a href="index.php" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    Test Homepage
                </a>
            </div>
        </div>
    </div>
</body>
</html>
