<?php
/**
 * دوال المباريات
 * Functions for managing football matches
 */

/**
 * الحصول على جميع المباريات مع إمكانية الفلترة
 */
function getMatches($limit = 20, $offset = 0, $filters = []) {
    $database = new Database();
    $db = $database->connect();

    // تحويل limit و offset إلى integer لتجنب خطأ SQL
    $limit = (int)$limit;
    $offset = (int)$offset;

    $where_conditions = [];
    $params = [];

    // فلترة حسب الحالة
    if (isset($filters['status']) && !empty($filters['status'])) {
        $where_conditions[] = "status = ?";
        $params[] = $filters['status'];
    }

    // فلترة حسب البطولة
    if (isset($filters['competition']) && !empty($filters['competition'])) {
        $where_conditions[] = "competition = ?";
        $params[] = $filters['competition'];
    }

    // فلترة حسب التاريخ
    if (isset($filters['date_from']) && !empty($filters['date_from'])) {
        $where_conditions[] = "DATE(match_date) >= ?";
        $params[] = $filters['date_from'];
    }

    if (isset($filters['date_to']) && !empty($filters['date_to'])) {
        $where_conditions[] = "DATE(match_date) <= ?";
        $params[] = $filters['date_to'];
    }

    // فلترة حسب الفريق
    if (isset($filters['team']) && !empty($filters['team'])) {
        $where_conditions[] = "(home_team LIKE ? OR away_team LIKE ?)";
        $params[] = '%' . $filters['team'] . '%';
        $params[] = '%' . $filters['team'] . '%';
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    $sql = "SELECT * FROM matches
            $where_clause
            ORDER BY match_date DESC
            LIMIT $limit OFFSET $offset";

    $stmt = $db->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على المباريات القادمة
 */
function getUpcomingMatches($limit = 10) {
    $database = new Database();
    $db = $database->connect();

    // تحويل limit إلى integer لتجنب خطأ SQL
    $limit = (int)$limit;

    $sql = "SELECT * FROM matches
            WHERE match_date > NOW() AND status = 'scheduled'
            ORDER BY match_date ASC
            LIMIT " . $limit;

    $stmt = $db->prepare($sql);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على المباريات المنتهية
 */
function getFinishedMatches($limit = 10) {
    $database = new Database();
    $db = $database->connect();

    // تحويل limit إلى integer لتجنب خطأ SQL
    $limit = (int)$limit;

    $sql = "SELECT * FROM matches
            WHERE status = 'finished'
            ORDER BY match_date DESC
            LIMIT " . $limit;

    $stmt = $db->prepare($sql);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على المباريات الجارية
 */
function getLiveMatches() {
    $database = new Database();
    $db = $database->connect();
    
    $sql = "SELECT * FROM matches 
            WHERE status = 'live'
            ORDER BY match_date DESC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على المباريات المميزة
 */
function getFeaturedMatches($limit = 5) {
    $database = new Database();
    $db = $database->connect();

    // تحويل limit إلى integer لتجنب خطأ SQL
    $limit = (int)$limit;

    $sql = "SELECT * FROM matches
            WHERE is_featured = 1
            ORDER BY match_date DESC
            LIMIT " . $limit;

    $stmt = $db->prepare($sql);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على مباراة واحدة
 */
function getMatch($id) {
    $database = new Database();
    $db = $database->connect();
    
    $sql = "SELECT * FROM matches WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$id]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * إضافة مباراة جديدة
 */
function addMatch($data) {
    $database = new Database();
    $db = $database->connect();
    
    $sql = "INSERT INTO matches (home_team, away_team, home_team_logo, away_team_logo, 
            match_date, competition, venue, status, home_score, away_score, 
            match_time, description, is_featured) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $db->prepare($sql);
    return $stmt->execute([
        $data['home_team'],
        $data['away_team'],
        $data['home_team_logo'] ?? null,
        $data['away_team_logo'] ?? null,
        $data['match_date'],
        $data['competition'],
        $data['venue'] ?? null,
        $data['status'] ?? 'scheduled',
        $data['home_score'] ?? null,
        $data['away_score'] ?? null,
        $data['match_time'] ?? null,
        $data['description'] ?? null,
        $data['is_featured'] ?? 0
    ]);
}

/**
 * تحديث مباراة
 */
function updateMatch($id, $data) {
    $database = new Database();
    $db = $database->connect();
    
    $sql = "UPDATE matches SET 
            home_team = ?, away_team = ?, home_team_logo = ?, away_team_logo = ?,
            match_date = ?, competition = ?, venue = ?, status = ?, 
            home_score = ?, away_score = ?, match_time = ?, description = ?, 
            is_featured = ?, updated_at = NOW()
            WHERE id = ?";
    
    $stmt = $db->prepare($sql);
    return $stmt->execute([
        $data['home_team'],
        $data['away_team'],
        $data['home_team_logo'] ?? null,
        $data['away_team_logo'] ?? null,
        $data['match_date'],
        $data['competition'],
        $data['venue'] ?? null,
        $data['status'] ?? 'scheduled',
        $data['home_score'] ?? null,
        $data['away_score'] ?? null,
        $data['match_time'] ?? null,
        $data['description'] ?? null,
        $data['is_featured'] ?? 0,
        $id
    ]);
}

/**
 * حذف مباراة
 */
function deleteMatch($id) {
    $database = new Database();
    $db = $database->connect();
    
    $sql = "DELETE FROM matches WHERE id = ?";
    $stmt = $db->prepare($sql);
    return $stmt->execute([$id]);
}

/**
 * الحصول على جميع البطولات
 */
function getCompetitions() {
    $database = new Database();
    $db = $database->connect();
    
    $sql = "SELECT * FROM competitions WHERE is_active = 1 ORDER BY name";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على جميع الفرق
 */
function getTeams() {
    $database = new Database();
    $db = $database->connect();
    
    $sql = "SELECT * FROM teams WHERE is_active = 1 ORDER BY name";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * تنسيق تاريخ المباراة
 */
function formatMatchDate($date, $format = 'full') {
    $match_date = new DateTime($date);
    $now = new DateTime();
    
    if ($format === 'full') {
        return $match_date->format('Y-m-d H:i');
    } elseif ($format === 'date_only') {
        return $match_date->format('Y-m-d');
    } elseif ($format === 'time_only') {
        return $match_date->format('H:i');
    } elseif ($format === 'arabic') {
        $days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        $months = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
            5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
            9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
        ];
        
        $day_name = $days[$match_date->format('w')];
        $day = $match_date->format('j');
        $month = $months[(int)$match_date->format('n')];
        $year = $match_date->format('Y');
        $time = $match_date->format('H:i');
        
        return "$day_name $day $month $year - $time";
    }
    
    return $match_date->format('Y-m-d H:i');
}

/**
 * الحصول على حالة المباراة باللغة العربية
 */
function getMatchStatusArabic($status) {
    $statuses = [
        'scheduled' => 'مجدولة',
        'live' => 'جارية',
        'finished' => 'انتهت',
        'postponed' => 'مؤجلة',
        'cancelled' => 'ملغية'
    ];
    
    return $statuses[$status] ?? $status;
}

/**
 * الحصول على لون حالة المباراة
 */
function getMatchStatusColor($status) {
    $colors = [
        'scheduled' => 'bg-blue-100 text-blue-800',
        'live' => 'bg-red-100 text-red-800',
        'finished' => 'bg-green-100 text-green-800',
        'postponed' => 'bg-yellow-100 text-yellow-800',
        'cancelled' => 'bg-gray-100 text-gray-800'
    ];
    
    return $colors[$status] ?? 'bg-gray-100 text-gray-800';
}

/**
 * الحصول على عدد المباريات الإجمالي
 */
function getTotalMatches($filters = []) {
    $database = new Database();
    $db = $database->connect();
    
    $where_conditions = [];
    $params = [];
    
    if (isset($filters['status']) && !empty($filters['status'])) {
        $where_conditions[] = "status = ?";
        $params[] = $filters['status'];
    }
    
    if (isset($filters['competition']) && !empty($filters['competition'])) {
        $where_conditions[] = "competition = ?";
        $params[] = $filters['competition'];
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $sql = "SELECT COUNT(*) FROM matches $where_clause";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchColumn();
}

/**
 * البحث في المباريات
 */
function searchMatches($query, $limit = 20) {
    $database = new Database();
    $db = $database->connect();

    // تحويل limit إلى integer لتجنب خطأ SQL
    $limit = (int)$limit;

    $sql = "SELECT * FROM matches
            WHERE home_team LIKE ? OR away_team LIKE ? OR competition LIKE ? OR venue LIKE ?
            ORDER BY match_date DESC
            LIMIT " . $limit;

    $search_term = '%' . $query . '%';
    $stmt = $db->prepare($sql);
    $stmt->execute([$search_term, $search_term, $search_term, $search_term]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على إحصائيات المباريات
 */
function getMatchesStats() {
    $database = new Database();
    $db = $database->connect();
    
    $stats = [];
    
    // إجمالي المباريات
    $stmt = $db->query("SELECT COUNT(*) FROM matches");
    $stats['total'] = $stmt->fetchColumn();
    
    // المباريات القادمة
    $stmt = $db->query("SELECT COUNT(*) FROM matches WHERE match_date > NOW() AND status = 'scheduled'");
    $stats['upcoming'] = $stmt->fetchColumn();
    
    // المباريات المنتهية
    $stmt = $db->query("SELECT COUNT(*) FROM matches WHERE status = 'finished'");
    $stats['finished'] = $stmt->fetchColumn();
    
    // المباريات الجارية
    $stmt = $db->query("SELECT COUNT(*) FROM matches WHERE status = 'live'");
    $stats['live'] = $stmt->fetchColumn();
    
    // المباريات المميزة
    $stmt = $db->query("SELECT COUNT(*) FROM matches WHERE is_featured = 1");
    $stats['featured'] = $stmt->fetchColumn();
    
    return $stats;
}

/**
 * رفع شعار الفريق
 */
function uploadTeamLogo($file, $team_name) {
    $upload_dir = 'uploads/teams/';

    // إنشاء المجلد إذا لم يكن موجوداً
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

    if (!in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF أو WebP'];
    }

    if ($file['size'] > 2 * 1024 * 1024) { // 2MB
        return ['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت'];
    }

    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $safe_team_name = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $team_name);
    $filename = $safe_team_name . '_' . time() . '.' . $file_extension;
    $filepath = $upload_dir . $filename;

    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filepath' => $filepath];
    } else {
        return ['success' => false, 'message' => 'فشل في رفع الملف'];
    }
}

/**
 * الحصول على المباريات حسب التاريخ (لعرض التقويم)
 */
function getMatchesByDate($date) {
    $database = new Database();
    $db = $database->connect();

    $sql = "SELECT * FROM matches
            WHERE DATE(match_date) = ?
            ORDER BY match_date ASC";

    $stmt = $db->prepare($sql);
    $stmt->execute([$date]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على المباريات في فترة زمنية محددة
 */
function getMatchesInDateRange($start_date, $end_date) {
    $database = new Database();
    $db = $database->connect();

    $sql = "SELECT * FROM matches
            WHERE DATE(match_date) BETWEEN ? AND ?
            ORDER BY match_date ASC";

    $stmt = $db->prepare($sql);
    $stmt->execute([$start_date, $end_date]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
