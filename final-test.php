<?php
/**
 * Final Test - Verify all fixes are working
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define ROOT_PATH
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Test - All Fixes Verification</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-pass { color: #10B981; }
        .test-fail { color: #EF4444; }
        .test-warning { color: #F59E0B; }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">🎯 Final Test - All Fixes Verification</h1>
            
            <!-- Test 1: Config Loading -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Test 1: Configuration Loading</h2>
                <?php
                try {
                    include_once 'config/config.php';
                    echo "<p class='test-pass'>✅ Config loaded successfully</p>";
                    
                    if (defined('DEBUG_MODE')) {
                        echo "<p class='test-pass'>✅ DEBUG_MODE defined: " . (DEBUG_MODE ? 'true' : 'false') . "</p>";
                    }
                    
                    if (defined('SITE_URL')) {
                        echo "<p class='test-pass'>✅ SITE_URL defined: " . SITE_URL . "</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='test-fail'>❌ Config error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>

            <!-- Test 2: Functions Loading -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Test 2: Functions Loading</h2>
                <?php
                try {
                    include_once 'includes/functions.php';
                    echo "<p class='test-pass'>✅ Functions loaded successfully</p>";
                    
                    if (function_exists('getSetting')) {
                        echo "<p class='test-pass'>✅ getSetting function available</p>";
                        $site_name = getSetting('site_name', 'Default');
                        echo "<p class='test-pass'>✅ getSetting test: $site_name</p>";
                    }
                    
                    if (function_exists('sanitizeInput')) {
                        echo "<p class='test-pass'>✅ sanitizeInput function available</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='test-fail'>❌ Functions error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>

            <!-- Test 3: Session -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Test 3: Session Management</h2>
                <?php
                try {
                    if (session_status() === PHP_SESSION_NONE) {
                        session_start();
                    }
                    echo "<p class='test-pass'>✅ Session started successfully</p>";
                    echo "<p class='test-pass'>✅ Session ID: " . session_id() . "</p>";
                } catch (Exception $e) {
                    echo "<p class='test-fail'>❌ Session error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>

            <!-- Test 4: Database Connection -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Test 4: Database Connection</h2>
                <?php
                try {
                    include_once 'config/database.php';
                    
                    if (class_exists('Database')) {
                        echo "<p class='test-pass'>✅ Database class loaded</p>";
                        
                        $database = new Database();
                        $db = $database->connect();
                        
                        if ($db) {
                            echo "<p class='test-pass'>✅ Database connected successfully</p>";
                            
                            // Test query
                            $stmt = $db->query("SELECT 1 as test");
                            if ($stmt && $stmt->fetch()) {
                                echo "<p class='test-pass'>✅ Database query test successful</p>";
                            }
                        } else {
                            echo "<p class='test-warning'>⚠️ Database connection failed (run setup.php)</p>";
                        }
                    } else {
                        echo "<p class='test-fail'>❌ Database class not found</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='test-warning'>⚠️ Database error: " . $e->getMessage() . " (run setup.php)</p>";
                }
                ?>
            </div>

            <!-- Test 5: File Access -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Test 5: File Access Test</h2>
                <?php
                $test_urls = [
                    'index.php' => 'Homepage',
                    'setup.php' => 'Database Setup',
                    'admin/login.php' => 'Admin Login'
                ];

                foreach ($test_urls as $url => $name) {
                    if (file_exists($url)) {
                        echo "<p class='test-pass'>✅ $name file exists</p>";
                    } else {
                        echo "<p class='test-fail'>❌ $name file missing</p>";
                    }
                }
                ?>
            </div>

            <!-- Test 6: Error Log Check -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">Test 6: Error Status</h2>
                <?php
                $error_log = ini_get('error_log');
                if ($error_log && file_exists($error_log)) {
                    $recent_errors = file_get_contents($error_log);
                    $recent_lines = array_slice(explode("\n", $recent_errors), -5);
                    
                    $has_recent_errors = false;
                    foreach ($recent_lines as $line) {
                        if (strpos($line, date('Y-m-d')) !== false && 
                            (strpos($line, 'Fatal error') !== false || strpos($line, 'Warning') !== false)) {
                            $has_recent_errors = true;
                            break;
                        }
                    }
                    
                    if (!$has_recent_errors) {
                        echo "<p class='test-pass'>✅ No recent errors in log</p>";
                    } else {
                        echo "<p class='test-warning'>⚠️ Recent errors found in log</p>";
                    }
                } else {
                    echo "<p class='test-pass'>✅ No error log or no errors</p>";
                }
                ?>
            </div>

            <!-- Overall Status -->
            <div class="bg-green-50 p-4 rounded-lg mb-6">
                <h3 class="font-semibold text-green-800 mb-2">🎉 Overall Status</h3>
                <p class="text-green-700">
                    If all tests above show ✅ (green checkmarks), your website is ready to use!
                </p>
            </div>

            <!-- Next Steps -->
            <div class="bg-blue-50 p-4 rounded-lg mb-6">
                <h3 class="font-semibold text-blue-800 mb-2">📋 Next Steps</h3>
                <ol class="list-decimal list-inside text-blue-700 space-y-1">
                    <li>If database shows ⚠️, run <a href="setup.php" class="underline">setup.php</a></li>
                    <li>Test the <a href="index.php" class="underline">homepage</a></li>
                    <li>Access <a href="admin/login.php" class="underline">admin panel</a> (admin/admin123)</li>
                    <li>Add RSS sources and fetch news</li>
                    <li>Remove debug files when done</li>
                </ol>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                <a href="index.php" class="bg-blue-600 text-white p-3 rounded text-center hover:bg-blue-700">
                    🏠 Homepage
                </a>
                <a href="setup.php" class="bg-green-600 text-white p-3 rounded text-center hover:bg-green-700">
                    ⚙️ Setup DB
                </a>
                <a href="admin/login.php" class="bg-purple-600 text-white p-3 rounded text-center hover:bg-purple-700">
                    🔐 Admin
                </a>
                <button onclick="location.reload()" class="bg-gray-600 text-white p-3 rounded hover:bg-gray-700">
                    🔄 Refresh
                </button>
            </div>

            <!-- Cleanup Notice -->
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 class="font-semibold text-yellow-800 mb-2">🧹 Cleanup</h3>
                <p class="text-yellow-700 text-sm">
                    Once everything is working, you can delete these debug files:
                    <code>debug-500.php</code>, <code>minimal-test.php</code>, <code>fix-500-errors.php</code>, 
                    <code>fix-session-errors.php</code>, <code>final-test.php</code>, <code>troubleshoot-403.php</code>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
