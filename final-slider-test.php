<?php
/**
 * اختبار نهائي لسلايدر المباريات
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/matches_functions.php';

echo "<h1>🏆 اختبار نهائي لسلايدر المباريات</h1>";

// اختبار الاتصال بقاعدة البيانات
try {
    $database = new Database();
    $db = $database->connect();
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

// اختبار دالة getUpcomingMatches
try {
    $upcoming_matches = getUpcomingMatches(5);
    echo "<p>✅ دالة getUpcomingMatches تعمل بنجاح</p>";
    echo "<p><strong>عدد المباريات القادمة:</strong> " . count($upcoming_matches) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في دالة getUpcomingMatches: " . $e->getMessage() . "</p>";
    $upcoming_matches = [];
}

// اختبار شرط عرض السلايدر
if (!empty($upcoming_matches)) {
    echo "<p style='color: green; font-weight: bold;'>✅ السلايدر سيظهر في الصفحة الرئيسية</p>";
    
    $all_matches = array_slice($upcoming_matches, 0, 9);
    $total_slides = ceil(count($all_matches) / 3);
    
    echo "<p><strong>عدد المباريات للسلايدر:</strong> " . count($all_matches) . "</p>";
    echo "<p><strong>عدد الشرائح:</strong> " . $total_slides . "</p>";
    
    echo "<h2>📋 قائمة المباريات:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th style='padding: 10px; text-align: center;'>الفريق المضيف</th>";
    echo "<th style='padding: 10px; text-align: center;'>الفريق الضيف</th>";
    echo "<th style='padding: 10px; text-align: center;'>التاريخ</th>";
    echo "<th style='padding: 10px; text-align: center;'>البطولة</th>";
    echo "<th style='padding: 10px; text-align: center;'>مميزة</th>";
    echo "</tr>";
    
    foreach ($upcoming_matches as $match) {
        echo "<tr>";
        echo "<td style='padding: 8px; text-align: center;'>" . $match['home_team'] . "</td>";
        echo "<td style='padding: 8px; text-align: center;'>" . $match['away_team'] . "</td>";
        echo "<td style='padding: 8px; text-align: center;'>" . formatMatchDate($match['match_date'], 'date_only') . "</td>";
        echo "<td style='padding: 8px; text-align: center;'>" . $match['competition'] . "</td>";
        echo "<td style='padding: 8px; text-align: center;'>" . ($match['is_featured'] ? '⭐ نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ السلايدر لن يظهر - لا توجد مباريات قادمة</p>";
}

// اختبار دوال التنسيق
if (!empty($upcoming_matches)) {
    echo "<h2>🔧 اختبار دوال التنسيق:</h2>";
    $test_match = $upcoming_matches[0];
    
    try {
        $date_only = formatMatchDate($test_match['match_date'], 'date_only');
        $time_only = formatMatchDate($test_match['match_date'], 'time_only');
        $full_date = formatMatchDate($test_match['match_date'], 'full');
        
        echo "<p>✅ دالة formatMatchDate تعمل بنجاح</p>";
        echo "<ul>";
        echo "<li><strong>التاريخ فقط:</strong> " . $date_only . "</li>";
        echo "<li><strong>الوقت فقط:</strong> " . $time_only . "</li>";
        echo "<li><strong>التاريخ الكامل:</strong> " . $full_date . "</li>";
        echo "</ul>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في دالة formatMatchDate: " . $e->getMessage() . "</p>";
    }
}

// روابط الاختبار
echo "<h2>🔗 روابط الاختبار:</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='index.php' target='_blank' style='display: inline-block; background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية الأصلية</a>";
echo "<a href='index-simple.php' target='_blank' style='display: inline-block; background: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة المبسطة</a>";
echo "<a href='test-slider-only.php' target='_blank' style='display: inline-block; background: #8b5cf6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار السلايدر فقط</a>";
echo "<a href='matches.php' target='_blank' style='display: inline-block; background: #f59e0b; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المباريات</a>";
echo "</div>";

// خلاصة النتائج
echo "<h2>📊 خلاصة النتائج:</h2>";
echo "<div style='background: #f0f9ff; border: 2px solid #0ea5e9; padding: 20px; border-radius: 10px; margin: 20px 0;'>";

if (!empty($upcoming_matches)) {
    echo "<h3 style='color: #0ea5e9; margin-top: 0;'>✅ النتيجة: نجح الاختبار</h3>";
    echo "<ul style='color: #0c4a6e;'>";
    echo "<li>✅ قاعدة البيانات متصلة</li>";
    echo "<li>✅ دالة getUpcomingMatches تعمل</li>";
    echo "<li>✅ توجد " . count($upcoming_matches) . " مباريات قادمة</li>";
    echo "<li>✅ دوال التنسيق تعمل</li>";
    echo "<li>✅ السلايدر يجب أن يظهر في الصفحة الرئيسية</li>";
    echo "</ul>";
    
    echo "<p style='color: #0c4a6e; font-weight: bold; margin-bottom: 0;'>";
    echo "🎉 تهانينا! سلايدر المباريات جاهز للعمل. ";
    echo "إذا لم يظهر في الصفحة الرئيسية، فالمشكلة قد تكون في التخزين المؤقت للمتصفح أو في CSS/JavaScript.";
    echo "</p>";
} else {
    echo "<h3 style='color: #dc2626; margin-top: 0;'>❌ النتيجة: فشل الاختبار</h3>";
    echo "<ul style='color: #7f1d1d;'>";
    echo "<li>❌ لا توجد مباريات قادمة</li>";
    echo "<li>❌ السلايدر لن يظهر</li>";
    echo "</ul>";
    
    echo "<p style='color: #7f1d1d; font-weight: bold; margin-bottom: 0;'>";
    echo "⚠️ يرجى إضافة مباريات تجريبية باستخدام ملف add-sample-matches.php";
    echo "</p>";
}

echo "</div>";

// معلومات إضافية
echo "<h2>ℹ️ معلومات إضافية:</h2>";
echo "<ul>";
echo "<li><strong>التاريخ والوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "<li><strong>المنطقة الزمنية:</strong> " . date_default_timezone_get() . "</li>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>نظام التشغيل:</strong> " . PHP_OS . "</li>";
echo "</ul>";
?>
