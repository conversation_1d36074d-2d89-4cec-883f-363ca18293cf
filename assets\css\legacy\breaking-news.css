/* 
 * Breaking News Ticker Advanced Styles
 * ملف CSS متقدم لشريط الأخبار العاجلة
 */

/* الأنيميشن الأساسي للشريط */
@keyframes breakingNewsSlide {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* تأثيرات متقدمة للشريط */
.breaking-news-ticker {
    position: relative;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* تأثير الضوء المتحرك */
.breaking-news-ticker::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: lightSweep 4s infinite;
    z-index: 1;
}

@keyframes lightSweep {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* تسمية "عاجل" المحسنة */
.breaking-news-label {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #dc2626;
    border: 2px solid #dc2626;
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
    animation: labelPulse 2s infinite;
}

@keyframes labelPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 12px rgba(220, 38, 38, 0.4);
    }
}

/* تأثير البرق في الأيقونة */
.breaking-news-label .fa-bolt {
    animation: boltFlash 1.5s infinite;
    filter: drop-shadow(0 0 4px #fbbf24);
}

@keyframes boltFlash {
    0%, 100% {
        color: #dc2626;
        text-shadow: 0 0 4px #fbbf24;
    }
    50% {
        color: #fbbf24;
        text-shadow: 0 0 8px #f59e0b;
    }
}

/* النص المتحرك */
.news-marquee {
    animation: breakingNewsSlide 50s linear infinite;
    white-space: nowrap;
    will-change: transform;
}

/* تأثيرات الروابط */
.news-marquee a {
    display: inline-block;
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0 1rem;
}

.news-marquee a:hover {
    color: #fbbf24;
    text-shadow: 0 0 8px rgba(251, 191, 36, 0.6);
    transform: scale(1.05);
}

/* النقاط الفاصلة */
.news-marquee a .fa-circle {
    color: #fbbf24;
    animation: dotPulse 2s infinite;
    margin: 0 0.5rem;
}

@keyframes dotPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* أزرار التحكم */
.ticker-controls button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.ticker-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.ticker-controls button:active {
    transform: scale(0.95);
}

/* حالات مختلفة للشريط */
.breaking-news-ticker.paused .news-marquee {
    animation-play-state: paused;
}

.breaking-news-ticker.hover-paused .news-marquee {
    animation-play-state: paused;
}

/* تأثير الإغلاق */
.breaking-news-ticker.closing {
    animation: slideUp 0.5s ease-out forwards;
}

@keyframes slideUp {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-100%);
        opacity: 0;
    }
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .breaking-news-ticker {
        font-size: 0.875rem;
    }
    
    .breaking-news-label {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .news-marquee {
        animation-duration: 40s;
    }
    
    .news-marquee a {
        padding: 0 0.5rem;
    }
    
    .ticker-controls button {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .breaking-news-ticker {
        font-size: 0.8rem;
    }
    
    .breaking-news-label span {
        display: none;
    }
    
    .breaking-news-label .fa-bolt {
        margin: 0;
    }
    
    .news-marquee {
        animation-duration: 35s;
    }
}

/* تحسينات الأداء */
.breaking-news-ticker,
.news-marquee,
.breaking-news-label {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تأثيرات إضافية للتفاعل */
.breaking-news-ticker:hover::before {
    animation-duration: 2s;
}

/* حالة التحميل */
.breaking-news-ticker.loading .news-marquee {
    opacity: 0.5;
    animation-duration: 60s;
}

.breaking-news-ticker.loading .breaking-news-label {
    animation: labelLoading 1s infinite;
}

@keyframes labelLoading {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* تأثيرات خاصة للأخبار العاجلة */
.urgent-news {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
}

.urgent-news::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255, 255, 255, 0.1) 10px,
        rgba(255, 255, 255, 0.1) 20px
    );
    animation: urgentPattern 2s linear infinite;
}

@keyframes urgentPattern {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(20px);
    }
}

/* تحسينات إضافية للوضوح */
.breaking-news-ticker .container {
    position: relative;
    z-index: 2;
}

/* تأثير التدرج في الأطراف */
.news-content::before,
.news-content::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 50px;
    z-index: 3;
    pointer-events: none;
}

.news-content::before {
    left: 0;
    background: linear-gradient(to right, #dc2626, transparent);
}

.news-content::after {
    right: 0;
    background: linear-gradient(to left, #dc2626, transparent);
}
