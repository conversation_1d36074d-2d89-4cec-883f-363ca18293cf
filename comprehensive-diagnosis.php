<?php
/**
 * تشخيص شامل لمشاكل موقع الأخبار
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تشخيص شامل للموقع</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo "th, td { padding: 12px; text-align: right; border: 1px solid #ddd; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo "h3 { color: #2980b9; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-warning { background: #f39c12; }";
echo ".btn-danger { background: #e74c3c; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 تشخيص شامل لموقع الأخبار</h1>";

// 1. فحص الاتصال بقاعدة البيانات
echo "<div class='section info'>";
echo "<h2>1. فحص قاعدة البيانات</h2>";
try {
    $database = new Database();
    $db = $database->connect();
    echo "<p>✅ الاتصال بقاعدة البيانات: نجح</p>";
    
    // فحص الجداول المطلوبة
    $required_tables = ['articles', 'categories', 'users', 'matches', 'rss_sources'];
    echo "<h3>الجداول المطلوبة:</h3>";
    echo "<table>";
    echo "<tr><th>اسم الجدول</th><th>الحالة</th><th>عدد السجلات</th></tr>";
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<tr><td>$table</td><td style='color: green;'>✅ موجود</td><td>$count</td></tr>";
        } catch (Exception $e) {
            echo "<tr><td>$table</td><td style='color: red;'>❌ غير موجود</td><td>-</td></tr>";
        }
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 2. فحص الملفات المطلوبة
echo "<div class='section info'>";
echo "<h2>2. فحص الملفات الأساسية</h2>";
$required_files = [
    'index.php' => 'الصفحة الرئيسية',
    'matches.php' => 'صفحة المباريات',
    'article.php' => 'صفحة المقال',
    'category.php' => 'صفحة التصنيف',
    'search.php' => 'صفحة البحث',
    'includes/header.php' => 'رأس الصفحة',
    'includes/footer.php' => 'تذييل الصفحة',
    'includes/functions.php' => 'الدوال الأساسية',
    'includes/matches_functions.php' => 'دوال المباريات',
    'config/config.php' => 'ملف الإعدادات',
    'assets/css/homepage-enhancements.css' => 'ملف CSS المحسن'
];

echo "<table>";
echo "<tr><th>الملف</th><th>الوصف</th><th>الحالة</th><th>الحجم</th></tr>";

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        $size = round(filesize($file) / 1024, 2) . ' KB';
        echo "<tr><td>$file</td><td>$description</td><td style='color: green;'>✅ موجود</td><td>$size</td></tr>";
    } else {
        echo "<tr><td>$file</td><td>$description</td><td style='color: red;'>❌ غير موجود</td><td>-</td></tr>";
    }
}
echo "</table>";
echo "</div>";

// 3. فحص المباريات
echo "<div class='section info'>";
echo "<h2>3. فحص نظام المباريات</h2>";
if (file_exists('includes/matches_functions.php')) {
    require_once 'includes/matches_functions.php';
    try {
        $upcoming_matches = getUpcomingMatches(10);
        echo "<p>✅ دالة getUpcomingMatches تعمل بنجاح</p>";
        echo "<p><strong>عدد المباريات القادمة:</strong> " . count($upcoming_matches) . "</p>";
        
        if (!empty($upcoming_matches)) {
            echo "<h3>أول 5 مباريات قادمة:</h3>";
            echo "<table>";
            echo "<tr><th>الفريق المضيف</th><th>الفريق الضيف</th><th>التاريخ</th><th>البطولة</th><th>مميزة</th></tr>";
            
            for ($i = 0; $i < min(5, count($upcoming_matches)); $i++) {
                $match = $upcoming_matches[$i];
                $featured = $match['is_featured'] ? '⭐ نعم' : 'لا';
                echo "<tr>";
                echo "<td>" . $match['home_team'] . "</td>";
                echo "<td>" . $match['away_team'] . "</td>";
                echo "<td>" . formatMatchDate($match['match_date'], 'date_only') . "</td>";
                echo "<td>" . $match['competition'] . "</td>";
                echo "<td>$featured</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد مباريات قادمة</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في نظام المباريات: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف دوال المباريات غير موجود</p>";
}
echo "</div>";

// 4. فحص المقالات
echo "<div class='section info'>";
echo "<h2>4. فحص نظام المقالات</h2>";
try {
    $articles = getLatestArticles(5);
    echo "<p>✅ دالة getLatestArticles تعمل بنجاح</p>";
    echo "<p><strong>عدد المقالات:</strong> " . count($articles) . "</p>";
    
    if (!empty($articles)) {
        echo "<h3>أحدث 5 مقالات:</h3>";
        echo "<table>";
        echo "<tr><th>العنوان</th><th>التصنيف</th><th>تاريخ النشر</th><th>المشاهدات</th></tr>";
        
        foreach ($articles as $article) {
            echo "<tr>";
            echo "<td>" . mb_substr($article['title'], 0, 50) . "...</td>";
            echo "<td>" . ($article['category_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . formatArabicDate($article['published_at']) . "</td>";
            echo "<td>" . number_format($article['views']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد مقالات</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في نظام المقالات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 5. فحص التصنيفات
echo "<div class='section info'>";
echo "<h2>5. فحص التصنيفات</h2>";
try {
    $categories = getCategories();
    echo "<p>✅ دالة getCategories تعمل بنجاح</p>";
    echo "<p><strong>عدد التصنيفات:</strong> " . count($categories) . "</p>";
    
    if (!empty($categories)) {
        echo "<h3>قائمة التصنيفات:</h3>";
        echo "<table>";
        echo "<tr><th>الاسم</th><th>الرمز</th><th>الوصف</th></tr>";
        
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td>" . $category['name'] . "</td>";
            echo "<td>" . $category['slug'] . "</td>";
            echo "<td>" . ($category['description'] ?? 'لا يوجد وصف') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في نظام التصنيفات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 6. روابط الاختبار
echo "<div class='section success'>";
echo "<h2>6. روابط الاختبار</h2>";
echo "<p>استخدم الروابط التالية لاختبار الصفحات المختلفة:</p>";
echo "<a href='index.php' target='_blank' class='btn btn-primary'>الصفحة الرئيسية</a>";
echo "<a href='matches.php' target='_blank' class='btn btn-success'>صفحة المباريات</a>";
echo "<a href='category.php?slug=general-news' target='_blank' class='btn btn-warning'>صفحة التصنيف</a>";
echo "<a href='search.php?q=test' target='_blank' class='btn btn-danger'>صفحة البحث</a>";
echo "<a href='admin/login.php' target='_blank' class='btn btn-primary'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
