# دليل البدء السريع - موقع الأخبار

## خطوات التشغيل السريع

### 1. التحقق من المتطلبات
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx) أو استخدام خادم PHP المدمج

### 2. إعداد قاعدة البيانات

#### الطريقة الأولى: استخدام XAMPP/WAMP
1. تشغيل XAMPP/WAMP
2. تشغيل Apache و MySQL
3. وضع ملفات المشروع في مجلد `htdocs/amr`
4. زيارة `http://localhost/amr/test-connection.php` للتحقق من النظام
5. زيارة `http://localhost/amr/setup.php` لإعداد قاعدة البيانات

#### الطريقة الثانية: خادم PHP المدمج
1. فتح Terminal/Command Prompt في مجلد المشروع
2. تشغيل الأمر:
   ```bash
   # Windows
   start-server.bat
   
   # Linux/Mac
   chmod +x start-server.sh
   ./start-server.sh
   ```
3. زيارة `http://localhost:8000/test-connection.php`
4. زيارة `http://localhost:8000/setup.php`

### 3. الوصول إلى الموقع
- **الموقع الرئيسي:** `http://localhost/amr/` أو `http://localhost:8000/`
- **لوحة التحكم:** `http://localhost/amr/admin/login.php` أو `http://localhost:8000/admin/login.php`

### 4. بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## الخطوات التالية

### 1. إضافة مصادر RSS
1. دخول لوحة التحكم
2. الذهاب إلى "مصادر RSS"
3. إضافة مصادر RSS مثل:
   - BBC Arabic: `https://feeds.bbci.co.uk/arabic/rss.xml`
   - Al Jazeera: `https://www.aljazeera.net/xml/rss/all.xml`
   - CNN Arabic: `https://arabic.cnn.com/api/v1/rss/rss.xml`

### 2. جلب الأخبار
- اضغط زر "جلب RSS" في لوحة التحكم
- أو انتظر الجلب التلقائي (إذا تم إعداد Cron Job)

### 3. تخصيص الموقع
- تعديل إعدادات الموقع من "الإعدادات"
- إضافة تصنيفات جديدة
- تخصيص التصميم حسب الحاجة

## مصادر RSS مقترحة

### أخبار عامة
- BBC Arabic: `https://feeds.bbci.co.uk/arabic/rss.xml`
- Al Jazeera: `https://www.aljazeera.net/xml/rss/all.xml`
- Sky News Arabia: `https://www.skynewsarabia.com/rss`

### تقنية
- TechCrunch: `https://techcrunch.com/feed/`
- Ars Technica: `https://feeds.arstechnica.com/arstechnica/index`

### رياضة
- ESPN: `https://www.espn.com/espn/rss/news`
- Goal.com: `https://www.goal.com/feeds/en/news`

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تشغيل MySQL
2. تحقق من إعدادات قاعدة البيانات في `config/database.php`
3. تأكد من صحة اسم المستخدم وكلمة المرور

### لا يتم جلب RSS
1. تحقق من صحة روابط RSS
2. تأكد من اتصال الإنترنت
3. راجع ملف السجل في `logs/rss-cron.log`

### مشاكل في الصلاحيات
```bash
# إعطاء صلاحيات للمجلدات المطلوبة
chmod 755 logs/
chmod 755 uploads/ # إذا كان موجوداً
```

## الأمان

### بعد الإعداد
1. احذف ملف `setup.php`
2. احذف ملف `test-connection.php`
3. غير كلمة مرور المدير
4. تأكد من إعدادات `.htaccess`

### للإنتاج
1. استخدم HTTPS
2. قم بعمل نسخ احتياطية دورية
3. حدث كلمات المرور بانتظام
4. راقب ملفات السجلات

## الدعم

للحصول على المساعدة:
1. راجع ملف `README.md` للتفاصيل الكاملة
2. تحقق من ملفات السجلات في مجلد `logs/`
3. راجع الكود المصدري للفهم أكثر

---

**ملاحظة:** هذا دليل سريع للبدء. راجع `README.md` للحصول على تعليمات مفصلة.
