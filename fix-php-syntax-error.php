<?php
/**
 * إصلاح خطأ PHP Syntax في index.php
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح خطأ PHP Syntax</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; white-space: pre-wrap; border-left: 4px solid #007bff; }";
echo ".btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }";
echo ".btn-primary { background: #3498db; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-danger { background: #e74c3c; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح خطأ PHP Syntax في index.php</h1>";

$fixes_applied = [];
$errors = [];

// 1. تشخيص المشكلة
echo "<div class='section error'>";
echo "<h2>1. تشخيص المشكلة</h2>";

if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    $lines = explode("\n", $index_content);
    
    echo "<p><strong>المشكلة المكتشفة:</strong></p>";
    echo "<div class='code'>";
    echo "السطر 787: &lt;?php \n";
    echo "السطر 788: (فارغ)\n";
    echo "السطر 789: &lt;?php include 'includes/footer.php'; ?&gt;";
    echo "</div>";
    
    echo "<p style='color: red;'><strong>الخطأ:</strong> علامة PHP مفتوحة بدون إغلاق في السطر 787، مما يسبب تضارب مع علامة PHP في السطر 789.</p>";
    
    // فحص المزيد من المشاكل المحتملة
    $php_open_tags = substr_count($index_content, '<?php');
    $php_close_tags = substr_count($index_content, '?>');
    
    echo "<p><strong>إحصائيات PHP Tags:</strong></p>";
    echo "<ul>";
    echo "<li>علامات PHP المفتوحة: $php_open_tags</li>";
    echo "<li>علامات PHP المغلقة: $php_close_tags</li>";
    echo "<li>الفرق: " . ($php_open_tags - $php_close_tags) . "</li>";
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>❌ ملف index.php غير موجود</p>";
}
echo "</div>";

// 2. إنشاء نسخة احتياطية
echo "<div class='section info'>";
echo "<h2>2. إنشاء نسخة احتياطية</h2>";

if (file_exists('index.php')) {
    $backup_name = 'index.php.backup.' . date('Y-m-d-H-i-s');
    if (copy('index.php', $backup_name)) {
        echo "<p style='color: green;'>✅ تم إنشاء نسخة احتياطية: $backup_name</p>";
        $fixes_applied[] = "إنشاء نسخة احتياطية";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء نسخة احتياطية</p>";
        $errors[] = "فشل في إنشاء نسخة احتياطية";
    }
} else {
    echo "<p style='color: red;'>❌ ملف index.php غير موجود</p>";
}
echo "</div>";

// 3. إصلاح الخطأ
echo "<div class='section success'>";
echo "<h2>3. إصلاح خطأ PHP Syntax</h2>";

if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    
    // إصلاح المشكلة الأساسية - إزالة علامة PHP الفارغة
    $fixed_content = str_replace("<?php \n\n<?php include 'includes/footer.php'; ?>", "<?php include 'includes/footer.php'; ?>", $index_content);
    
    // إصلاح إضافي - إزالة أي علامات PHP فارغة أخرى
    $fixed_content = preg_replace('/\<\?php\s*\n\s*\n/', '', $fixed_content);
    $fixed_content = preg_replace('/\<\?php\s*\n\s*\<\?php/', '<?php', $fixed_content);
    
    // التأكد من أن الملف ينتهي بشكل صحيح
    $fixed_content = rtrim($fixed_content) . "\n";
    
    // حفظ الملف المصحح
    if (file_put_contents('index.php', $fixed_content)) {
        echo "<p style='color: green;'>✅ تم إصلاح خطأ PHP Syntax بنجاح</p>";
        $fixes_applied[] = "إصلاح خطأ PHP Syntax";
        
        // التحقق من صحة الإصلاح
        $syntax_check = shell_exec('php -l index.php 2>&1');
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "<p style='color: green;'>✅ تم التحقق من صحة PHP Syntax - لا توجد أخطاء</p>";
            $fixes_applied[] = "التحقق من صحة PHP Syntax";
        } else {
            echo "<p style='color: orange;'>⚠️ تحذير: $syntax_check</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ فشل في حفظ الملف المصحح</p>";
        $errors[] = "فشل في حفظ الملف المصحح";
    }
} else {
    echo "<p style='color: red;'>❌ ملف index.php غير موجود</p>";
}
echo "</div>";

// 4. فحص شامل للملف
echo "<div class='section info'>";
echo "<h2>4. فحص شامل للملف المصحح</h2>";

if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    $lines = explode("\n", $index_content);
    
    echo "<p><strong>إحصائيات الملف:</strong></p>";
    echo "<ul>";
    echo "<li>عدد الأسطر: " . count($lines) . "</li>";
    echo "<li>حجم الملف: " . round(strlen($index_content) / 1024, 2) . " KB</li>";
    echo "</ul>";
    
    // فحص آخر 10 أسطر
    echo "<p><strong>آخر 10 أسطر من الملف:</strong></p>";
    echo "<div class='code'>";
    $last_lines = array_slice($lines, -10);
    foreach ($last_lines as $i => $line) {
        $line_number = count($lines) - 10 + $i + 1;
        echo sprintf("%3d: %s\n", $line_number, htmlspecialchars($line));
    }
    echo "</div>";
    
    // فحص علامات PHP
    $php_open_tags = substr_count($index_content, '<?php');
    $php_close_tags = substr_count($index_content, '?>');
    
    echo "<p><strong>فحص علامات PHP:</strong></p>";
    echo "<ul>";
    echo "<li>علامات PHP المفتوحة: $php_open_tags</li>";
    echo "<li>علامات PHP المغلقة: $php_close_tags</li>";
    if ($php_open_tags == $php_close_tags) {
        echo "<li style='color: green;'>✅ علامات PHP متوازنة</li>";
    } else {
        echo "<li style='color: orange;'>⚠️ علامات PHP غير متوازنة (هذا طبيعي في ملفات PHP)</li>";
    }
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>❌ ملف index.php غير موجود</p>";
}
echo "</div>";

// 5. فحص الملفات الأخرى
echo "<div class='section warning'>";
echo "<h2>5. فحص الملفات الأخرى للمشاكل المشابهة</h2>";

$files_to_check = [
    'matches.php' => 'صفحة المباريات',
    'article.php' => 'صفحة المقال',
    'category.php' => 'صفحة التصنيف',
    'search.php' => 'صفحة البحث'
];

echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px; border: 1px solid #ddd;'>الملف</th>";
echo "<th style='padding: 10px; border: 1px solid #ddd;'>الحالة</th>";
echo "<th style='padding: 10px; border: 1px solid #ddd;'>ملاحظات</th>";
echo "</tr>";

foreach ($files_to_check as $file => $description) {
    echo "<tr>";
    echo "<td style='padding: 10px; border: 1px solid #ddd;'>$description</td>";
    
    if (file_exists($file)) {
        $syntax_check = shell_exec("php -l $file 2>&1");
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "<td style='padding: 10px; border: 1px solid #ddd; color: green;'>✅ سليم</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>لا توجد أخطاء syntax</td>";
        } else {
            echo "<td style='padding: 10px; border: 1px solid #ddd; color: red;'>❌ خطأ</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($syntax_check) . "</td>";
        }
    } else {
        echo "<td style='padding: 10px; border: 1px solid #ddd; color: orange;'>⚠️ غير موجود</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd;'>الملف غير موجود</td>";
    }
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 6. ملخص الإصلاحات
echo "<div class='section success'>";
echo "<h2>6. ملخص الإصلاحات</h2>";

if (!empty($fixes_applied)) {
    echo "<h3>الإصلاحات المطبقة:</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li style='color: green;'>✅ $fix</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: blue;'>ℹ️ لم يتم تطبيق إصلاحات</p>";
}

if (!empty($errors)) {
    echo "<h3>الأخطاء:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>❌ $error</li>";
    }
    echo "</ul>";
}

echo "<h3>تفاصيل المشكلة التي تم إصلاحها:</h3>";
echo "<div class='code'>";
echo "المشكلة الأصلية:\n";
echo "السطر 787: &lt;?php \n";
echo "السطر 788: (فارغ)\n";
echo "السطر 789: &lt;?php include 'includes/footer.php'; ?&gt;\n\n";
echo "الإصلاح المطبق:\n";
echo "السطر 787: &lt;?php include 'includes/footer.php'; ?&gt;";
echo "</div>";

echo "<h3>سبب المشكلة:</h3>";
echo "<p>حدثت هذه المشكلة أثناء إعادة هيكلة الهيدر والفوتر، حيث تم ترك علامة PHP مفتوحة بدون محتوى أو إغلاق، مما تسبب في تضارب مع علامة PHP التالية.</p>";

echo "<h3>الحل المطبق:</h3>";
echo "<ul>";
echo "<li>إزالة علامة PHP الفارغة</li>";
echo "<li>دمج استدعاء الفوتر في علامة PHP واحدة</li>";
echo "<li>تنظيف أي علامات PHP مكررة أو فارغة</li>";
echo "<li>التحقق من صحة PHP Syntax</li>";
echo "</ul>";
echo "</div>";

// 7. اختبار النتائج
echo "<div class='section info'>";
echo "<h2>7. اختبار النتائج</h2>";
echo "<div style='text-align: center;'>";
echo "<a href='index.php' target='_blank' class='btn btn-primary'>اختبار الصفحة الرئيسية</a>";
echo "<a href='matches.php' target='_blank' class='btn btn-success'>اختبار صفحة المباريات</a>";
echo "<a href='header-diagnosis.php' target='_blank' class='btn btn-danger'>تشخيص الهيدر</a>";
echo "</div>";

echo "<p style='margin-top: 20px; text-align: center; color: #27ae60; font-weight: bold;'>";
echo "🎉 تم إصلاح خطأ PHP Syntax بنجاح! يجب أن تعمل الصفحة الآن بدون أخطاء.";
echo "</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
