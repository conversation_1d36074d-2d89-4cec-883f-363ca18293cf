<?php
/**
 * ملف التكوين العام للموقع الإخباري
 */

// منع الوصول المباشر
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__DIR__));
}

// إعدادات عامة
define('SITE_URL', 'http://localhost/amr'); // غير هذا إلى رابط موقعك
define('ADMIN_URL', SITE_URL . '/admin');
define('ASSETS_URL', SITE_URL . '/assets');

// إعدادات قاعدة البيانات (يمكن نقلها من database.php)
define('DB_HOST', 'localhost');
define('DB_NAME', 'news_website');
define('DB_USER', 'root');
define('DB_PASS', '');

// إعدادات الأمان
define('SESSION_LIFETIME', 3600 * 24); // 24 ساعة
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);

// إعدادات RSS
define('RSS_USER_AGENT', 'NewsBot/1.0 (Compatible RSS Reader)');
define('RSS_TIMEOUT', 30);
define('RSS_MAX_ARTICLES_PER_FEED', 50);

// إعدادات الرفع
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('UPLOAD_PATH', ROOT_PATH . '/uploads');

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // ساعة واحدة

// إعدادات السجلات
define('LOG_ENABLED', true);
define('LOG_PATH', ROOT_PATH . '/logs');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// إعدادات البريد الإلكتروني (للإشعارات)
define('MAIL_ENABLED', false);
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '');
define('MAIL_PASSWORD', '');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'موقع الأخبار');

// إعدادات التصفح
define('ARTICLES_PER_PAGE', 12);
define('SEARCH_RESULTS_PER_PAGE', 15);
define('ADMIN_ITEMS_PER_PAGE', 20);

// إعدادات SEO
define('SITE_NAME', 'موقع الأخبار');
define('SITE_DESCRIPTION', 'موقع إخباري شامل يقدم آخر الأخبار والتطورات');
define('SITE_KEYWORDS', 'أخبار، عاجل، تقنية، رياضة، اقتصاد، سياسة');

// إعدادات وسائل التواصل الاجتماعي
define('FACEBOOK_URL', '#');
define('TWITTER_URL', '#');
define('INSTAGRAM_URL', '#');
define('YOUTUBE_URL', '#');
define('TELEGRAM_URL', '#');

// إعدادات المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات اللغة والترميز
define('SITE_LANGUAGE', 'ar');
define('SITE_CHARSET', 'UTF-8');
define('SITE_DIRECTION', 'rtl');

// إعدادات التطوير
define('DEBUG_MODE', true); // غير إلى false في الإنتاج
define('SHOW_ERRORS', DEBUG_MODE);

// تفعيل عرض الأخطاء حسب وضع التطوير
if (SHOW_ERRORS) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// ملاحظة: إعدادات الجلسة تم نقلها إلى includes/session_init.php

// دالة للحصول على رابط كامل
function getFullUrl($path = '') {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $base = dirname($_SERVER['SCRIPT_NAME']);
    
    if ($base === '/') {
        $base = '';
    }
    
    return $protocol . '://' . $host . $base . '/' . ltrim($path, '/');
}

// دالة للحصول على رابط الأصول
function getAssetUrl($asset) {
    return ASSETS_URL . '/' . ltrim($asset, '/');
}

// دالة تسجيل الأخطاء
function logError($message, $file = 'error.log') {
    if (!LOG_ENABLED) return;
    
    $log_file = LOG_PATH . '/' . $file;
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message" . PHP_EOL;
    
    // إنشاء مجلد السجلات إذا لم يكن موجوداً
    if (!is_dir(LOG_PATH)) {
        mkdir(LOG_PATH, 0755, true);
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}

// ملاحظة: الدوال المساعدة موجودة في includes/functions.php
// تم إزالة الدوال المكررة لتجنب تضارب التعريفات

// تحميل ملف التكوين المخصص إذا كان موجوداً
$custom_config = ROOT_PATH . '/config/custom.php';
if (file_exists($custom_config)) {
    require_once $custom_config;
}
?>
