            </main>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center">
            <i class="fas fa-spinner fa-spin text-blue-600 text-2xl ml-3"></i>
            <span class="text-gray-700">جاري التحميل...</span>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">تأكيد العملية</h3>
                <p id="confirmation-message" class="text-gray-600 mb-6">هل أنت متأكد من أنك تريد تنفيذ هذه العملية؟</p>
                <div class="flex space-x-3 space-x-reverse">
                    <button onclick="confirmAction()" class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                        تأكيد
                    </button>
                    <button onclick="closeConfirmationModal()" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let confirmCallback = null;

        // Show loading overlay
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
        }

        // Hide loading overlay
        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }

        // Show confirmation modal
        function showConfirmation(message, callback) {
            document.getElementById('confirmation-message').textContent = message;
            document.getElementById('confirmation-modal').classList.remove('hidden');
            confirmCallback = callback;
        }

        // Close confirmation modal
        function closeConfirmationModal() {
            document.getElementById('confirmation-modal').classList.add('hidden');
            confirmCallback = null;
        }

        // Confirm action
        function confirmAction() {
            if (confirmCallback) {
                confirmCallback();
            }
            closeConfirmationModal();
        }

        // Delete confirmation
        function confirmDelete(url, message = 'هل أنت متأكد من أنك تريد حذف هذا العنصر؟') {
            showConfirmation(message, function() {
                showLoading();
                window.location.href = url;
            });
        }

        // Form submission with loading
        function submitFormWithLoading(formId) {
            showLoading();
            document.getElementById(formId).submit();
        }

        // AJAX request helper
        function makeAjaxRequest(url, data = {}, method = 'POST') {
            return fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: method !== 'GET' ? JSON.stringify(data) : null
            })
            .then(response => response.json())
            .catch(error => {
                console.error('AJAX Error:', error);
                throw error;
            });
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const colors = {
                success: 'bg-green-100 border-green-500 text-green-700',
                error: 'bg-red-100 border-red-500 text-red-700',
                warning: 'bg-yellow-100 border-yellow-500 text-yellow-700',
                info: 'bg-blue-100 border-blue-500 text-blue-700'
            };

            const notification = document.createElement('div');
            notification.className = `${colors[type]} border-l-4 p-4 rounded mb-4 fixed top-4 right-4 z-50 max-w-md shadow-lg`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle ml-2"></i>
                        <span>${message}</span>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.transition = 'opacity 0.5s ease-out';
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.remove();
                    }, 500);
                }
            }, 5000);
        }

        // Format numbers
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-EG').format(num);
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification('تم نسخ النص بنجاح', 'success');
            }, function(err) {
                showNotification('فشل في نسخ النص', 'error');
            });
        }

        // Toggle element visibility
        function toggleElement(elementId) {
            const element = document.getElementById(elementId);
            element.classList.toggle('hidden');
        }

        // Validate form
        function validateForm(formId) {
            const form = document.getElementById(formId);
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });

            return isValid;
        }

        // Auto-resize textarea
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }

        // Initialize auto-resize for all textareas
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea[data-auto-resize]');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    autoResizeTextarea(this);
                });
                // Initial resize
                autoResizeTextarea(textarea);
            });
        });

        // Search functionality
        function initializeSearch(inputId, targetClass) {
            const searchInput = document.getElementById(inputId);
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const items = document.querySelectorAll(targetClass);
                    
                    items.forEach(item => {
                        const text = item.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            item.style.display = '';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            }
        }

        // Sortable table functionality
        function makeSortable(tableId) {
            const table = document.getElementById(tableId);
            if (!table) return;

            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    const column = this.dataset.sort;
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    
                    const isAscending = this.classList.contains('sort-asc');
                    
                    // Remove sort classes from all headers
                    headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
                    
                    // Add appropriate class to current header
                    this.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
                    
                    rows.sort((a, b) => {
                        const aVal = a.querySelector(`[data-sort="${column}"]`).textContent.trim();
                        const bVal = b.querySelector(`[data-sort="${column}"]`).textContent.trim();
                        
                        if (isAscending) {
                            return bVal.localeCompare(aVal, 'ar', { numeric: true });
                        } else {
                            return aVal.localeCompare(bVal, 'ar', { numeric: true });
                        }
                    });
                    
                    rows.forEach(row => tbody.appendChild(row));
                });
            });
        }

        // Initialize tooltips
        function initializeTooltips() {
            const tooltipElements = document.querySelectorAll('[data-tooltip]');
            tooltipElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'absolute bg-gray-800 text-white text-sm rounded py-1 px-2 z-50';
                    tooltip.textContent = this.dataset.tooltip;
                    tooltip.id = 'tooltip';
                    
                    document.body.appendChild(tooltip);
                    
                    const rect = this.getBoundingClientRect();
                    tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
                    tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
                });
                
                element.addEventListener('mouseleave', function() {
                    const tooltip = document.getElementById('tooltip');
                    if (tooltip) {
                        tooltip.remove();
                    }
                });
            });
        }

        // Initialize all functionality when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeTooltips();
            
            // Close modals when clicking outside
            document.addEventListener('click', function(event) {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (event.target === modal) {
                        modal.classList.add('hidden');
                    }
                });
            });
        });
    </script>
</body>
</html>
