# 🚨 403 Forbidden Error - Complete Troubleshooting Guide

## 🔍 Quick Diagnosis

**First, run the troubleshooting tool:**
```
http://localhost/amr/troubleshoot-403.php
```

## 🎯 Most Common Causes & Solutions

### 1. **Overly Restrictive .htaccess Rules** ⭐ (Most Likely)

**Problem:** The .htaccess file has rules that block legitimate access.

**Quick Fix:**
```bash
# Temporarily disable .htaccess
mv .htaccess .htaccess.backup

# Or use the simple version
cp .htaccess.simple .htaccess
```

**Test:** Try accessing the site again.

### 2. **Incorrect File Permissions**

**Problem:** Web server can't read the files.

**Fix for Linux/Mac:**
```bash
# Set correct permissions
find . -type f -name "*.php" -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 644 .htaccess
```

**Fix for Windows (XAMPP):**
- Usually not a permission issue
- Check if files are in the correct htdocs folder

### 3. **Wrong Directory Structure**

**Problem:** Files not in the web server's document root.

**Fix:**
- Ensure files are in: `C:\xampp\htdocs\amr\` (Windows)
- Or: `/var/www/html/amr/` (Linux)
- Or: `/Applications/XAMPP/htdocs/amr/` (Mac)

### 4. **Apache Configuration Issues**

**Problem:** Apache not configured to allow .htaccess or directory access.

**Fix:** Check Apache configuration:
```apache
# In httpd.conf or virtual host
<Directory "/path/to/your/site">
    AllowOverride All
    Require all granted
</Directory>
```

## 🛠️ Step-by-Step Troubleshooting

### Step 1: Basic Checks
1. **Verify file location:**
   ```bash
   # Check if index.php exists
   ls -la index.php
   ```

2. **Check web server status:**
   - XAMPP: Ensure Apache is running (green light)
   - Command line: `sudo systemctl status apache2`

3. **Verify URL:**
   - Correct: `http://localhost/amr/`
   - Incorrect: `http://localhost/amr/index.php/`

### Step 2: .htaccess Troubleshooting
1. **Disable .htaccess temporarily:**
   ```bash
   mv .htaccess .htaccess.disabled
   ```

2. **If site works without .htaccess:**
   - The .htaccess file is the problem
   - Use the simple version: `cp .htaccess.simple .htaccess`

3. **If site still doesn't work:**
   - Problem is not .htaccess related
   - Continue to Step 3

### Step 3: Permission Checks
1. **Check file permissions:**
   ```bash
   # Files should be 644
   ls -la *.php
   
   # Directories should be 755
   ls -ld */
   ```

2. **Fix permissions if needed:**
   ```bash
   chmod 644 *.php
   chmod 755 admin/ config/ includes/ classes/ logs/
   ```

### Step 4: Apache Configuration
1. **Check Apache error log:**
   ```bash
   # Linux
   tail -f /var/log/apache2/error.log
   
   # XAMPP Windows
   # Check: C:\xampp\apache\logs\error.log
   
   # XAMPP Mac
   tail -f /Applications/XAMPP/logs/error_log
   ```

2. **Common error messages and fixes:**
   - `"Options ExecCGI is off"` → Enable CGI in Apache config
   - `"AllowOverride None"` → Change to `AllowOverride All`
   - `"access denied"` → Check file permissions

### Step 5: PHP Configuration
1. **Check if PHP is working:**
   ```php
   <?php phpinfo(); ?>
   ```
   Save as `test.php` and access via browser.

2. **Common PHP issues:**
   - PHP not installed/enabled
   - Wrong PHP version
   - Missing PHP modules

## 🔧 Quick Fixes

### Fix 1: Use Simple .htaccess
```bash
# Replace complex .htaccess with simple version
cp .htaccess.simple .htaccess
```

### Fix 2: Completely Remove .htaccess
```bash
# Remove .htaccess entirely (temporary fix)
rm .htaccess
```

### Fix 3: Reset All Permissions
```bash
# Linux/Mac - Reset all permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
```

### Fix 4: Use PHP Built-in Server
```bash
# Bypass Apache entirely
php -S localhost:8000
```

## 🎯 Environment-Specific Solutions

### XAMPP (Windows)
1. **Check XAMPP Control Panel:**
   - Apache should show "Running" (green)
   - Click "Admin" next to Apache for config

2. **Common XAMPP issues:**
   - Port 80 conflict → Change Apache port
   - Antivirus blocking → Add XAMPP to exceptions
   - Files in wrong location → Must be in `htdocs/amr/`

3. **XAMPP .htaccess fix:**
   ```apache
   # In C:\xampp\apache\conf\httpd.conf
   # Find and uncomment:
   LoadModule rewrite_module modules/mod_rewrite.so
   
   # Find and change:
   AllowOverride None
   # To:
   AllowOverride All
   ```

### WAMP (Windows)
1. **Check WAMP status:** All services should be green
2. **Enable mod_rewrite:** Left-click WAMP → Apache → Apache Modules → rewrite_module

### Linux (Apache)
1. **Enable mod_rewrite:**
   ```bash
   sudo a2enmod rewrite
   sudo systemctl restart apache2
   ```

2. **Check virtual host config:**
   ```apache
   <VirtualHost *:80>
       DocumentRoot /var/www/html/amr
       <Directory /var/www/html/amr>
           AllowOverride All
           Require all granted
       </Directory>
   </VirtualHost>
   ```

## 🚀 Testing After Fixes

### Test Checklist:
1. ✅ **Basic access:** `http://localhost/amr/`
2. ✅ **Admin panel:** `http://localhost/amr/admin/login.php`
3. ✅ **Database setup:** `http://localhost/amr/setup.php`
4. ✅ **Connection test:** `http://localhost/amr/test-connection.php`
5. ✅ **Troubleshooting tool:** `http://localhost/amr/troubleshoot-403.php`

### If Still Not Working:
1. **Check browser console** for JavaScript errors
2. **Try different browser** (Chrome, Firefox, Edge)
3. **Clear browser cache** (Ctrl+F5)
4. **Check firewall/antivirus** settings
5. **Try incognito/private mode**

## 🔒 Security Notes

### After Fixing:
1. **Remove troubleshooting files:**
   ```bash
   rm troubleshoot-403.php fix-htaccess.php test-connection.php
   ```

2. **Restore proper .htaccess:**
   - Use the corrected version, not the overly restrictive one
   - Test thoroughly before deploying to production

3. **Set proper permissions:**
   - Files: 644 (readable by web server)
   - Directories: 755 (accessible by web server)
   - Sensitive files: 600 (owner only)

## 📞 Emergency Access

If nothing works, use PHP's built-in server:
```bash
cd /path/to/your/project
php -S localhost:8000
```

Then access: `http://localhost:8000`

This bypasses Apache entirely and helps isolate the problem.

---

**Need more help?** Run the troubleshooting tool and check the specific error messages in your Apache error log.
