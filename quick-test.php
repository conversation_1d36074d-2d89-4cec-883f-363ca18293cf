<?php
/**
 * اختبار سريع للصفحة الرئيسية
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// الحصول على المباريات القادمة (إذا كان نظام المباريات مفعل)
$upcoming_matches = [];
if (file_exists('includes/matches_functions.php')) {
    require_once 'includes/matches_functions.php';
    try {
        $upcoming_matches = getUpcomingMatches(5);
    } catch (Exception $e) {
        // نظام المباريات غير مفعل أو غير مثبت
        $upcoming_matches = [];
    }
}

echo "<h1>اختبار سريع للصفحة الرئيسية</h1>";
echo "<p><strong>عدد المباريات القادمة:</strong> " . count($upcoming_matches) . "</p>";
echo "<p><strong>حالة السلايدر:</strong> " . (!empty($upcoming_matches) ? '✅ سيظهر' : '❌ لن يظهر') . "</p>";

if (!empty($upcoming_matches)) {
    $all_matches = array_slice($upcoming_matches, 0, 9); // أقصى 9 مباريات
    $total_slides = ceil(count($all_matches) / 3); // حساب عدد الشرائح للديسكتوب
    
    echo "<p><strong>عدد المباريات للسلايدر:</strong> " . count($all_matches) . "</p>";
    echo "<p><strong>عدد الشرائح:</strong> " . $total_slides . "</p>";
    
    echo "<h2>المباريات:</h2>";
    echo "<ul>";
    foreach ($upcoming_matches as $match) {
        echo "<li>" . $match['home_team'] . " vs " . $match['away_team'] . " - " . $match['match_date'] . "</li>";
    }
    echo "</ul>";
    
    // اختبار الشرط
    echo "<h2>اختبار الشرط:</h2>";
    if (!empty($upcoming_matches)) {
        echo "<p style='color: green;'>✅ الشرط !empty(\$upcoming_matches) صحيح</p>";
        echo "<p>سيتم عرض السلايدر في الصفحة الرئيسية</p>";
    } else {
        echo "<p style='color: red;'>❌ الشرط !empty(\$upcoming_matches) خاطئ</p>";
        echo "<p>لن يتم عرض السلايدر في الصفحة الرئيسية</p>";
    }
} else {
    echo "<p style='color: red;'>لا توجد مباريات قادمة</p>";
}

// اختبار تحميل الصفحة الرئيسية
echo "<h2>اختبار تحميل الصفحة الرئيسية:</h2>";
echo "<p><a href='index.php?debug=1' target='_blank'>فتح الصفحة الرئيسية مع التشخيص</a></p>";
echo "<p><a href='test-slider-only.php' target='_blank'>فتح صفحة اختبار السلايدر فقط</a></p>";
?>
