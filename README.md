# موقع الأخبار الشامل مع تكامل RSS - نسخة مكتملة

موقع إخباري متكامل مبني بـ PHP مع نظام إدارة محتوى ديناميكي وتكامل مع مصادر RSS.

## 🎉 النظام مكتمل الآن!

تم إكمال جميع الميزات الأساسية والمتقدمة للموقع. النظام جاهز للاستخدام الفوري.

## الميزات الرئيسية

### الميزات العامة
- 📰 عرض الأخبار بتصميم عصري ومتجاوب
- 🔍 نظام بحث متقدم في المقالات
- 📱 تصميم متجاوب يعمل على جميع الأجهزة
- 🏷️ تصنيف المقالات والتنقل السهل
- 👁️ عداد مشاهدات للمقالات
- 📊 عرض المقالات المميزة
- 🔗 مشاركة المقالات على وسائل التواصل

### نظام RSS
- 📡 جلب تلقائي للأخبار من مصادر RSS متعددة
- ⚙️ إدارة مصادر RSS (إضافة/تعديل/حذف)
- 🔄 جدولة جلب RSS تلقائياً
- 📝 تحليل وتنظيف محتوى RSS
- 🖼️ استخراج الصور من المحتوى

### لوحة التحكم الإدارية المكتملة
- 📊 لوحة تحكم شاملة مع إحصائيات تفاعلية
- ✍️ إدارة المقالات الكاملة (إضافة/تعديل/حذف/بحث/تصفية)
- 📁 إدارة التصنيفات مع عداد المقالات
- 📡 إدارة مصادر RSS مع اختبار الاتصال
- 👥 إدارة المستخدمين والأدوار (للمديرين)
- ⚙️ إعدادات الموقع الشاملة
- 📈 تقارير وإحصائيات مفصلة مع رسوم بيانية
- 🔄 جلب RSS تلقائي ويدوي
- 🎨 واجهة عصرية ومتجاوبة
- 🔐 نظام مصادقة وتفويض متقدم

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- امتدادات PHP المطلوبة:
  - PDO
  - PDO_MySQL
  - SimpleXML
  - cURL
  - mbstring

## 🚀 البدء السريع (3 دقائق)

### الخطوة 1: رفع الملفات
```bash
# نسخ جميع الملفات إلى مجلد الخادم
cp -r * /path/to/your/webserver/
```

### الخطوة 2: إعداد قاعدة البيانات
1. تأكد من تشغيل خادم MySQL
2. قم بزيارة `http://yoursite.com/setup.php`
3. اتبع التعليمات لإنشاء قاعدة البيانات

### الخطوة 3: تسجيل الدخول
- اذهب إلى `http://yoursite.com/admin/login.php`
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### الخطوة 4: إضافة البيانات الأولية
1. في لوحة التحكم، اضغط على "إضافة البيانات الأولية"
2. سيتم إضافة التصنيفات ومصادر RSS والمقالات التجريبية
3. اضغط على "جلب أخبار RSS" لجلب أول مجموعة من الأخبار

🎉 **مبروك! موقعك جاهز الآن**

## التثبيت والإعداد المفصل

### 1. تحميل الملفات
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r * /path/to/your/webserver/
```

### 2. إعداد قاعدة البيانات
1. تأكد من تشغيل خادم MySQL
2. قم بتعديل إعدادات قاعدة البيانات في `config/database.php` (اختياري)
3. قم بزيارة `http://yoursite.com/setup.php` لإنشاء قاعدة البيانات

### 3. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد السجلات
chmod 755 logs/
chmod 644 logs/*

# إعطاء صلاحيات الكتابة لمجلد الرفع (إذا كان موجوداً)
chmod 755 uploads/
```

### 4. إعداد Cron Job (اختياري)
لجلب RSS تلقائياً، أضف هذا السطر إلى crontab:
```bash
# جلب RSS كل ساعة
0 * * * * /usr/bin/php /path/to/your/website/cron/fetch-rss.php

# أو كل 30 دقيقة
*/30 * * * * /usr/bin/php /path/to/your/website/cron/fetch-rss.php
```

## بيانات الدخول الافتراضية

بعد تشغيل setup.php، يمكنك الدخول إلى لوحة التحكم باستخدام:

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** يُنصح بتغيير كلمة المرور فوراً بعد أول تسجيل دخول.

## هيكل المشروع

```
├── admin/                  # لوحة التحكم الإدارية المكتملة
│   ├── includes/          # ملفات مشتركة للإدارة
│   │   ├── auth.php       # نظام المصادقة
│   │   ├── header.php     # رأس لوحة التحكم
│   │   └── footer.php     # تذييل لوحة التحكم
│   ├── dashboard.php      # لوحة التحكم الرئيسية مع إحصائيات
│   ├── articles.php       # إدارة المقالات الكاملة
│   ├── categories.php     # إدارة التصنيفات
│   ├── rss-sources.php    # إدارة مصادر RSS
│   ├── users.php          # إدارة المستخدمين
│   ├── settings.php       # إعدادات الموقع
│   ├── init-data.php      # إضافة البيانات الأولية
│   ├── login.php          # صفحة تسجيل الدخول
│   ├── logout.php         # تسجيل الخروج
│   └── fetch-rss.php      # جلب RSS عبر AJAX
├── classes/               # فئات PHP
│   └── RSSParser.php      # فئة تحليل RSS
├── config/                # ملفات التكوين
│   └── database.php       # إعدادات قاعدة البيانات
├── cron/                  # مهام مجدولة
│   └── fetch-rss.php      # جلب RSS تلقائياً
├── includes/              # ملفات مشتركة
│   ├── functions.php      # دوال مساعدة
│   ├── header.php         # رأس الصفحة
│   └── footer.php         # تذييل الصفحة
├── logs/                  # ملفات السجلات
├── index.php              # الصفحة الرئيسية
├── article.php            # عرض المقال
├── category.php           # عرض التصنيف
├── search.php             # صفحة البحث
└── setup.php              # إعداد قاعدة البيانات
```

## الاستخدام

### إضافة مصادر RSS
1. ادخل إلى لوحة التحكم
2. اذهب إلى "مصادر RSS"
3. اضغط "إضافة مصدر جديد"
4. أدخل اسم المصدر ورابط RSS
5. اختر التصنيف المناسب
6. احفظ التغييرات

### جلب الأخبار
- **يدوياً:** اضغط زر "جلب RSS" في لوحة التحكم
- **تلقائياً:** سيتم الجلب حسب الجدولة المحددة في الإعدادات

### إدارة المقالات
- عرض جميع المقالات من "المقالات"
- تعديل أو حذف المقالات
- تمييز المقالات المهمة
- إدارة التصنيفات

## التخصيص

### تغيير التصميم
- ملفات CSS موجودة في `includes/header.php`
- يمكن تخصيص الألوان والخطوط
- التصميم يستخدم Tailwind CSS

### إضافة ميزات جديدة
- أضف دوال جديدة في `includes/functions.php`
- أنشئ صفحات جديدة حسب الحاجة
- استخدم نفس هيكل الملفات الموجود

## الأمان

### إجراءات الأمان المطبقة
- تشفير كلمات المرور
- حماية من SQL Injection
- تنظيف المدخلات
- جلسات آمنة
- التحقق من الصلاحيات

### توصيات إضافية
- احذف ملف `setup.php` بعد الإعداد
- استخدم HTTPS في الإنتاج
- قم بعمل نسخ احتياطية دورية
- حدث كلمات المرور بانتظام

## استكشاف الأخطاء

### مشاكل شائعة

**خطأ في الاتصال بقاعدة البيانات:**
- تحقق من إعدادات قاعدة البيانات في `config/database.php`
- تأكد من تشغيل خادم MySQL

**لا يتم جلب RSS:**
- تحقق من صحة روابط RSS
- تأكد من إعدادات الشبكة والجدار الناري
- راجع ملف السجل في `logs/rss-cron.log`

**مشاكل في الصلاحيات:**
- تأكد من صلاحيات الكتابة لمجلد logs
- تحقق من إعدادات PHP

## الدعم والمساهمة

هذا المشروع مفتوح المصدر ويمكن تطويره وتحسينه. لأي استفسارات أو مشاكل:

1. راجع ملفات السجلات
2. تحقق من إعدادات النظام
3. راجع الكود للفهم أكثر

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

## 🧪 اختبار النظام

### اختبار شامل:
قم بزيارة `http://yoursite.com/test-complete.php` لإجراء اختبار شامل للنظام

### قائمة الاختبار:
1. ✅ **الصفحة الرئيسية:** `http://localhost/amr/`
2. ✅ **لوحة التحكم:** `http://localhost/amr/admin/login.php`
3. ✅ **إعداد قاعدة البيانات:** `http://localhost/amr/setup.php`
4. ✅ **اختبار الاتصال:** `http://localhost/amr/test-connection.php`
5. ✅ **اختبار شامل:** `http://localhost/amr/test-complete.php`
6. ✅ **إضافة البيانات الأولية:** `http://localhost/amr/admin/init-data.php`

### إذا كان كل شيء يعمل:
1. **احذف ملفات الاختبار** (اختياري):
   ```bash
   rm test-*.php troubleshoot-*.php debug-*.php fix-*.php
   ```

2. **ابدأ في استخدام موقعك!**

---

**ملاحظة:** هذا المشروع تم تطويره لأغراض تعليمية وتجارية. يمكن استخدامه وتطويره بحرية.
