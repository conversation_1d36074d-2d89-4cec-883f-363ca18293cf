/*
 * Accessibility Fixes - إصلاحات الوصولية
 * ملف CSS لتحسين إمكانية الوصول وتجربة المستخدمين ذوي الاحتياجات الخاصة
 */

/* ==========================================================================
   Focus Management - إدارة التركيز
   ========================================================================== */

/* تحسين مؤشر التركيز العام */
*:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* تحسين التركيز للروابط */
a:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    text-decoration: underline;
}

/* تحسين التركيز للأزرار */
button:focus,
.btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* تحسين التركيز للنماذج */
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* تحسين التركيز للبطاقات */
.news-card:focus-within {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    box-shadow: var(--shadow-lg);
}

/* ==========================================================================
   Color Contrast Improvements - تحسين التباين اللوني
   ========================================================================== */

/* تحسين التباين للنصوص المكتومة */
.text-muted {
    color: var(--neutral-600); /* تباين 7:1 مع الخلفية البيضاء */
}

/* تحسين التباين للروابط الثانوية */
.link-secondary {
    color: var(--primary-700); /* تباين محسن */
}

.link-secondary:hover {
    color: var(--primary-800);
}

/* تحسين التباين للنصوص الصغيرة */
.text-small {
    color: var(--neutral-700); /* تباين أفضل للنصوص الصغيرة */
    font-size: var(--text-sm);
}

/* تحسين التباين للتواريخ والمعلومات الإضافية */
.meta-text {
    color: var(--neutral-600);
    font-size: var(--text-sm);
}

/* تحسين التباين للشارات */
.badge-light {
    background: var(--neutral-100);
    color: var(--neutral-800); /* تباين محسن */
    border: 1px solid var(--neutral-300);
}

/* ==========================================================================
   Touch Target Optimization - تحسين أهداف اللمس
   ========================================================================== */

/* أحجام مناسبة للمس (44px minimum) */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-2);
}

/* تحسين أزرار التنقل للمس */
.nav-link {
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
}

/* تحسين الأزرار للمس */
.btn {
    min-height: 44px;
    padding: var(--space-3) var(--space-6);
}

.btn-sm {
    min-height: 36px;
    padding: var(--space-2) var(--space-4);
}

/* تحسين الروابط للمس */
.card-link {
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: var(--space-2);
}

/* ==========================================================================
   Keyboard Navigation - التنقل بلوحة المفاتيح
   ========================================================================== */

/* تحسين التنقل بلوحة المفاتيح للقوائم */
.nav-menu {
    display: flex;
    gap: var(--space-2);
}

.nav-menu li {
    list-style: none;
}

.nav-menu a {
    display: block;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.nav-menu a:focus {
    background: var(--primary-50);
    outline: 2px solid var(--primary-500);
}

/* تحسين التنقل للبطاقات */
.news-card {
    position: relative;
}

.news-card a {
    text-decoration: none;
}

.news-card a:focus {
    outline: none;
}

.news-card:focus-within {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* تحسين التنقل للنماذج */
.form-group {
    margin-bottom: var(--space-4);
}

.form-label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: var(--font-medium);
    color: var(--neutral-700);
}

/* ==========================================================================
   Screen Reader Support - دعم قارئات الشاشة
   ========================================================================== */

/* إخفاء المحتوى بصرياً مع الاحتفاظ بإمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* إظهار المحتوى عند التركيز */
.sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: var(--space-2);
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    background: var(--primary-600);
    color: white;
    border-radius: var(--radius-md);
}

/* تحسين وصف الصور */
img:not([alt]) {
    outline: 2px solid var(--error-500);
}

/* تحسين الأيقونات الزخرفية */
.icon-decorative {
    aria-hidden: true;
}

/* ==========================================================================
   Skip Links - روابط التخطي
   ========================================================================== */

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-600);
    color: white;
    padding: var(--space-2) var(--space-4);
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: 1000;
    font-weight: var(--font-medium);
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
    outline: 2px solid white;
    outline-offset: 2px;
}

/* ==========================================================================
   Reduced Motion Support - دعم الحركة المقللة
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .news-card:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
    
    .image-card:hover,
    .image-hero:hover {
        transform: none;
    }
}

/* ==========================================================================
   High Contrast Mode Support - دعم الوضع عالي التباين
   ========================================================================== */

@media (prefers-contrast: high) {
    .badge {
        border: 2px solid currentColor;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
    
    .news-card {
        border: 2px solid var(--neutral-400);
    }
    
    .sidebar-widget {
        border: 2px solid var(--neutral-400);
    }
}

/* ==========================================================================
   Error States and Validation - حالات الخطأ والتحقق
   ========================================================================== */

/* حالات الخطأ في النماذج */
.form-input:invalid {
    border-color: var(--error-500);
    outline-color: var(--error-500);
}

.form-input:invalid:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
    color: var(--error-600);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.form-error::before {
    content: "⚠️";
    font-size: var(--text-xs);
}

/* حالات النجاح */
.form-success {
    color: var(--success-600);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.form-success::before {
    content: "✅";
    font-size: var(--text-xs);
}

/* ==========================================================================
   Loading States - حالات التحميل
   ========================================================================== */

/* مؤشر التحميل المتاح */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--neutral-200);
    border-top: 2px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--neutral-600);
    font-size: var(--text-sm);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

/* حالة التحميل للمحتوى */
.content-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.content-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    border: 3px solid var(--neutral-200);
    border-top: 3px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 10;
}

/* ==========================================================================
   Responsive Accessibility - الوصولية المتجاوبة
   ========================================================================== */

@media (max-width: 767px) {
    .touch-target {
        min-height: 48px;
        min-width: 48px;
    }
    
    .btn {
        min-height: 48px;
        font-size: var(--text-base);
    }
    
    .nav-link {
        min-height: 48px;
        font-size: var(--text-base);
    }
    
    .skip-link {
        left: var(--space-2);
        right: var(--space-2);
        text-align: center;
    }
}

/* ==========================================================================
   Print Accessibility - وصولية الطباعة
   ========================================================================== */

@media print {
    .skip-link,
    .back-to-top,
    .loading-spinner {
        display: none !important;
    }
    
    a::after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: var(--neutral-600);
    }
    
    .news-card {
        break-inside: avoid;
        border: 1px solid var(--neutral-400);
        margin-bottom: var(--space-4);
    }
}
