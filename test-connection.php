<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * يمكن حذف هذا الملف بعد التأكد من عمل النظام
 */

// تحديد المسار الجذر
define('ROOT_PATH', __DIR__);

// تحميل ملفات التكوين
require_once 'config/config.php';
require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال - موقع الأخبار</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>body { font-family: 'Ta<PERSON>wal', Arial, sans-serif; }</style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-lg p-8 max-w-2xl w-full">
            <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">اختبار النظام</h1>
            
            <div class="space-y-4">
                <!-- اختبار PHP -->
                <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full ml-3"></div>
                        <span class="font-medium">PHP</span>
                    </div>
                    <span class="text-green-700">إصدار <?php echo PHP_VERSION; ?></span>
                </div>

                <!-- اختبار قاعدة البيانات -->
                <?php
                try {
                    $database = new Database();
                    $db = $database->connect();
                    $db_status = 'متصلة';
                    $db_color = 'green';
                } catch (Exception $e) {
                    $db_status = 'غير متصلة: ' . $e->getMessage();
                    $db_color = 'red';
                }
                ?>
                <div class="flex items-center justify-between p-4 bg-<?php echo $db_color; ?>-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-<?php echo $db_color; ?>-500 rounded-full ml-3"></div>
                        <span class="font-medium">قاعدة البيانات</span>
                    </div>
                    <span class="text-<?php echo $db_color; ?>-700"><?php echo $db_status; ?></span>
                </div>

                <!-- اختبار الامتدادات المطلوبة -->
                <?php
                $required_extensions = ['pdo', 'pdo_mysql', 'simplexml', 'curl', 'mbstring'];
                foreach ($required_extensions as $ext):
                    $loaded = extension_loaded($ext);
                    $color = $loaded ? 'green' : 'red';
                    $status = $loaded ? 'مثبت' : 'غير مثبت';
                ?>
                <div class="flex items-center justify-between p-4 bg-<?php echo $color; ?>-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-<?php echo $color; ?>-500 rounded-full ml-3"></div>
                        <span class="font-medium"><?php echo strtoupper($ext); ?></span>
                    </div>
                    <span class="text-<?php echo $color; ?>-700"><?php echo $status; ?></span>
                </div>
                <?php endforeach; ?>

                <!-- اختبار الصلاحيات -->
                <?php
                $writable_dirs = ['logs'];
                foreach ($writable_dirs as $dir):
                    $writable = is_writable($dir);
                    $color = $writable ? 'green' : 'red';
                    $status = $writable ? 'قابل للكتابة' : 'غير قابل للكتابة';
                ?>
                <div class="flex items-center justify-between p-4 bg-<?php echo $color; ?>-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-<?php echo $color; ?>-500 rounded-full ml-3"></div>
                        <span class="font-medium">مجلد <?php echo $dir; ?></span>
                    </div>
                    <span class="text-<?php echo $color; ?>-700"><?php echo $status; ?></span>
                </div>
                <?php endforeach; ?>

                <!-- معلومات النظام -->
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-blue-800 mb-2">معلومات النظام</h3>
                    <div class="text-sm text-blue-700 space-y-1">
                        <p><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></p>
                        <p><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></p>
                        <p><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></p>
                        <p><strong>حد الرفع:</strong> <?php echo ini_get('upload_max_filesize'); ?></p>
                        <p><strong>وقت التنفيذ الأقصى:</strong> <?php echo ini_get('max_execution_time'); ?> ثانية</p>
                    </div>
                </div>
            </div>

            <!-- أزرار التنقل -->
            <div class="mt-8 flex space-x-4 space-x-reverse">
                <?php if ($db_color === 'green'): ?>
                <a href="setup.php" class="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg text-center hover:bg-blue-700 transition-colors">
                    إعداد قاعدة البيانات
                </a>
                <?php endif; ?>
                
                <a href="index.php" class="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg text-center hover:bg-gray-700 transition-colors">
                    الذهاب إلى الموقع
                </a>
            </div>

            <!-- تحذير -->
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <div class="w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center ml-2">
                        <span class="text-white text-xs">!</span>
                    </div>
                    <p class="text-yellow-800 text-sm">
                        <strong>تنبيه:</strong> احذف هذا الملف (test-connection.php) بعد التأكد من عمل النظام لأسباب أمنية.
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
