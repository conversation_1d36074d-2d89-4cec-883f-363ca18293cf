/*
 * Layout - التخطيط العام
 * ملف CSS للتخطيط العام والهيكل الأساسي للموقع
 */

/* ==========================================================================
   Page Layout - تخطيط الصفحة
   ========================================================================== */

.page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    padding: var(--space-8) 0;
}

/* Header Layout - تخطيط الهيدر */
.header-top {
    background: var(--neutral-800);
    color: var(--neutral-200);
    padding: var(--space-2) 0;
    font-size: var(--text-sm);
}

.header-main {
    background: white;
    padding: var(--space-4) 0;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: all var(--transition-base);
}

.header-main.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
    padding: var(--space-3) 0;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-6);
}

.header-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.header-nav {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

/* Navigation Layout - تخطيط التنقل */
.nav-categories {
    background: var(--primary-600);
    padding: var(--space-3) 0;
}

.nav-categories-list {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-categories-list::-webkit-scrollbar {
    display: none;
}

.nav-category-item {
    color: white;
    text-decoration: none;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-weight: var(--font-medium);
    white-space: nowrap;
    transition: all var(--transition-fast);
    font-size: var(--text-sm);
}

.nav-category-item:hover,
.nav-category-item.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Main Content Layout - تخطيط المحتوى الرئيسي */
.content-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
    margin: var(--space-8) 0;
}

@media (min-width: 1024px) {
    .content-wrapper {
        grid-template-columns: 1fr 300px;
    }
    
    .content-wrapper.full-width {
        grid-template-columns: 1fr;
    }
}

.main-column {
    min-width: 0;
}

.sidebar-column {
    min-width: 0;
}

/* Hero Section Layout - تخطيط القسم البطولي */
.hero-section {
    margin-bottom: var(--space-12);
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
}

@media (min-width: 1024px) {
    .hero-grid {
        grid-template-columns: 2fr 1fr;
    }
}

.hero-main {
    position: relative;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.hero-side {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

/* Articles Grid Layout - تخطيط شبكة المقالات */
.articles-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

@media (min-width: 640px) {
    .articles-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .articles-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1280px) {
    .articles-grid.large {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Category Header Layout - تخطيط هيدر التصنيف */
.category-header {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: white;
    padding: var(--space-12) 0;
    border-radius: var(--radius-2xl);
    margin-bottom: var(--space-8);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.category-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.category-header-content {
    position: relative;
    z-index: 1;
}

/* Article Page Layout - تخطيط صفحة المقال */
.article-header {
    margin-bottom: var(--space-8);
    text-align: center;
}

.article-meta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    margin: var(--space-4) 0;
    flex-wrap: wrap;
}

.article-content {
    max-width: 800px;
    margin: 0 auto;
    line-height: var(--leading-relaxed);
}

.article-content h2,
.article-content h3,
.article-content h4 {
    margin-top: var(--space-8);
    margin-bottom: var(--space-4);
}

.article-content p {
    margin-bottom: var(--space-6);
}

.article-content img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    margin: var(--space-6) 0;
    box-shadow: var(--shadow-md);
}

/* Footer Layout - تخطيط الفوتر */
.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

@media (min-width: 640px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

.footer-section {
    min-width: 0;
}

.footer-bottom {
    border-top: 1px solid var(--neutral-700);
    padding-top: var(--space-6);
    text-align: center;
    color: var(--neutral-400);
}

/* Search Layout - تخطيط البحث */
.search-form {
    display: flex;
    gap: var(--space-2);
    margin-bottom: var(--space-8);
}

.search-input {
    flex: 1;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-button {
    padding: var(--space-3) var(--space-6);
    background: var(--primary-600);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-button:hover {
    background: var(--primary-700);
}

/* Pagination Layout - تخطيط ترقيم الصفحات */
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin: var(--space-12) 0;
}

/* Breadcrumb Layout - تخطيط مسار التنقل */
.breadcrumb-wrapper {
    margin-bottom: var(--space-6);
    padding: var(--space-3) 0;
    border-bottom: 1px solid var(--neutral-200);
}

/* ==========================================================================
   Responsive Layout Adjustments - تعديلات التخطيط المتجاوب
   ========================================================================== */

@media (max-width: 1023px) {
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
    
    .sidebar-column {
        order: -1;
    }
}

@media (max-width: 767px) {
    .main-content {
        padding: var(--space-4) 0;
    }
    
    .header-container {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .header-nav {
        order: 1;
        width: 100%;
        justify-content: center;
    }
    
    .header-actions {
        order: 2;
    }
    
    .nav-categories-list {
        justify-content: flex-start;
        padding: 0 var(--space-4);
    }
    
    .hero-grid {
        gap: var(--space-4);
    }
    
    .articles-grid {
        gap: var(--space-4);
    }
    
    .category-header {
        padding: var(--space-8) var(--space-4);
        margin: 0 -var(--space-4) var(--space-6);
        border-radius: 0;
    }
    
    .article-meta {
        flex-direction: column;
        gap: var(--space-2);
    }
    
    .search-form {
        flex-direction: column;
    }
    
    .footer-content {
        gap: var(--space-6);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-3);
    }
    
    .header-main {
        padding: var(--space-3) 0;
    }
    
    .nav-categories {
        padding: var(--space-2) 0;
    }
    
    .content-wrapper {
        margin: var(--space-4) 0;
        gap: var(--space-4);
    }
    
    .articles-grid {
        gap: var(--space-3);
    }
}

/* ==========================================================================
   Print Layout - تخطيط الطباعة
   ========================================================================== */

@media print {
    .header-main,
    .nav-categories,
    .sidebar-column,
    .footer-content,
    .back-to-top,
    .search-form {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 0;
        margin: 0;
    }
    
    .news-card,
    .article-content {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
        margin-bottom: 1rem;
    }
}
