/**
 * Breaking News Ticker Advanced JavaScript
 * جافا سكريبت متقدم لشريط الأخبار العاجلة
 */

class BreakingNewsTicker {
    constructor() {
        this.ticker = document.getElementById('breaking-news-ticker');
        this.marquee = document.getElementById('news-marquee');
        this.pauseBtn = document.getElementById('ticker-pause');
        this.closeBtn = document.getElementById('ticker-close');
        
        this.isPlaying = true;
        this.isPaused = false;
        this.isHovered = false;
        this.refreshInterval = null;
        this.animationSpeed = 45; // seconds
        
        this.init();
    }
    
    init() {
        if (!this.ticker || !this.marquee) return;
        
        this.setupEventListeners();
        this.setupAnimation();
        this.setupAutoRefresh();
        this.checkUserPreferences();
        this.setupKeyboardControls();
        this.setupTouchControls();
    }
    
    setupEventListeners() {
        // Pause/Play button
        if (this.pauseBtn) {
            this.pauseBtn.addEventListener('click', () => this.togglePlayPause());
        }
        
        // Close button
        if (this.closeBtn) {
            this.closeBtn.addEventListener('click', () => this.closeTicker());
        }
        
        // Hover events
        this.marquee.addEventListener('mouseenter', () => this.onHoverStart());
        this.marquee.addEventListener('mouseleave', () => this.onHoverEnd());
        
        // Click events on news items
        this.setupNewsItemClicks();
        
        // Window visibility change
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // Resize events
        window.addEventListener('resize', () => this.handleResize());
    }
    
    setupAnimation() {
        // Create dynamic CSS for animation
        const style = document.createElement('style');
        style.id = 'breaking-news-animation';
        style.textContent = this.generateAnimationCSS();
        document.head.appendChild(style);
        
        // Apply animation class
        this.marquee.classList.add('news-marquee-animate');
    }
    
    generateAnimationCSS() {
        const speed = this.getResponsiveSpeed();
        return `
            @keyframes breakingNewsFlow {
                0% { transform: translateX(100%); }
                100% { transform: translateX(-100%); }
            }
            
            .news-marquee-animate {
                animation: breakingNewsFlow ${speed}s linear infinite;
            }
            
            .news-marquee-paused {
                animation-play-state: paused !important;
            }
            
            .news-marquee-hover {
                animation-play-state: paused !important;
            }
            
            @media (max-width: 768px) {
                .news-marquee-animate {
                    animation-duration: ${speed * 0.8}s;
                }
            }
            
            @media (max-width: 480px) {
                .news-marquee-animate {
                    animation-duration: ${speed * 0.7}s;
                }
            }
        `;
    }
    
    getResponsiveSpeed() {
        const width = window.innerWidth;
        if (width < 480) return this.animationSpeed * 0.7;
        if (width < 768) return this.animationSpeed * 0.8;
        return this.animationSpeed;
    }
    
    setupNewsItemClicks() {
        const newsLinks = this.marquee.querySelectorAll('a');
        newsLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Add click tracking
                this.trackNewsClick(link.href, link.textContent);
            });
        });
    }
    
    trackNewsClick(url, title) {
        // Track news item clicks for analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'breaking_news_click', {
                'news_url': url,
                'news_title': title
            });
        }
        
        // Store in localStorage for internal analytics
        const clicks = JSON.parse(localStorage.getItem('breakingNewsClicks') || '[]');
        clicks.push({
            url: url,
            title: title,
            timestamp: new Date().toISOString()
        });
        
        // Keep only last 50 clicks
        if (clicks.length > 50) {
            clicks.splice(0, clicks.length - 50);
        }
        
        localStorage.setItem('breakingNewsClicks', JSON.stringify(clicks));
    }
    
    togglePlayPause() {
        const icon = this.pauseBtn.querySelector('i');
        
        if (this.isPlaying) {
            this.pause();
            icon.className = 'fas fa-play text-sm';
            this.pauseBtn.title = 'تشغيل';
        } else {
            this.play();
            icon.className = 'fas fa-pause text-sm';
            this.pauseBtn.title = 'إيقاف';
        }
        
        this.isPlaying = !this.isPlaying;
        this.saveUserPreference('paused', !this.isPlaying);
    }
    
    pause() {
        this.marquee.classList.add('news-marquee-paused');
        this.ticker.classList.add('paused');
    }
    
    play() {
        this.marquee.classList.remove('news-marquee-paused');
        this.ticker.classList.remove('paused');
    }
    
    onHoverStart() {
        if (this.isPlaying) {
            this.marquee.classList.add('news-marquee-hover');
            this.ticker.classList.add('hover-paused');
            this.isHovered = true;
        }
    }
    
    onHoverEnd() {
        this.marquee.classList.remove('news-marquee-hover');
        this.ticker.classList.remove('hover-paused');
        this.isHovered = false;
    }
    
    closeTicker() {
        this.ticker.classList.add('closing');
        
        // Animate close
        this.ticker.style.transform = 'translateY(-100%)';
        this.ticker.style.transition = 'transform 0.5s ease-out, opacity 0.5s ease-out';
        this.ticker.style.opacity = '0';
        
        setTimeout(() => {
            this.ticker.style.display = 'none';
            this.saveUserPreference('hidden', true);
            this.saveUserPreference('hiddenTime', new Date().getTime());
        }, 500);
    }
    
    setupAutoRefresh() {
        // Refresh content every 5 minutes
        this.refreshInterval = setInterval(() => {
            if (document.visibilityState === 'visible' && !this.isHovered) {
                this.refreshContent();
            }
        }, 300000); // 5 minutes
    }
    
    async refreshContent() {
        try {
            const response = await fetch(window.location.href, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) throw new Error('Network response was not ok');
            
            const html = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newMarquee = doc.getElementById('news-marquee');
            
            if (newMarquee && newMarquee.innerHTML !== this.marquee.innerHTML) {
                this.updateContent(newMarquee.innerHTML);
            }
        } catch (error) {
            console.warn('Failed to refresh breaking news content:', error);
        }
    }
    
    updateContent(newContent) {
        // Fade out
        this.marquee.style.opacity = '0.5';
        this.marquee.style.transition = 'opacity 0.3s ease';
        
        setTimeout(() => {
            // Update content
            this.marquee.innerHTML = newContent;
            
            // Restart animation
            this.marquee.classList.remove('news-marquee-animate');
            
            setTimeout(() => {
                this.marquee.classList.add('news-marquee-animate');
                this.marquee.style.opacity = '1';
                
                // Re-setup click events
                this.setupNewsItemClicks();
            }, 100);
        }, 300);
    }
    
    handleVisibilityChange() {
        if (document.visibilityState === 'hidden') {
            // Pause when tab is not visible
            if (this.isPlaying) {
                this.pause();
                this.isPaused = true;
            }
        } else {
            // Resume when tab becomes visible
            if (this.isPaused && this.isPlaying) {
                this.play();
                this.isPaused = false;
            }
        }
    }
    
    handleResize() {
        // Update animation speed based on screen size
        const style = document.getElementById('breaking-news-animation');
        if (style) {
            style.textContent = this.generateAnimationCSS();
        }
    }
    
    setupKeyboardControls() {
        document.addEventListener('keydown', (e) => {
            // Only if ticker is focused or no other input is focused
            if (document.activeElement.tagName === 'INPUT' || 
                document.activeElement.tagName === 'TEXTAREA') {
                return;
            }
            
            switch(e.key) {
                case ' ': // Spacebar to pause/play
                    e.preventDefault();
                    if (this.pauseBtn) this.togglePlayPause();
                    break;
                case 'Escape': // Escape to close
                    if (this.closeBtn) this.closeTicker();
                    break;
            }
        });
    }
    
    setupTouchControls() {
        let touchStartX = 0;
        let touchEndX = 0;
        
        this.ticker.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });
        
        this.ticker.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            this.handleSwipe();
        });
        
        const handleSwipe = () => {
            const swipeDistance = touchEndX - touchStartX;
            
            if (Math.abs(swipeDistance) > 50) {
                if (swipeDistance > 0) {
                    // Swipe right - pause/play
                    this.togglePlayPause();
                } else {
                    // Swipe left - close
                    this.closeTicker();
                }
            }
        };
        
        this.handleSwipe = handleSwipe;
    }
    
    checkUserPreferences() {
        const hidden = this.getUserPreference('hidden');
        const hiddenTime = this.getUserPreference('hiddenTime');
        const paused = this.getUserPreference('paused');
        
        // Check if ticker should be hidden
        if (hidden === 'true') {
            const currentTime = new Date().getTime();
            const oneHour = 3600000; // 1 hour in milliseconds
            
            if (!hiddenTime || (currentTime - parseInt(hiddenTime)) > oneHour) {
                // Show again after 1 hour
                this.clearUserPreference('hidden');
                this.clearUserPreference('hiddenTime');
            } else {
                this.ticker.style.display = 'none';
                return;
            }
        }
        
        // Check if ticker should be paused
        if (paused === 'true') {
            this.togglePlayPause();
        }
    }
    
    saveUserPreference(key, value) {
        localStorage.setItem(`breakingNews_${key}`, value.toString());
    }
    
    getUserPreference(key) {
        return localStorage.getItem(`breakingNews_${key}`);
    }
    
    clearUserPreference(key) {
        localStorage.removeItem(`breakingNews_${key}`);
    }
    
    destroy() {
        // Clean up
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        // Remove event listeners
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        
        // Remove animation styles
        const style = document.getElementById('breaking-news-animation');
        if (style) {
            style.remove();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.breakingNewsTicker = new BreakingNewsTicker();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BreakingNewsTicker;
}
