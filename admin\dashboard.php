<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

checkAuth();

$page_title = 'لوحة التحكم الرئيسية';

// الحصول على الإحصائيات
$database = new Database();
$db = $database->connect();

// إحصائيات المقالات
$stmt = $db->query("SELECT COUNT(*) FROM articles");
$total_articles = $stmt->fetchColumn();

$stmt = $db->query("SELECT COUNT(*) FROM articles WHERE DATE(created_at) = CURDATE()");
$today_articles = $stmt->fetchColumn();

$stmt = $db->query("SELECT COUNT(*) FROM articles WHERE is_featured = 1");
$featured_articles = $stmt->fetchColumn();

// إحصائيات التصنيفات
$stmt = $db->query("SELECT COUNT(*) FROM categories");
$total_categories = $stmt->fetchColumn();

// إحصائيات مصادر RSS
$stmt = $db->query("SELECT COUNT(*) FROM rss_sources");
$total_rss_sources = $stmt->fetchColumn();

$stmt = $db->query("SELECT COUNT(*) FROM rss_sources WHERE is_active = 1");
$active_rss_sources = $stmt->fetchColumn();

// إحصائيات المشاهدات
$stmt = $db->query("SELECT SUM(views) FROM articles");
$total_views = $stmt->fetchColumn() ?: 0;

// أحدث المقالات
$stmt = $db->prepare("SELECT a.*, c.name as category_name FROM articles a LEFT JOIN categories c ON a.category_id = c.id ORDER BY a.created_at DESC LIMIT 5");
$stmt->execute();
$recent_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

// إحصائيات المقالات حسب التصنيف
$stmt = $db->query("SELECT c.name, COUNT(a.id) as count FROM categories c LEFT JOIN articles a ON c.id = a.category_id GROUP BY c.id, c.name ORDER BY count DESC");
$category_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

// إحصائيات المقالات الأسبوعية
$stmt = $db->query("
    SELECT DATE(created_at) as date, COUNT(*) as count 
    FROM articles 
    WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) 
    GROUP BY DATE(created_at) 
    ORDER BY date ASC
");
$weekly_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<!-- Dashboard Content -->
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
        <h1 class="text-2xl font-bold mb-2">مرحباً، <?php echo $_SESSION['full_name'] ?? $_SESSION['username']; ?>!</h1>
        <p class="text-blue-100">إليك نظرة عامة على إحصائيات موقعك الإخباري</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Articles -->
        <div class="bg-white rounded-lg shadow-md p-6 admin-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">إجمالي المقالات</p>
                    <p class="text-3xl font-bold text-gray-800"><?php echo number_format($total_articles); ?></p>
                </div>
                <div class="bg-blue-100 p-3 rounded-full">
                    <i class="fas fa-newspaper text-blue-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600 font-medium">+<?php echo $today_articles; ?></span>
                <span class="text-gray-600 mr-2">اليوم</span>
            </div>
        </div>

        <!-- Total Views -->
        <div class="bg-white rounded-lg shadow-md p-6 admin-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">إجمالي المشاهدات</p>
                    <p class="text-3xl font-bold text-gray-800"><?php echo number_format($total_views); ?></p>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                    <i class="fas fa-eye text-green-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-blue-600 font-medium"><?php echo $featured_articles; ?></span>
                <span class="text-gray-600 mr-2">مقال مميز</span>
            </div>
        </div>

        <!-- Categories -->
        <div class="bg-white rounded-lg shadow-md p-6 admin-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">التصنيفات</p>
                    <p class="text-3xl font-bold text-gray-800"><?php echo number_format($total_categories); ?></p>
                </div>
                <div class="bg-yellow-100 p-3 rounded-full">
                    <i class="fas fa-folder text-yellow-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-gray-600">تصنيفات نشطة</span>
            </div>
        </div>

        <!-- RSS Sources -->
        <div class="bg-white rounded-lg shadow-md p-6 admin-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">مصادر RSS</p>
                    <p class="text-3xl font-bold text-gray-800"><?php echo number_format($total_rss_sources); ?></p>
                </div>
                <div class="bg-purple-100 p-3 rounded-full">
                    <i class="fas fa-rss text-purple-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600 font-medium"><?php echo $active_rss_sources; ?></span>
                <span class="text-gray-600 mr-2">نشط</span>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Weekly Articles Chart -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">المقالات الأسبوعية</h3>
            <canvas id="weeklyChart" width="400" height="200"></canvas>
        </div>

        <!-- Category Distribution -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">توزيع المقالات حسب التصنيف</h3>
            <canvas id="categoryChart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Recent Articles and Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Articles -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">أحدث المقالات</h3>
                <a href="articles.php" class="text-blue-600 hover:text-blue-800 text-sm">عرض الكل</a>
            </div>
            
            <?php if (!empty($recent_articles)): ?>
            <div class="space-y-4">
                <?php foreach ($recent_articles as $article): ?>
                <div class="flex items-center space-x-4 space-x-reverse p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-newspaper text-gray-500"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-gray-800 truncate">
                            <a href="articles.php?edit=<?php echo $article['id']; ?>" class="hover:text-blue-600">
                                <?php echo $article['title']; ?>
                            </a>
                        </h4>
                        <div class="flex items-center text-sm text-gray-500 mt-1">
                            <span><?php echo $article['category_name'] ?? 'بدون تصنيف'; ?></span>
                            <span class="mx-2">•</span>
                            <span><?php echo formatArabicDate($article['created_at']); ?></span>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-eye ml-1"></i>
                        <?php echo number_format($article['views']); ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-newspaper text-4xl mb-3"></i>
                <p>لا توجد مقالات بعد</p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">إجراءات سريعة</h3>
            <div class="space-y-3">
                <a href="articles.php?action=add" class="flex items-center p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="fas fa-plus ml-3"></i>
                    إضافة مقال جديد
                </a>
                
                <a href="categories.php?action=add" class="flex items-center p-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-folder-plus ml-3"></i>
                    إضافة تصنيف
                </a>
                
                <a href="rss-sources.php?action=add" class="flex items-center p-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors">
                    <i class="fas fa-rss ml-3"></i>
                    إضافة مصدر RSS
                </a>
                
                <button onclick="fetchRSS()" class="w-full flex items-center p-3 bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors">
                    <i class="fas fa-sync-alt ml-3"></i>
                    جلب أخبار RSS
                </button>
                
                <?php if ($_SESSION['user_role'] === 'admin'): ?>
                <a href="settings.php" class="flex items-center p-3 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="fas fa-cog ml-3"></i>
                    إعدادات الموقع
                </a>

                <a href="init-data.php" class="flex items-center p-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors">
                    <i class="fas fa-database ml-3"></i>
                    إضافة البيانات الأولية
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">حالة النظام</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-database text-green-600 ml-2"></i>
                    <span class="text-green-800">قاعدة البيانات</span>
                </div>
                <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">متصلة</span>
            </div>
            
            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-rss text-blue-600 ml-2"></i>
                    <span class="text-blue-800">مصادر RSS</span>
                </div>
                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs"><?php echo $active_rss_sources; ?> نشط</span>
            </div>
            
            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-server text-yellow-600 ml-2"></i>
                    <span class="text-yellow-800">الخادم</span>
                </div>
                <span class="bg-yellow-600 text-white px-2 py-1 rounded text-xs">يعمل</span>
            </div>
        </div>
    </div>
</div>

<script>
// Weekly Articles Chart
const weeklyCtx = document.getElementById('weeklyChart').getContext('2d');
const weeklyData = <?php echo json_encode($weekly_stats); ?>;

new Chart(weeklyCtx, {
    type: 'line',
    data: {
        labels: weeklyData.map(item => item.date),
        datasets: [{
            label: 'المقالات',
            data: weeklyData.map(item => item.count),
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// Category Distribution Chart
const categoryCtx = document.getElementById('categoryChart').getContext('2d');
const categoryData = <?php echo json_encode($category_stats); ?>;

new Chart(categoryCtx, {
    type: 'doughnut',
    data: {
        labels: categoryData.map(item => item.name),
        datasets: [{
            data: categoryData.map(item => item.count),
            backgroundColor: [
                '#3B82F6',
                '#10B981',
                '#F59E0B',
                '#EF4444',
                '#8B5CF6',
                '#06B6D4'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// RSS Fetch function
function fetchRSS() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري الجلب...';
    button.disabled = true;

    fetch('fetch-rss.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم جلب ${data.articles_count} مقال جديد بنجاح`);
                location.reload();
            } else {
                alert('حدث خطأ أثناء جلب RSS: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
            console.error('Error:', error);
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
}
</script>

<?php include 'includes/footer.php'; ?>
