<?php
session_start();
require_once '../config/database.php';
require_once '../classes/RSSParser.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول للطلبات من المتصفح
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
    checkAuth();
}

header('Content-Type: application/json');

try {
    $rssParser = new RSSParser();
    $articles_count = $rssParser->fetchAllActiveFeeds();
    
    if ($articles_count !== false) {
        echo json_encode([
            'success' => true,
            'articles_count' => $articles_count,
            'message' => "تم جلب $articles_count مقال جديد بنجاح"
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء جلب مصادر RSS'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>
