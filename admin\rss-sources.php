<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../classes/RSSParser.php';
require_once 'includes/auth.php';

checkAuth();

$page_title = 'إدارة مصادر RSS';
$database = new Database();
$db = $database->connect();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$source_id = $_GET['id'] ?? null;
$edit_id = $_GET['edit'] ?? null;

$success_message = '';
$error_message = '';

// حذف مصدر RSS
if ($action === 'delete' && $source_id) {
    try {
        $stmt = $db->prepare("DELETE FROM rss_sources WHERE id = ?");
        if ($stmt->execute([$source_id])) {
            $success_message = 'تم حذف مصدر RSS بنجاح';
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حذف مصدر RSS';
    }
    $action = 'list';
}

// تبديل حالة التفعيل
if ($action === 'toggle_active' && $source_id) {
    try {
        $stmt = $db->prepare("UPDATE rss_sources SET is_active = NOT is_active WHERE id = ?");
        if ($stmt->execute([$source_id])) {
            $success_message = 'تم تحديث حالة مصدر RSS';
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء تحديث مصدر RSS';
    }
    $action = 'list';
}

// اختبار مصدر RSS
if ($action === 'test' && $source_id) {
    try {
        $stmt = $db->prepare("SELECT url FROM rss_sources WHERE id = ?");
        $stmt->execute([$source_id]);
        $source = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($source) {
            $rssParser = new RSSParser();
            $result = $rssParser->testRSSFeed($source['url']);
            
            if ($result['success']) {
                $success_message = "تم اختبار المصدر بنجاح. تم العثور على {$result['items_count']} عنصر.";
            } else {
                $error_message = "فشل في اختبار المصدر: " . $result['error'];
            }
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء اختبار مصدر RSS';
    }
    $action = 'list';
}

// جلب من مصدر RSS محدد
if ($action === 'fetch' && $source_id) {
    try {
        $rssParser = new RSSParser();
        $articles_count = $rssParser->fetchRSSFeed($source_id);
        
        if ($articles_count !== false) {
            $success_message = "تم جلب $articles_count مقال جديد من المصدر";
        } else {
            $error_message = 'فشل في جلب المقالات من المصدر';
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء جلب المقالات';
    }
    $action = 'list';
}

// إضافة أو تعديل مصدر RSS
if ($_SERVER['REQUEST_METHOD'] === 'POST' && in_array($action, ['add', 'edit'])) {
    $name = trim($_POST['name']);
    $url = trim($_POST['url']);
    $category_id = $_POST['category_id'] ?: null;
    $fetch_interval = (int)$_POST['fetch_interval'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if (!empty($name) && !empty($url)) {
        // التحقق من صحة URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            $error_message = 'رابط RSS غير صحيح';
        } else {
            try {
                if ($action === 'add') {
                    // التحقق من عدم وجود مصدر بنفس الرابط
                    $stmt = $db->prepare("SELECT COUNT(*) FROM rss_sources WHERE url = ?");
                    $stmt->execute([$url]);
                    if ($stmt->fetchColumn() > 0) {
                        $error_message = 'يوجد مصدر RSS بهذا الرابط بالفعل';
                    } else {
                        $stmt = $db->prepare("
                            INSERT INTO rss_sources (name, url, category_id, fetch_interval, is_active) 
                            VALUES (?, ?, ?, ?, ?)
                        ");
                        if ($stmt->execute([$name, $url, $category_id, $fetch_interval, $is_active])) {
                            $success_message = 'تم إضافة مصدر RSS بنجاح';
                            $action = 'list';
                        }
                    }
                } elseif ($action === 'edit' && $edit_id) {
                    // التحقق من عدم وجود مصدر آخر بنفس الرابط
                    $stmt = $db->prepare("SELECT COUNT(*) FROM rss_sources WHERE url = ? AND id != ?");
                    $stmt->execute([$url, $edit_id]);
                    if ($stmt->fetchColumn() > 0) {
                        $error_message = 'يوجد مصدر RSS آخر بهذا الرابط بالفعل';
                    } else {
                        $stmt = $db->prepare("
                            UPDATE rss_sources 
                            SET name = ?, url = ?, category_id = ?, fetch_interval = ?, is_active = ? 
                            WHERE id = ?
                        ");
                        if ($stmt->execute([$name, $url, $category_id, $fetch_interval, $is_active, $edit_id])) {
                            $success_message = 'تم تحديث مصدر RSS بنجاح';
                            $action = 'list';
                        }
                    }
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء حفظ مصدر RSS: ' . $e->getMessage();
            }
        }
    } else {
        $error_message = 'يرجى إدخال اسم المصدر والرابط';
    }
}

// الحصول على بيانات المصدر للتعديل
$source_data = null;
if (($action === 'edit' && $edit_id) || ($action === 'add' && $edit_id)) {
    $stmt = $db->prepare("SELECT * FROM rss_sources WHERE id = ?");
    $stmt->execute([$edit_id]);
    $source_data = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($source_data) {
        $action = 'edit';
    }
}

// الحصول على التصنيفات
$stmt = $db->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// الحصول على مصادر RSS مع إحصائيات
$stmt = $db->query("
    SELECT r.*, c.name as category_name, COUNT(a.id) as articles_count,
           MAX(a.created_at) as last_article_date
    FROM rss_sources r 
    LEFT JOIN categories c ON r.category_id = c.id 
    LEFT JOIN articles a ON r.id = a.rss_source_id 
    GROUP BY r.id 
    ORDER BY r.name
");
$rss_sources = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">إدارة مصادر RSS</h1>
        <?php if ($action === 'list'): ?>
        <div class="flex space-x-2 space-x-reverse">
            <button onclick="fetchAllRSS()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-sync-alt ml-2"></i>جلب من جميع المصادر
            </button>
            <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus ml-2"></i>إضافة مصدر جديد
            </a>
        </div>
        <?php else: ?>
        <a href="?" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            <i class="fas fa-arrow-right ml-2"></i>العودة للقائمة
        </a>
        <?php endif; ?>
    </div>

    <!-- Messages -->
    <?php if ($success_message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- RSS Sources List -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold">قائمة مصادر RSS</h2>
        </div>

        <?php if (!empty($rss_sources)): ?>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم المصدر</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التصنيف</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد المقالات</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر جلب</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($rss_sources as $source): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-rss text-purple-600"></i>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($source['name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($source['url']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $source['category_name'] ?? 'بدون تصنيف'; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                <?php echo number_format($source['articles_count']); ?> مقال
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo $source['last_fetched'] ? formatArabicDate($source['last_fetched']) : 'لم يتم الجلب بعد'; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php if ($source['is_active']): ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">نشط</span>
                            <?php else: ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">معطل</span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="?edit=<?php echo $source['id']; ?>" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit ml-1"></i>تعديل
                                </a>
                                <a href="?action=test&id=<?php echo $source['id']; ?>" class="text-green-600 hover:text-green-900">
                                    <i class="fas fa-vial ml-1"></i>اختبار
                                </a>
                                <a href="?action=fetch&id=<?php echo $source['id']; ?>" class="text-purple-600 hover:text-purple-900">
                                    <i class="fas fa-download ml-1"></i>جلب
                                </a>
                                <a href="?action=toggle_active&id=<?php echo $source['id']; ?>" class="text-yellow-600 hover:text-yellow-900">
                                    <i class="fas fa-power-off ml-1"></i><?php echo $source['is_active'] ? 'تعطيل' : 'تفعيل'; ?>
                                </a>
                                <a href="?action=delete&id=<?php echo $source['id']; ?>" 
                                   onclick="return confirm('هل أنت متأكد من حذف هذا المصدر؟')" 
                                   class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash ml-1"></i>حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-12">
            <i class="fas fa-rss text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مصادر RSS</h3>
            <p class="text-gray-500 mb-4">ابدأ بإضافة مصادر RSS لجلب الأخبار تلقائياً</p>
            <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                إضافة مصدر جديد
            </a>
        </div>
        <?php endif; ?>
    </div>

    <?php else: ?>
    <!-- Add/Edit Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold mb-6">
            <?php echo $action === 'add' ? 'إضافة مصدر RSS جديد' : 'تعديل مصدر RSS'; ?>
        </h2>

        <form method="POST" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المصدر *</label>
                    <input type="text" name="name" required 
                           value="<?php echo $source_data['name'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="مثال: موقع الأخبار العربية">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">التصنيف</label>
                    <select name="category_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">اختر التصنيف (اختياري)</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" 
                                <?php echo ($source_data['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo $category['name']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط RSS *</label>
                    <input type="url" name="url" required 
                           value="<?php echo $source_data['url'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="https://example.com/rss.xml">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">فترة الجلب (بالثواني)</label>
                    <select name="fetch_interval" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="1800" <?php echo ($source_data['fetch_interval'] ?? 3600) == 1800 ? 'selected' : ''; ?>>30 دقيقة</option>
                        <option value="3600" <?php echo ($source_data['fetch_interval'] ?? 3600) == 3600 ? 'selected' : ''; ?>>ساعة واحدة</option>
                        <option value="7200" <?php echo ($source_data['fetch_interval'] ?? 3600) == 7200 ? 'selected' : ''; ?>>ساعتان</option>
                        <option value="21600" <?php echo ($source_data['fetch_interval'] ?? 3600) == 21600 ? 'selected' : ''; ?>>6 ساعات</option>
                        <option value="43200" <?php echo ($source_data['fetch_interval'] ?? 3600) == 43200 ? 'selected' : ''; ?>>12 ساعة</option>
                        <option value="86400" <?php echo ($source_data['fetch_interval'] ?? 3600) == 86400 ? 'selected' : ''; ?>>24 ساعة</option>
                    </select>
                </div>

                <div class="flex items-center">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" 
                               <?php echo ($source_data['is_active'] ?? 1) ? 'checked' : ''; ?>
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">مصدر نشط</span>
                    </label>
                </div>
            </div>

            <div class="flex justify-end space-x-3 space-x-reverse">
                <a href="?" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    إلغاء
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <?php echo $action === 'add' ? 'إضافة المصدر' : 'تحديث المصدر'; ?>
                </button>
            </div>
        </form>
    </div>
    <?php endif; ?>
</div>

<script>
function fetchAllRSS() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري الجلب...';
    button.disabled = true;
    
    fetch('fetch-rss.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم جلب ${data.articles_count} مقال جديد بنجاح`);
                location.reload();
            } else {
                alert('حدث خطأ أثناء جلب RSS: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
            console.error('Error:', error);
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
}
</script>

<?php include 'includes/footer.php'; ?>
