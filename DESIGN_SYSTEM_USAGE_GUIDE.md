# 📖 دليل استخدام نظام التصميم الموحد

## 🎨 الملفات الأساسية

### ملفات CSS المطلوبة (بالترتيب):
1. `assets/css/design-system.css` - المتغيرات والأساسيات
2. `assets/css/layout.css` - التخطيط العام
3. `assets/css/components.css` - المكونات

### ملفات CSS الاختيارية:
- `assets/css/homepage-enhancements.css` - للصفحة الرئيسية فقط
- `assets/css/matches-widget.css` - لجدول المباريات

## 🔧 كيفية الاستخدام

### في ملف header.php:
```html
<!-- Design System CSS -->
<link rel="stylesheet" href="assets/css/design-system.css">
<link rel="stylesheet" href="assets/css/layout.css">
<link rel="stylesheet" href="assets/css/components.css">
```

### استخدام المتغيرات:
```css
.my-element {
    color: var(--primary-600);
    font-size: var(--text-lg);
    padding: var(--space-4);
    border-radius: var(--radius-md);
}
```

### استخدام الفئات الجاهزة:
```html
<button class="btn btn-primary">زر أساسي</button>
<div class="card">بطاقة</div>
<span class="badge badge-success">شارة نجاح</span>
```

## 🎨 نظام الألوان

### الألوان الأساسية:
- `--primary-600` - اللون الأساسي
- `--secondary-500` - اللون الثانوي
- `--success-600` - لون النجاح
- `--warning-500` - لون التحذير
- `--error-600` - لون الخطأ

### الألوان المحايدة:
- `--neutral-50` إلى `--neutral-900`

## 📏 نظام المسافات

- `--space-1` (4px) إلى `--space-24` (96px)
- استخدم `--space-4` (16px) كمسافة أساسية

## 🔤 نظام الطباعة

### أحجام الخطوط:
- `--text-xs` (12px) إلى `--text-6xl` (60px)
- `--text-base` (16px) هو الحجم الأساسي

### أوزان الخطوط:
- `--font-light` (300) إلى `--font-bold` (700)

## 📱 التصميم المتجاوب

### نقاط التوقف:
- موبايل: أقل من 768px
- تابلت: 768px - 1023px  
- ديسكتوب: 1024px فما فوق

## ⚠️ ملاحظات مهمة

1. استخدم دائماً متغيرات النظام بدلاً من القيم المباشرة
2. اتبع نمط التسمية المحدد للفئات
3. اختبر التصميم على أحجام شاشات مختلفة
4. تأكد من التباين المناسب للوصولية

## 🔗 روابط مفيدة

- صفحة التدقيق: `design-consistency-audit.php`
- تقرير التحسينات: `DESIGN_CONSISTENCY_REPORT.md`
