<?php
require_once dirname(__DIR__) . '/config/database.php';

class RSSParser {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->connect();
    }
    
    /**
     * اختبار موجز RSS
     */
    public function testRSSFeed($url) {
        try {
            $rss_content = $this->fetchRSSContent($url);
            if (!$rss_content) {
                return ['success' => false, 'error' => 'فشل في جلب محتوى RSS'];
            }

            $xml = simplexml_load_string($rss_content);
            if (!$xml) {
                return ['success' => false, 'error' => 'محتوى RSS غير صالح'];
            }

            $items_count = 0;
            if (isset($xml->channel->item)) {
                $items_count = count($xml->channel->item);
            } elseif (isset($xml->entry)) {
                $items_count = count($xml->entry);
            }

            return [
                'success' => true,
                'items_count' => $items_count,
                'title' => (string)($xml->channel->title ?? $xml->title ?? 'غير محدد')
            ];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * جلب وتحليل موجز RSS
     */
    public function fetchRSSFeed($rss_source_id) {
        try {
            // الحصول على معلومات مصدر RSS
            $stmt = $this->db->prepare("SELECT * FROM rss_sources WHERE id = ? AND is_active = 1");
            $stmt->execute([$rss_source_id]);
            $source = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$source) {
                return false;
            }
            
            // جلب محتوى RSS
            $rss_content = $this->fetchRSSContent($source['url']);
            if (!$rss_content) {
                return false;
            }
            
            // تحليل XML
            $xml = simplexml_load_string($rss_content);
            if (!$xml) {
                return false;
            }
            
            $articles_added = 0;
            
            // تحليل العناصر
            if (isset($xml->channel->item)) {
                foreach ($xml->channel->item as $item) {
                    $article_data = $this->parseRSSItem($item, $source);
                    if ($this->saveArticle($article_data)) {
                        $articles_added++;
                    }
                }
            } elseif (isset($xml->entry)) {
                // Atom feed
                foreach ($xml->entry as $entry) {
                    $article_data = $this->parseAtomEntry($entry, $source);
                    if ($this->saveArticle($article_data)) {
                        $articles_added++;
                    }
                }
            }
            
            // تحديث وقت آخر جلب
            $this->updateLastFetched($rss_source_id);
            
            return $articles_added;
            
        } catch (Exception $e) {
            error_log("RSS Fetch Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * جلب محتوى RSS من URL
     */
    private function fetchRSSContent($url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (compatible; NewsBot/1.0)',
                'follow_location' => true
            ]
        ]);
        
        return file_get_contents($url, false, $context);
    }
    
    /**
     * تحليل عنصر RSS
     */
    private function parseRSSItem($item, $source) {
        $title = (string) $item->title;
        $description = (string) $item->description;
        $link = (string) $item->link;
        $pub_date = (string) $item->pubDate;
        $author = (string) $item->author;
        
        // استخراج الصورة من المحتوى
        $image_url = $this->extractImageFromContent($description);
        
        // تنظيف المحتوى
        $content = $this->cleanContent($description);
        $excerpt = $this->generateExcerpt($content);
        
        // تحويل التاريخ
        $published_at = $this->parseDate($pub_date);
        
        // إنشاء slug
        $slug = $this->generateSlug($title);
        
        return [
            'title' => $title,
            'slug' => $slug,
            'content' => $content,
            'excerpt' => $excerpt,
            'image_url' => $image_url,
            'source_url' => $link,
            'author' => $author,
            'category_id' => $source['category_id'],
            'rss_source_id' => $source['id'],
            'published_at' => $published_at
        ];
    }
    
    /**
     * تحليل عنصر Atom
     */
    private function parseAtomEntry($entry, $source) {
        $title = (string) $entry->title;
        $content = (string) $entry->content;
        $link = (string) $entry->link['href'];
        $published = (string) $entry->published;
        $author = (string) $entry->author->name;
        
        $image_url = $this->extractImageFromContent($content);
        $clean_content = $this->cleanContent($content);
        $excerpt = $this->generateExcerpt($clean_content);
        $published_at = $this->parseDate($published);
        $slug = $this->generateSlug($title);
        
        return [
            'title' => $title,
            'slug' => $slug,
            'content' => $clean_content,
            'excerpt' => $excerpt,
            'image_url' => $image_url,
            'source_url' => $link,
            'author' => $author,
            'category_id' => $source['category_id'],
            'rss_source_id' => $source['id'],
            'published_at' => $published_at
        ];
    }
    
    /**
     * حفظ المقال في قاعدة البيانات
     */
    private function saveArticle($article_data) {
        try {
            // التحقق من وجود المقال
            $stmt = $this->db->prepare("SELECT id FROM articles WHERE source_url = ?");
            $stmt->execute([$article_data['source_url']]);
            
            if ($stmt->fetch()) {
                return false; // المقال موجود بالفعل
            }
            
            // إدراج المقال الجديد
            $sql = "INSERT INTO articles (title, slug, content, excerpt, image_url, source_url, author, category_id, rss_source_id, published_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                $article_data['title'],
                $article_data['slug'],
                $article_data['content'],
                $article_data['excerpt'],
                $article_data['image_url'],
                $article_data['source_url'],
                $article_data['author'],
                $article_data['category_id'],
                $article_data['rss_source_id'],
                $article_data['published_at']
            ]);
            
        } catch (Exception $e) {
            error_log("Save Article Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * استخراج الصورة من المحتوى
     */
    private function extractImageFromContent($content) {
        preg_match('/<img[^>]+src="([^"]+)"/', $content, $matches);
        return isset($matches[1]) ? $matches[1] : null;
    }
    
    /**
     * تنظيف المحتوى
     */
    private function cleanContent($content) {
        // إزالة HTML tags غير المرغوب فيها
        $content = strip_tags($content, '<p><br><strong><em><ul><ol><li><a>');
        return trim($content);
    }
    
    /**
     * إنشاء مقتطف
     */
    private function generateExcerpt($content, $length = 200) {
        $text = strip_tags($content);
        if (strlen($text) <= $length) {
            return $text;
        }
        return substr($text, 0, $length) . '...';
    }
    
    /**
     * تحويل التاريخ
     */
    private function parseDate($date_string) {
        $timestamp = strtotime($date_string);
        return $timestamp ? date('Y-m-d H:i:s', $timestamp) : date('Y-m-d H:i:s');
    }
    
    /**
     * إنشاء slug
     */
    private function generateSlug($title) {
        require_once dirname(__DIR__) . '/includes/functions.php';
        return createUniqueSlug($title, 'articles');
    }
    
    /**
     * تحديث وقت آخر جلب
     */
    private function updateLastFetched($rss_source_id) {
        $stmt = $this->db->prepare("UPDATE rss_sources SET last_fetched = NOW() WHERE id = ?");
        $stmt->execute([$rss_source_id]);
    }
    
    /**
     * جلب جميع مصادر RSS النشطة
     */
    public function fetchAllActiveFeeds() {
        $stmt = $this->db->prepare("SELECT id FROM rss_sources WHERE is_active = 1");
        $stmt->execute();
        $sources = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $total_articles = 0;
        foreach ($sources as $source_id) {
            $articles_added = $this->fetchRSSFeed($source_id);
            if ($articles_added !== false) {
                $total_articles += $articles_added;
            }
        }
        
        return $total_articles;
    }
}
?>
