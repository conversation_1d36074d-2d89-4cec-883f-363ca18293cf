<?php
/**
 * Automatic 500 Error Fix Tool
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

$action = $_GET['action'] ?? '';
$results = [];

function fixResult($message, $success = true) {
    global $results;
    $results[] = [
        'message' => $message,
        'success' => $success
    ];
}

switch ($action) {
    case 'fix_includes':
        // Fix include paths in main files
        $files_to_fix = [
            'index.php',
            'article.php',
            'category.php',
            'search.php'
        ];
        
        foreach ($files_to_fix as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                $original = $content;
                
                // Fix ROOT_PATH definition
                if (!preg_match('/define\s*\(\s*[\'"]ROOT_PATH[\'"]/', $content)) {
                    $content = "<?php\n// Define ROOT_PATH\nif (!defined('ROOT_PATH')) {\n    define('ROOT_PATH', __DIR__);\n}\n\n" . ltrim($content, "<?php\n");
                }
                
                // Fix config includes
                $content = preg_replace(
                    '/require_once\s+[\'"]config\/config\.php[\'"];?/',
                    "require_once ROOT_PATH . '/config/config.php';",
                    $content
                );
                
                $content = preg_replace(
                    '/require_once\s+[\'"]includes\/functions\.php[\'"];?/',
                    "require_once ROOT_PATH . '/includes/functions.php';",
                    $content
                );
                
                if ($content !== $original) {
                    file_put_contents($file, $content);
                    fixResult("Fixed includes in $file");
                } else {
                    fixResult("No changes needed in $file");
                }
            } else {
                fixResult("File $file not found", false);
            }
        }
        break;
        
    case 'fix_config':
        // Create a safe config file
        $config_content = '<?php
// Safe configuration file
if (!defined("ROOT_PATH")) {
    define("ROOT_PATH", dirname(__DIR__));
}

// Basic settings
define("SITE_URL", "http://localhost/amr");
define("DEBUG_MODE", true);

// Database settings (update these)
define("DB_HOST", "localhost");
define("DB_NAME", "news_website");
define("DB_USER", "root");
define("DB_PASS", "");

// Enable error reporting in debug mode
if (defined("DEBUG_MODE") && DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set("display_errors", 1);
} else {
    error_reporting(0);
    ini_set("display_errors", 0);
}

// Set timezone
date_default_timezone_set("Asia/Riyadh");
';
        
        if (!is_dir('config')) {
            mkdir('config', 0755, true);
        }
        
        file_put_contents('config/config.php', $config_content);
        fixResult("Created safe config file");
        break;
        
    case 'fix_database':
        // Create a minimal database class
        $db_content = '<?php
class Database {
    private $host = "localhost";
    private $db_name = "news_website";
    private $username = "root";
    private $password = "";
    private $conn;

    public function connect() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8")
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $e) {
            // Log error instead of displaying
            error_log("Database Connection Error: " . $e->getMessage());
            return false;
        }
        
        return $this->conn;
    }
}
';
        
        if (!is_dir('config')) {
            mkdir('config', 0755, true);
        }
        
        file_put_contents('config/database.php', $db_content);
        fixResult("Created safe database class");
        break;
        
    case 'fix_functions':
        // Create minimal functions file
        $functions_content = '<?php
// Minimal functions file

// Get setting function (safe)
function getSetting($key, $default = "") {
    // Return default values for now
    $settings = [
        "site_name" => "موقع الأخبار",
        "site_description" => "موقع إخباري شامل",
        "articles_per_page" => "12"
    ];
    
    return isset($settings[$key]) ? $settings[$key] : $default;
}

// Get articles function (safe)
function getArticles($page = 1, $per_page = 12, $category_id = null, $search = null) {
    // Return empty array for now
    return [];
}

// Get total articles function (safe)
function getTotalArticles($category_id = null, $search = null) {
    return 0;
}

// Get categories function (safe)
function getCategories() {
    return [];
}

// Get featured articles function (safe)
function getFeaturedArticles($limit = 5) {
    return [];
}

// Get latest articles function (safe)
function getLatestArticles($limit = 10, $exclude_id = null) {
    return [];
}

// Format date function
function formatDate($date, $format = "Y-m-d H:i") {
    return date($format, strtotime($date));
}

// Format Arabic date function
function formatArabicDate($date) {
    return formatDate($date, "Y-m-d H:i");
}

// Generate pagination function (safe)
function generatePagination($current_page, $total_pages, $base_url) {
    return "";
}

// Sanitize input function
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, "UTF-8");
}
';
        
        if (!is_dir('includes')) {
            mkdir('includes', 0755, true);
        }
        
        file_put_contents('includes/functions.php', $functions_content);
        fixResult("Created safe functions file");
        break;
        
    case 'create_simple_index':
        // Create a simple working index.php
        $simple_index = '<?php
// Simple working index.php
error_reporting(E_ALL);
ini_set("display_errors", 1);

// Define ROOT_PATH
if (!defined("ROOT_PATH")) {
    define("ROOT_PATH", __DIR__);
}

// Start session
session_start();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>موقع الأخبار</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>body { font-family: "Tajawal", Arial, sans-serif; }</style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
            <h1 class="text-2xl font-bold text-gray-800 mb-4">موقع الأخبار</h1>
            <p class="text-gray-600 mb-6">الموقع يعمل بنجاح!</p>
            
            <div class="space-y-3">
                <a href="setup.php" class="block w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                    إعداد قاعدة البيانات
                </a>
                <a href="admin/login.php" class="block w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">
                    لوحة التحكم
                </a>
                <a href="debug-500.php" class="block w-full bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700">
                    أدوات التشخيص
                </a>
            </div>
            
            <div class="mt-6 text-sm text-gray-500">
                <p>PHP Version: <?php echo PHP_VERSION; ?></p>
                <p>Server: <?php echo $_SERVER["SERVER_SOFTWARE"] ?? "Unknown"; ?></p>
            </div>
        </div>
    </div>
</body>
</html>';
        
        file_put_contents('index.php', $simple_index);
        fixResult("Created simple working index.php");
        break;
        
    case 'fix_permissions':
        // Fix file permissions (Linux/Mac only)
        if (PHP_OS_FAMILY !== 'Windows') {
            $commands = [
                'find . -type f -name "*.php" -exec chmod 644 {} \;',
                'find . -type d -exec chmod 755 {} \;',
                'chmod 755 .',
                'chmod -R 755 config/ includes/ admin/ classes/'
            ];
            
            foreach ($commands as $cmd) {
                exec($cmd . ' 2>&1', $output, $return_code);
                if ($return_code === 0) {
                    fixResult("Executed: $cmd");
                } else {
                    fixResult("Failed: $cmd - " . implode(' ', $output), false);
                }
            }
        } else {
            fixResult("Permission fix not needed on Windows");
        }
        break;
        
    default:
        fixResult("No action specified", false);
}

// Display results
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 Error Fix Results</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold mb-4">Fix Results</h1>
            
            <?php if (!empty($results)): ?>
                <div class="space-y-2">
                    <?php foreach ($results as $result): ?>
                        <div class="p-3 rounded <?php echo $result['success'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                            <?php echo $result['success'] ? '✅' : '❌'; ?> <?php echo htmlspecialchars($result['message']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="bg-blue-100 text-blue-800 p-4 rounded">
                    <h2 class="font-semibold mb-2">Available Fixes:</h2>
                    <div class="space-y-2">
                        <a href="?action=create_simple_index" class="block bg-blue-600 text-white p-2 rounded hover:bg-blue-700">
                            Create Simple Working Index
                        </a>
                        <a href="?action=fix_config" class="block bg-green-600 text-white p-2 rounded hover:bg-green-700">
                            Fix Config File
                        </a>
                        <a href="?action=fix_database" class="block bg-purple-600 text-white p-2 rounded hover:bg-purple-700">
                            Fix Database Class
                        </a>
                        <a href="?action=fix_functions" class="block bg-orange-600 text-white p-2 rounded hover:bg-orange-700">
                            Fix Functions File
                        </a>
                        <a href="?action=fix_includes" class="block bg-red-600 text-white p-2 rounded hover:bg-red-700">
                            Fix Include Paths
                        </a>
                        <?php if (PHP_OS_FAMILY !== 'Windows'): ?>
                        <a href="?action=fix_permissions" class="block bg-gray-600 text-white p-2 rounded hover:bg-gray-700">
                            Fix File Permissions
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="mt-6 flex space-x-4">
                <a href="minimal-test.php" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Run Minimal Test
                </a>
                <a href="debug-500.php" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Full Diagnostic
                </a>
                <a href="index.php" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Test Homepage
                </a>
            </div>
        </div>
    </div>
</body>
</html>
