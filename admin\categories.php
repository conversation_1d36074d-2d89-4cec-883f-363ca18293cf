<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

checkAuth();

$page_title = 'إدارة التصنيفات';
$database = new Database();
$db = $database->connect();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$category_id = $_GET['id'] ?? null;
$edit_id = $_GET['edit'] ?? null;

$success_message = '';
$error_message = '';

// حذف تصنيف
if ($action === 'delete' && $category_id) {
    try {
        // التحقق من وجود مقالات في هذا التصنيف
        $stmt = $db->prepare("SELECT COUNT(*) FROM articles WHERE category_id = ?");
        $stmt->execute([$category_id]);
        $articles_count = $stmt->fetchColumn();
        
        if ($articles_count > 0) {
            $error_message = "لا يمكن حذف هذا التصنيف لأنه يحتوي على $articles_count مقال. يرجى نقل المقالات إلى تصنيف آخر أولاً.";
        } else {
            $stmt = $db->prepare("DELETE FROM categories WHERE id = ?");
            if ($stmt->execute([$category_id])) {
                $success_message = 'تم حذف التصنيف بنجاح';
            }
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حذف التصنيف';
    }
    $action = 'list';
}

// إضافة أو تعديل تصنيف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && in_array($action, ['add', 'edit'])) {
    $name = trim($_POST['name']);
    $description = trim($_POST['description']);
    
    if (!empty($name)) {
        $slug = createSlug($name);
        
        try {
            if ($action === 'add') {
                // التحقق من عدم وجود تصنيف بنفس الاسم
                $stmt = $db->prepare("SELECT COUNT(*) FROM categories WHERE name = ? OR slug = ?");
                $stmt->execute([$name, $slug]);
                if ($stmt->fetchColumn() > 0) {
                    $error_message = 'يوجد تصنيف بهذا الاسم بالفعل';
                } else {
                    $stmt = $db->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
                    if ($stmt->execute([$name, $slug, $description])) {
                        $success_message = 'تم إضافة التصنيف بنجاح';
                        $action = 'list';
                    }
                }
            } elseif ($action === 'edit' && $edit_id) {
                // التحقق من عدم وجود تصنيف آخر بنفس الاسم
                $stmt = $db->prepare("SELECT COUNT(*) FROM categories WHERE (name = ? OR slug = ?) AND id != ?");
                $stmt->execute([$name, $slug, $edit_id]);
                if ($stmt->fetchColumn() > 0) {
                    $error_message = 'يوجد تصنيف آخر بهذا الاسم بالفعل';
                } else {
                    $stmt = $db->prepare("UPDATE categories SET name = ?, slug = ?, description = ? WHERE id = ?");
                    if ($stmt->execute([$name, $slug, $description, $edit_id])) {
                        $success_message = 'تم تحديث التصنيف بنجاح';
                        $action = 'list';
                    }
                }
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء حفظ التصنيف: ' . $e->getMessage();
        }
    } else {
        $error_message = 'يرجى إدخال اسم التصنيف';
    }
}

// الحصول على بيانات التصنيف للتعديل
$category_data = null;
if (($action === 'edit' && $edit_id) || ($action === 'add' && $edit_id)) {
    $stmt = $db->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$edit_id]);
    $category_data = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($category_data) {
        $action = 'edit';
    }
}

// الحصول على التصنيفات مع عدد المقالات
$stmt = $db->query("
    SELECT c.*, COUNT(a.id) as articles_count 
    FROM categories c 
    LEFT JOIN articles a ON c.id = a.category_id 
    GROUP BY c.id 
    ORDER BY c.name
");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800">إدارة التصنيفات</h1>
        <?php if ($action === 'list'): ?>
        <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <i class="fas fa-plus ml-2"></i>إضافة تصنيف جديد
        </a>
        <?php else: ?>
        <a href="?" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            <i class="fas fa-arrow-right ml-2"></i>العودة للقائمة
        </a>
        <?php endif; ?>
    </div>

    <!-- Messages -->
    <?php if ($success_message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Categories List -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold">قائمة التصنيفات</h2>
        </div>

        <?php if (!empty($categories)): ?>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم التصنيف</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد المقالات</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($categories as $category): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-folder text-blue-600"></i>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($category['name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($category['slug']); ?></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900">
                                <?php echo $category['description'] ? htmlspecialchars(substr($category['description'], 0, 100)) . (strlen($category['description']) > 100 ? '...' : '') : 'لا يوجد وصف'; ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                <?php echo number_format($category['articles_count']); ?> مقال
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo formatArabicDate($category['created_at']); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="?edit=<?php echo $category['id']; ?>" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit ml-1"></i>تعديل
                                </a>
                                <a href="../category.php?slug=<?php echo $category['slug']; ?>" target="_blank" class="text-green-600 hover:text-green-900">
                                    <i class="fas fa-eye ml-1"></i>عرض
                                </a>
                                <?php if ($category['articles_count'] == 0): ?>
                                <a href="?action=delete&id=<?php echo $category['id']; ?>" 
                                   onclick="return confirm('هل أنت متأكد من حذف هذا التصنيف؟')" 
                                   class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash ml-1"></i>حذف
                                </a>
                                <?php else: ?>
                                <span class="text-gray-400 cursor-not-allowed" title="لا يمكن حذف تصنيف يحتوي على مقالات">
                                    <i class="fas fa-trash ml-1"></i>حذف
                                </span>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-12">
            <i class="fas fa-folder-open text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد تصنيفات</h3>
            <p class="text-gray-500 mb-4">ابدأ بإضافة تصنيف جديد لتنظيم مقالاتك</p>
            <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                إضافة تصنيف جديد
            </a>
        </div>
        <?php endif; ?>
    </div>

    <?php else: ?>
    <!-- Add/Edit Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold mb-6">
            <?php echo $action === 'add' ? 'إضافة تصنيف جديد' : 'تعديل التصنيف'; ?>
        </h2>

        <form method="POST" class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">اسم التصنيف *</label>
                <input type="text" name="name" required 
                       value="<?php echo $category_data['name'] ?? ''; ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="مثال: تقنية، رياضة، سياسة">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">وصف التصنيف</label>
                <textarea name="description" rows="4" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="وصف مختصر للتصنيف (اختياري)"><?php echo $category_data['description'] ?? ''; ?></textarea>
            </div>

            <div class="flex justify-end space-x-3 space-x-reverse">
                <a href="?" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    إلغاء
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <?php echo $action === 'add' ? 'إضافة التصنيف' : 'تحديث التصنيف'; ?>
                </button>
            </div>
        </form>
    </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
