<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

session_start();

$page_title = 'اختبار شامل لجميع الميزات';

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-4xl font-bold text-center mb-8 text-gray-800">
            <i class="fas fa-rocket ml-2 text-blue-600"></i>
            اختبار شامل لجميع ميزات الموقع
        </h1>

        <!-- نظرة عامة -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-blue-800">
                <i class="fas fa-star ml-2"></i>الميزات المتاحة
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- شريط الأخبار العاجلة -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-broadcast-tower text-2xl text-red-600 ml-3"></i>
                        <h3 class="font-bold text-lg">شريط الأخبار العاجلة</h3>
                    </div>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ حركة سلسة من اليمين لليسار</li>
                        <li>✅ إيقاف/تشغيل وإغلاق</li>
                        <li>✅ تحديث تلقائي كل 5 دقائق</li>
                        <li>✅ تصميم متجاوب</li>
                    </ul>
                    <a href="test-breaking-news.php" class="inline-block mt-3 bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors">
                        اختبار مفصل
                    </a>
                </div>

                <!-- تحسينات الصفحة الرئيسية -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-magic text-2xl text-purple-600 ml-3"></i>
                        <h3 class="font-bold text-lg">تحسينات الصفحة الرئيسية</h3>
                    </div>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ Smooth Scrolling</li>
                        <li>✅ Scroll to Top Button</li>
                        <li>✅ Infinite Scrolling</li>
                        <li>✅ Scroll Animations</li>
                        <li>✅ Sticky Navigation</li>
                        <li>✅ Progress Bar</li>
                    </ul>
                    <a href="test-homepage-enhancements.php" class="inline-block mt-3 bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700 transition-colors">
                        اختبار مفصل
                    </a>
                </div>

                <!-- نظام إدارة المحتوى -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-cogs text-2xl text-green-600 ml-3"></i>
                        <h3 class="font-bold text-lg">نظام إدارة المحتوى</h3>
                    </div>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ إدارة المقالات والتصنيفات</li>
                        <li>✅ نظام المستخدمين والصلاحيات</li>
                        <li>✅ رفع الصور والملفات</li>
                        <li>✅ إعدادات الموقع</li>
                    </ul>
                    <a href="admin/dashboard.php" class="inline-block mt-3 bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700 transition-colors">
                        لوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-yellow-800">
                <i class="fas fa-bolt ml-2"></i>اختبارات سريعة
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-3">اختبار شريط الأخبار العاجلة</h3>
                    <div class="space-y-2">
                        <button onclick="testBreakingNews()" class="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors">
                            اختبار الإيقاف/التشغيل
                        </button>
                        <button onclick="testBreakingNewsHover()" class="w-full bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors">
                            اختبار Hover
                        </button>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg">
                    <h3 class="font-semibold text-gray-800 mb-3">اختبار تحسينات الصفحة</h3>
                    <div class="space-y-2">
                        <button onclick="testScrollToTop()" class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                            اختبار العودة لأعلى
                        </button>
                        <button onclick="testProgressBar()" class="w-full bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition-colors">
                            اختبار شريط التقدم
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات النظام -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-green-800">
                <i class="fas fa-chart-bar ml-2"></i>إحصائيات النظام
            </h2>
            
            <?php
            $database = new Database();
            $db = $database->connect();
            
            $total_articles = $db->query("SELECT COUNT(*) FROM articles")->fetchColumn();
            $featured_articles = $db->query("SELECT COUNT(*) FROM articles WHERE is_featured = 1")->fetchColumn();
            $categories_count = $db->query("SELECT COUNT(*) FROM categories")->fetchColumn();
            $total_views = $db->query("SELECT SUM(views) FROM articles")->fetchColumn();
            ?>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-3xl font-bold text-blue-600"><?php echo number_format($total_articles); ?></div>
                    <div class="text-sm text-gray-600">إجمالي المقالات</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-3xl font-bold text-green-600"><?php echo number_format($featured_articles); ?></div>
                    <div class="text-sm text-gray-600">مقالات مميزة</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-3xl font-bold text-purple-600"><?php echo number_format($categories_count); ?></div>
                    <div class="text-sm text-gray-600">التصنيفات</div>
                </div>
                
                <div class="bg-white p-4 rounded-lg text-center">
                    <div class="text-3xl font-bold text-orange-600"><?php echo number_format($total_views); ?></div>
                    <div class="text-sm text-gray-600">إجمالي المشاهدات</div>
                </div>
            </div>
        </div>

        <!-- حالة الميزات -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-check-circle ml-2"></i>حالة الميزات
            </h2>
            
            <div id="features-status" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>

        <!-- أدوات المطور -->
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-indigo-800">
                <i class="fas fa-code ml-2"></i>أدوات المطور
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="showConsoleInfo()" class="bg-indigo-600 text-white px-4 py-3 rounded hover:bg-indigo-700 transition-colors">
                    <i class="fas fa-terminal ml-2"></i>معلومات Console
                </button>
                
                <button onclick="testPerformance()" class="bg-purple-600 text-white px-4 py-3 rounded hover:bg-purple-700 transition-colors">
                    <i class="fas fa-tachometer-alt ml-2"></i>اختبار الأداء
                </button>
                
                <button onclick="exportLogs()" class="bg-gray-600 text-white px-4 py-3 rounded hover:bg-gray-700 transition-colors">
                    <i class="fas fa-download ml-2"></i>تصدير السجلات
                </button>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h2 class="text-2xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-link ml-2"></i>روابط سريعة
            </h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="index.php" class="bg-blue-600 text-white px-4 py-3 rounded text-center hover:bg-blue-700 transition-colors">
                    <i class="fas fa-home block mb-1"></i>الصفحة الرئيسية
                </a>
                
                <a href="admin/dashboard.php" class="bg-green-600 text-white px-4 py-3 rounded text-center hover:bg-green-700 transition-colors">
                    <i class="fas fa-tachometer-alt block mb-1"></i>لوحة التحكم
                </a>
                
                <a href="add-sample-news.php" class="bg-yellow-600 text-white px-4 py-3 rounded text-center hover:bg-yellow-700 transition-colors">
                    <i class="fas fa-plus block mb-1"></i>إضافة مقالات تجريبية
                </a>
                
                <a href="HOMEPAGE_ENHANCEMENTS_GUIDE.md" class="bg-purple-600 text-white px-4 py-3 rounded text-center hover:bg-purple-700 transition-colors">
                    <i class="fas fa-book block mb-1"></i>دليل التحسينات
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// اختبار شريط الأخبار العاجلة
function testBreakingNews() {
    const pauseBtn = document.getElementById('ticker-pause');
    if (pauseBtn) {
        pauseBtn.click();
        showNotification('تم اختبار زر الإيقاف/التشغيل', 'success');
    } else {
        showNotification('شريط الأخبار غير متاح', 'error');
    }
}

function testBreakingNewsHover() {
    const marquee = document.getElementById('news-marquee');
    if (marquee) {
        marquee.dispatchEvent(new Event('mouseenter'));
        setTimeout(() => {
            marquee.dispatchEvent(new Event('mouseleave'));
        }, 2000);
        showNotification('تم اختبار تأثير Hover', 'success');
    } else {
        showNotification('شريط الأخبار غير متاح', 'error');
    }
}

// اختبار تحسينات الصفحة
function testScrollToTop() {
    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    setTimeout(() => {
        showNotification('الآن اضغط على زر العودة لأعلى', 'info');
    }, 1000);
}

function testProgressBar() {
    const progressBar = document.getElementById('scroll-progress');
    if (progressBar) {
        showNotification('شريط التقدم يعمل! لاحظ الشريط الأزرق في أعلى الصفحة', 'success');
    } else {
        showNotification('شريط التقدم غير متاح في هذه الصفحة', 'warning');
    }
}

// أدوات المطور
function showConsoleInfo() {
    console.group('🚀 معلومات النظام');
    console.log('📊 Homepage Enhancements:', window.homepageEnhancements);
    console.log('📡 Breaking News Ticker:', window.breakingNewsTicker);
    console.log('🌐 User Agent:', navigator.userAgent);
    console.log('📱 Screen Size:', `${screen.width}x${screen.height}`);
    console.log('🔧 Viewport Size:', `${window.innerWidth}x${window.innerHeight}`);
    console.groupEnd();
    
    showNotification('تم عرض المعلومات في Console', 'info');
}

function testPerformance() {
    const start = performance.now();
    
    // محاكاة عملية معقدة
    for (let i = 0; i < 100000; i++) {
        Math.random();
    }
    
    const end = performance.now();
    const duration = end - start;
    
    console.log(`⚡ Performance Test: ${duration.toFixed(2)}ms`);
    showNotification(`اختبار الأداء: ${duration.toFixed(2)}ms`, 'info');
}

function exportLogs() {
    const logs = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        viewport: `${window.innerWidth}x${window.innerHeight}`,
        features: window.homepageEnhancements?.getEnabledFeatures() || [],
        performance: {
            memory: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : 'غير متاح'
        }
    };
    
    const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-logs-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    showNotification('تم تصدير السجلات', 'success');
}

// عرض حالة الميزات
function updateFeaturesStatus() {
    const statusContainer = document.getElementById('features-status');
    const features = [
        { name: 'شريط الأخبار العاجلة', id: 'breaking-news-ticker', icon: 'fas fa-broadcast-tower' },
        { name: 'زر العودة لأعلى', id: 'scroll-to-top', icon: 'fas fa-arrow-up' },
        { name: 'شريط التقدم', id: 'scroll-progress', icon: 'fas fa-chart-line' },
        { name: 'التنقل المثبت', id: 'sticky-nav', icon: 'fas fa-thumbtack' },
        { name: 'تحسينات الصفحة الرئيسية', check: () => window.homepageEnhancements, icon: 'fas fa-magic' },
        { name: 'شريط الأخبار المتقدم', check: () => window.breakingNewsTicker, icon: 'fas fa-newspaper' }
    ];
    
    statusContainer.innerHTML = features.map(feature => {
        const isActive = feature.id ? 
            document.getElementById(feature.id) !== null : 
            feature.check ? feature.check() : false;
        
        const statusClass = isActive ? 'text-green-600' : 'text-red-600';
        const statusIcon = isActive ? 'fas fa-check-circle' : 'fas fa-times-circle';
        const statusText = isActive ? 'نشط' : 'غير نشط';
        
        return `
            <div class="bg-white p-3 rounded-lg flex items-center justify-between">
                <div class="flex items-center">
                    <i class="${feature.icon} text-gray-600 ml-2"></i>
                    <span class="font-medium">${feature.name}</span>
                </div>
                <div class="flex items-center ${statusClass}">
                    <i class="${statusIcon} ml-1"></i>
                    <span class="text-sm">${statusText}</span>
                </div>
            </div>
        `;
    }).join('');
}

// نظام الإشعارات
function showNotification(message, type = 'info') {
    const colors = {
        success: 'bg-green-500',
        error: 'bg-red-500',
        warning: 'bg-yellow-500',
        info: 'bg-blue-500'
    };
    
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// تشغيل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateFeaturesStatus();
    
    // تحديث حالة الميزات كل 5 ثوان
    setInterval(updateFeaturesStatus, 5000);
    
    showNotification('تم تحميل صفحة الاختبار الشامل بنجاح!', 'success');
});
</script>

<?php include 'includes/footer.php'; ?>
